import { LeftArrowBackIcon } from "@/utils/icons";
import { useEffect, useState } from "react";

const Priority = ({
  setSelectedMenu,
  selectedPriority,
  setSelectedPriority,
}) => {
  const [tempSelected, setTempSelected] = useState([]);

  const list = [
    { label: "Low", value: 1 },
    { label: "Medium", value: 2 },
    { label: "High", value: 3 },
  ];

  const handleSelect = (value) => {
    setTempSelected((prev) => {
      if (prev.includes(value)) {
        return prev.filter((item) => item !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  useEffect(() => {
    setTempSelected(selectedPriority);
  }, [selectedPriority]);

  return (
    <>
      <div className="tw-flex tw-justify-between tw-items-center">
        <button
          type="button"
          onClick={() => {
            setSelectedMenu(null);
          }}
          className=""
        >
          <LeftArrowBackIcon />
        </button>
        <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
          Priority
        </p>
        <button
          type="button"
          className="tw-text-primary-purple tw-font-bold"
          onClick={() => {
            setSelectedPriority(tempSelected);
            setSelectedMenu(null);
          }}
        >
          Done
        </button>
      </div>

      <div className="tw-my-2">
        {list.map((ele) => (
          <button
            type="button"
            className="tw-my-5 tw-w-full tw-flex tw-justify-between tw-items-center"
            key={ele.value}
            onClick={() => handleSelect(ele.value)}
          >
            <p
              className={`tw-text-xl tw-text-primary-black tw-font-medium ${
                tempSelected.includes(ele.value) && "!tw-text-primary-purple"
              }`}
            >
              {ele.label}
            </p>
            <div>
              <input
                className="tw-scale-150"
                type="checkbox"
                checked={tempSelected.includes(ele.value)}
                readOnly
              />
            </div>
          </button>
        ))}
      </div>
    </>
  );
};

export default Priority;
