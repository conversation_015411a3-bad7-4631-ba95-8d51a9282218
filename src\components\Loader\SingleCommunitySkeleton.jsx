import { CustomContainer } from "../Common/Custom-Display";
import { Skeleton } from "../ui/skeleton";
import ExplorePostSkeleton from "./ExplorePostSkeleton";

const SingleCommunitySkeleton = ({ followerCount = 5 }) => {
  return (
    <div className="tw-mt-4">
      <Skeleton className="tw-relative tw-w-full tw-h-[21rem] !tw-rounded-3xl" />

      <div className="tw-flex tw-mt-6 tw-justify-between tw-items-center">
        <Skeleton className=" !tw-rounded-xl tw-h-8 tw-w-[28rem]" />
        <Skeleton
          className={`tw-py-7 tw-cursor-pointer !tw-rounded-full tw-px-16`}
        />
      </div>
      <div className="tw-flex tw-items-center tw-gap-5">
        <div className="tw-flex tw-items-center  tw-space-x-[-7px] ">
          {Array.from({ length: followerCount }, (_, i) => i + 1)?.map(
            (data, index) => (
              <Skeleton
                key={index}
                className="!tw-rounded-full tw-border-2 tw-border-white tw-overflow-hidden tw-w-10 tw-h-10 tw-shadow-lg tw-relative"
              />
            )
          )}
        </div>
        <div>
          <Skeleton className=" tw-h-3 tw-mb-1 tw-w-[5rem]" />
          <Skeleton className=" tw-h-4 tw-w-[5rem]" />
        </div>
      </div>

      {/* Tabs */}
      <div className="tw-flex tw-justify-between tw-items-center tw-my-3">
        <Skeleton className="tw-h-5 !tw-rounded-lg tw-w-[10rem] " />
        <Skeleton
          className={`tw-py-6 tw-cursor-pointer !tw-rounded-full tw-px-14`}
        />
      </div>

      <ExplorePostSkeleton count={3} />
    </div>
  );
};

export default SingleCommunitySkeleton;
