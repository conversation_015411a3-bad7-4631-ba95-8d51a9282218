import Image from "next/image";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import { useState } from "react";
import StoriesPreview from "./StoriesPreview";
import UserAvatar from "../Common/UserAvatar";

const Stories = ({ storyList = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const handleStoryClick = (index) => {
    setCurrentUserIndex(index);
    setIsOpen(true);
  };
  // console.log(storyList);
  return (
    <>
      <StoriesPreview
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        currentUserIndex={currentUserIndex}
        setCurrentUserIndex={setCurrentUserIndex}
        storyData={storyList}
      />
      <div className="tw-w-full tw-overflow-hidden tw-block">
        <Carousel
          opts={{
            align: "start",
            draggable: true, // Enable dragging
            speed: 8, // Lower value = smoother
            loop: false, // Enable infinite loop if needed
            startIndex: 0, // Ensure it starts at the first slide
            containScroll: "trimSnaps", // Keeps items inside the viewport
            skipSnaps: true,
          }}
          className=""
        >
          <CarouselContent>
            {storyList?.map((story, i) => (
              <CarouselItem
                className="!tw-flex !tw-flex-col !tw-gap-1.5 !tw-items-center"
                key={`${story.id}`}
              >
                <button
                  key={`${story.id}`}
                  type="button"
                  className=""
                  onClick={() => handleStoryClick(i)}
                >
                  <div className="tw-relative  tw-border-[#CCCCCC] tw-border-4 tw-p-1  tw-flex-shrink-0 tw-rounded-full tw-overflow-hidden">
                    <UserAvatar
                      imageUrl={story.image}
                      imageParentClassName={`!tw-w-20 !tw-h-20`}
                      userName={story?.firstName}
                      userNameClassName={`!tw-text-5xl`}
                    />
                  </div>
                  <p className="tw-text-primary-black tw-font-medium">
                    {story?.firstName}
                  </p>
                </button>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </>
  );
};

export default Stories;
