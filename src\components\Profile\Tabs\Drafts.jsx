"use client";
import Empty from "@/components/Common/Empty";
import { useContext, useEffect, useState } from "react";
import DraftSkeleton from "@/components/Loader/DraftSkeleton";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import DraftsCard from "@/components/ProjectPage/SingleProject/Tabs/DraftsComponent/DraftsCard";
import { getAllPost } from "@/app/action";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import authStorage from "@/utils/API/AuthStorage";
import { useProfile } from "@/components/context/ProfileContext";
import { valContext } from "@/components/context/ValContext";

const Drafts = () => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [userData, setUserData] = useState(null);
  const api = useApiRequest();
  const [refresh, setRefresh] = useState(false);
  const { profile } = useProfile();

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };
  const { loginUserData } = useContext(valContext);

  const fetchData = async (userId) => {
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      UserId: +userId,
      isPublished: 0,
    };

    if (Object.keys(query)?.length > 0) {
      query.sortOrder = "DESC";
    }

    try {
      api.sendRequest(
        getAllPost,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [...prev, ...res?.data?.data]);
            // setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setDataList(() => [...res?.data?.data]);
            // setLoadOff(false);
          } else {
            // setLoadOff(true);
          }
        },

        { ...query }
      );
    } catch (error) {
      console.error("Error fetching blog data:", error);
      // setScrollLoader(false);
    } finally {
      // setScrollLoader(false);
    }
    // setScrollLoader(false);
  };

  useEffect(() => {
    if (profile?.id) {
      fetchData(profile?.id);
    }
  }, [pagination.page, profile, refresh]);
  return (
    <div className="tw-mb-20 lg:tw-mb-0">
      {dataList?.map((ele) => (
        <div className="tw-mb-8" key={ele?.id}>
          <DraftsCard
            resetDataList={resetDataList}
            setRefresh={setRefresh}
            userData={loginUserData}
            data={ele}
          />
        </div>
      ))}
      <InfiniteScroll
        threshold={90}
        loadMoreFunction={() => {
          if (
            Math.ceil(pagination.total / pagination.limit) > pagination.page
          ) {
            setPagination((prev) => ({
              ...prev,
              page: prev?.page + 1,
            }));
          }
        }}
        isLoading={api.isLoading}
        loadingComponent={<DraftSkeleton count={2} />}
        timeout={10}
        // loadOff={loadOff}
      />
      {!api.isLoading && dataList?.length === 0 && (
        <div className="tw-flex tw-justify-center tw-items-center tw-h-[25rem]">
          <Empty iconRequired={false} label="No drafts available!" />
        </div>
      )}
    </div>
  );
};

export default Drafts;
