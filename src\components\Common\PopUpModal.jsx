import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Trash2 } from "lucide-react";
import CustomButton from "./Custom-Button";
const PopUpModal = ({
  isOpen,
  setIsOpen,
  isLoading = false,
  icon = <Trash2 stroke="#EF3B41" size={40} />,
  contentClassName = "",
  iconBgClass = "tw-bg-[#f9dde0] tw-p-5 tw-rounded-full",
  mainMessage = "Delete Post",
  subMessage = "Are you sure you want to Delete Post Permanently?",
  onConfirm = () => {},
  onCancel = () => {},
  confirmButtonText = "Yes",
  cancelButtonText = "No",
  confirmButtonClass = "tw-outline-none tw-rounded-full !tw-text-base tw-border-none hover:!tw-bg-[#EF3B41] active:!tw-bg-[#EF3B41] !tw-bg-[#EF3B41] tw-px-6 !tw-py-3 tw-text-white tw-transition  hover:tw-transform hover:tw-scale-105",
  cancelButtonClass = "tw-outline-none tw-text-incenti-purple tw-px-6 tw-py-[10px] tw-border-incenti-purple tw-border tw-rounded-full tw-transition hover:tw-transform hover:tw-scale-105",
}) => {
  return (
    <>
      <Dialog
        open={typeof isOpen === "boolean" ? isOpen : isOpen !== null}
        onOpenChange={(value) => {
          //   console.log(value);
          if (!value && isLoading) return;
          if (!value) {
            if (typeof isOpen === "boolean") {
              setIsOpen(value);
            } else {
              setIsOpen(null);
            }
          }
        }}
      >
        <DialogContent
          hideCloseButton
          className={`!tw-w-[30rem]  !tw-p-10 !tw-rounded-3xl ${contentClassName}`}
        >
          <DialogHeader>
            <DialogTitle className="tw-opacity-0"></DialogTitle>
            <div className="tw-flex  tw-gap-5 tw-flex-col tw-items-center  ">
              {icon && <div className={iconBgClass}>{icon}</div>}
              <div className="tw-flex  tw-flex-col tw-items-center">
                <p className="tw-text-primary-black  tw-font-semibold tw-text-lg tw-text-center">
                  {mainMessage}
                </p>
                <p className="tw-mx-auto tw-text-[#787E89] tw-text-sm tw-max-w-[20rem] tw-text-center">
                  {subMessage}
                </p>
              </div>
              {/* Buttons */}
              <div className=" tw-w-full tw-flex tw-gap-5 tw-justify-center tw-items-center">
                <button
                  disabled={isLoading}
                  onClick={() => {
                    if (typeof isOpen === "boolean") {
                      setIsOpen(false);
                    } else {
                      setIsOpen(null);
                    }
                    onCancel();
                  }}
                  className={`${cancelButtonClass} ${
                    isLoading && "tw-cursor-not-allowed"
                  }`}
                >
                  {cancelButtonText}
                </button>
                <CustomButton
                  loading={isLoading}
                  disabled={isLoading}
                  count={8}
                  onClick={() => {
                    onConfirm();
                  }}
                  type="button"
                  className={`${confirmButtonClass} ${
                    isLoading && "tw-cursor-not-allowed"
                  }`}
                >
                  {confirmButtonText}
                </CustomButton>
              </div>
            </div>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PopUpModal;
