"use client";
import Image from "next/image";
// import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import { blurDataURL, moveCoverImageToFront } from "@/utils/function";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";

const PostImageCarousel = ({ imagArr, imageClass = null }) => {
  return (
    <div className="tw-relative">
      {/* React-responsive carousel */}
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
        className={`tw-relative tw-flex tw-justify-center lg:tw-justify-end tw-items-center lg:tw-aspect-[400/250]  2xl:tw-h-[179px] xl:tw-h-[197px] lg:tw-h-[173px] tw-h-full`}
      >
        <Carousel
          showThumbs={false}
          swipeable
          emulateTouch
          showArrows={false}
          showStatus={false}
          showIndicators={moveCoverImageToFront(imagArr)?.length > 1}
        >
          {moveCoverImageToFront(imagArr)?.map((ele, i) => (
            <div
              key={`${ele?.id}-${i}`}
              onClick={(e) => {
                e.stopPropagation();
              }}
              className={`tw-relative ${
                moveCoverImageToFront(imagArr).length > 1 && "tw-cursor-pointer"
              } tw-flex tw-justify-end tw-items-center tw-aspect-[410/250]  2xl:tw-h-[179px] xl:tw-h-[197px] lg:tw-h-[173px] md:tw-h-[163px] 423:tw-h-[228px] 375:tw-h-[195px] tw-h-[177px] 320:tw-h-[158px] 2xl:tw-col-span-4 xl:tw-col-span-5 lg:tw-col-span-4 md:tw-col-span-5 tw-col-span-12  ${imageClass}`}
            >
              <Image
                src={ele?.link}
                fill
                alt={"Toolplate Blog Image"}
                className="tw-rounded-[20px] tw-border-gray-color tw-overflow-hidden tw-object-cover tw-relative"
                title={"Toolplate Blog"}
                sizes="auto"
                blurDataURL={blurDataURL(400, 250)}
                placeholder="blur"
              />
            </div>
          ))}
        </Carousel>
      </div>
    </div>
  );
};

export default PostImageCarousel;
