"use client"
import React, { useEffect, useState } from 'react'

const useChangePlaceholder = (placeholderChange) => {
    const [currentPlaceholderIndex, setCurrentPlaceholderIndex] = useState(0);
    const [typedText, setTypedText] = useState("");
    useEffect(() => {
        let timeout;
        if (typedText === placeholderChange[currentPlaceholderIndex]) {
            timeout = setTimeout(() => {
                setTypedText("");
                setCurrentPlaceholderIndex(
                    (prevIndex) => (prevIndex + 1) % placeholderChange.length
                );
            }, 3000); // Delay before switching to the next placeholder
        } else {
            timeout = setTimeout(() => {
                setTypedText(
                    (prevText) =>
                        prevText +
                        placeholderChange[currentPlaceholderIndex][typedText.length]
                );
            }, 85); // Typing speed
        }

        return () => clearTimeout(timeout);
    }, [typedText, currentPlaceholderIndex, placeholderChange]);

    return { typedText }


}

export default useChangePlaceholder