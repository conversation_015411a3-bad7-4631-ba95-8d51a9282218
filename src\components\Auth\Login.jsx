"use client";
import Image from "next/image";
import React, { useContext, useEffect, useState } from "react";
import Marquee from "react-fast-marquee";

import {
  AppleIcon,
  GoogleIcon,
  HideEyeIcon,
  LinkedInIcon,
  SeeEyeIcon,
} from "@/utils/icons";
import LoginForm from "./LoginForm";
import ForgotForm from "./ForgotForm";
import VerifyOTP from "./VerifyOTP";
import ResetPassForm from "./ResetPassForm";
import SignupForm from "./SignupForm";
import { getToken } from "@/utils/function";
import authStorage from "@/utils/API/AuthStorage";
import LoginFormLeftLayout from "./LoginFormLeftLayout";
import { valContext } from "../context/ValContext";

const Login = ({ activeType = "login" }) => {
  const [activeForm, setActiveForm] = useState(activeType);
  const { setIsMobile } = useContext(valContext);

  useEffect(() => {
    const updateIsMobile = () => setIsMobile(window.innerWidth <= 1023);
    updateIsMobile(); // Initialize on mount
    window.addEventListener("resize", updateIsMobile);
    return () => window.removeEventListener("resize", updateIsMobile);
  }, []);

  return (
    <div className="tw-h-full lg:tw-min-h-screen  lg:tw-grid tw-grid-flow-col lg:tw-grid-cols-2 ">
      <LoginFormLeftLayout />
      {/* Right Side (Login Form) */}
      <div className="tw-h-full lg:tw-min-h-screen tw-pt-28 lg:tw-pt-10 tw-max-w-[30rem] lg:tw-max-w-full tw-mx-auto tw-flex tw-flex-col tw-justify-center tw-items-center tw-gap-4">
        {activeForm === "login" && <LoginForm setActiveForm={setActiveForm} />}
        {activeForm === "signup" && (
          <SignupForm setActiveForm={setActiveForm} />
        )}
        {activeForm === "forgot" && (
          <ForgotForm setActiveForm={setActiveForm} />
        )}
        {activeForm === "verifyOtp" && (
          <VerifyOTP setActiveForm={setActiveForm} />
        )}
        {activeForm === "resetPass" && (
          <ResetPassForm setActiveForm={setActiveForm} />
        )}
      </div>
    </div>
  );
};

export default Login;
