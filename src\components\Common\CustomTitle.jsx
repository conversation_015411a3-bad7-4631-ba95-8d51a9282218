import React from "react";
import { HeadingTags } from "./Custom-Display";

const CustomTitle = ({
  name = "",
  description = "",
  extraButton = null,
  type = 1,
  className = "",
  htag = 2,
  textClassName = "",
}) => {
  switch (type) {
    case 1:
      return (
        <div className={`tw-flex tw-justify-between ${className}`}>
          <div className="tw-flex tw-flex-col tw-gap-0">
            <HeadingTags
              htag={htag}
              className={`tw-text-incenti-20 md:tw-text-incenti-24 tw-font-bold tw-text-primary-black tw-line-clamp-2 ${textClassName}`}
            >
              {name}
            </HeadingTags>

            {!!description ? (
              <p className="tw-text-[#374151B2] tw-text-incenti-base tw-font-normal">
                {description}
              </p>
            ) : (
              ""
            )}
          </div>
          {extraButton}
        </div>
      );
    case 2:
      return (
        <div className={`tw-flex tw-justify-between ${className}`}>
          <div className="tw-flex tw-flex-col tw-gap-1">
            {!!description ? (
              <p className="tw-text-incenti-[17px] tw-font-bold tw-text-incenti-orange tw-tracking-widest tw-uppercase">
                {description}
              </p>
            ) : (
              ""
            )}
            {!!name ? (
              <HeadingTags
                htag={htag}
                className="tw-text-incenti-24 tw-font-bold -tw-mt-[7px]"
                // tw-text-incenti-20 sm:
              >
                {name}
              </HeadingTags>
            ) : (
              ""
            )}
          </div>
          {extraButton}
        </div>
      );
    case 3:
      return (
        <div className={`tw-flex tw-justify-between ${className}`}>
          <div className="tw-flex tw-flex-col tw-gap-0">
            <HeadingTags
              htag={htag}
              className="tw-text-incenti-20 sm:tw-text-incenti-24 tw-font-bold tw-text-incenti-orange"
            >
              {name}
            </HeadingTags>

            {!!description ? (
              <p className="tw-text-[#374151B2] tw-text-incenti-base tw-font-normal">
                {description}
              </p>
            ) : (
              ""
            )}
          </div>
          {extraButton}
        </div>
      );
    case 4:
      return (
        <div className={`tw-flex tw-justify-between ${className}`}>
          <div className="tw-flex tw-flex-col tw-gap-2">
            <HeadingTags
              htag={htag}
              className="tw-text-incenti-24 tw-font-semibold"
            >
              {name}
            </HeadingTags>

            {!!description && (
              <p className="tw-text-[#1F1F1F] tw-text-incenti-18 tw-font-normal">
                {description}
              </p>
            )}
          </div>
          {extraButton}
        </div>
      );
    case 5:
      return (
        <div className={`tw-flex tw-justify-between ${className}`}>
          <div className="tw-flex tw-flex-col tw-gap-2">
            <HeadingTags
              htag={htag}
              className="tw-text-incenti-20 tw-font-semibold"
            >
              {name}
            </HeadingTags>

            {!!description && (
              <p className="tw-text-[#1F1F1F] tw-text-incenti-18 tw-font-normal">
                {description}
              </p>
            )}
          </div>
          {extraButton}
        </div>
      );

    default:
      return (
        <div className={`tw-flex tw-justify-between ${className}`}>
          <div className="tw-flex tw-flex-col tw-w-full tw-gap-2">
            <HeadingTags
              htag={htag}
              className="tw-text-incenti-24 tw-font-semibold"
            >
              {name}
            </HeadingTags>

            {!!description && (
              <p className="tw-text-[#374151B2] tw-text-incenti-sm tw-font-normal">
                {description}
              </p>
            )}
          </div>
          {extraButton}
        </div>
      );
  }
};

export default CustomTitle;
