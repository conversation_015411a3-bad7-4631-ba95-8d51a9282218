import UserAvatar from "@/components/Common/UserAvatar";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { convertUTCtoLocal } from "@/utils/function";
import Image from "next/image";
import Link from "next/link";
const AboutModal = ({
  isOpen,
  setIsOpen,
  projectData,
  loginUser,
  extraText = null,
}) => {
  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="!tw-rounded-[1.75rem] !tw-p-10">
          <DialogHeader>
            <DialogTitle className="tw-text-center !tw-font-bold !tw-text-xl !tw-text-primary-black">
              About
            </DialogTitle>
            <p className="tw-text-center !tw-mt-1.5 tw-font-bold tw-text-3xl !tw-text-primary-black ">
              {projectData?.name}
            </p>
          </DialogHeader>
          <p className="tw-text-center !tw-text-lg tw-text-primary-black tw-break-all">
            {projectData?.description}
          </p>
          <Link
            href={
              loginUser?.id === projectData?.User?.id
                ? "/profile"
                : `/user/${projectData?.User?.slug}`
            }
            className="tw-flex tw-justify-center tw-gap-2 tw-items-center"
          >
            {/* <div className="tw-relative tw-w-[2.5rem] tw-h-[2.5rem]">
              <Image
                src={projectData?.User?.image}
                alt="user"
                fill
                className="!tw-rounded-full"
              />
            </div> */}
            <UserAvatar
              imageParentClassName="!tw-w-[2.5rem] !tw-h-[2.5rem]"
              imageUrl={projectData?.User?.image}
              userName={projectData?.User?.firstName}
              userNameClassName="!tw-text-xl"
            />
            <div>
              <p className="tw-font-bold !tw-text-sm">
                {`${projectData?.User?.firstName ?? ""} ${
                  projectData?.User?.lastName ?? ""
                }`}
              </p>
              <p className="tw-text-[#787e89] !tw-text-sm">
                Created on&nbsp;
                {convertUTCtoLocal(projectData?.createdAt, "MMM DD, YYYY")}
              </p>
            </div>
          </Link>

          {extraText && (
            <p className="tw-text-[#787e89] !tw-text-sm tw-mt-3 tw-text-center">
              {extraText}
            </p>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AboutModal;
