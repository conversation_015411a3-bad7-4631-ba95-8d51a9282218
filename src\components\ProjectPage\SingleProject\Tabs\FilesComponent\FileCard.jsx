import ShowMenuList from "@/components/Common/ShowMenuList";
import { getFileTypeIcon } from "@/utils/function";
import { DownloadIcon, MovePostIcon, ThreeDotMenuIcon } from "@/utils/icons";
import { FolderClosed, Trash2 } from "lucide-react";

const FileCard = ({
  ele,
  downloadHandler,
  deleteHandler,
  moveFileHandler = () => {},
  otherLabel,
  showAllSettings = false,
  onlyDownloadSettings = false,
}) => {
  // console.log(ele);
  const folderSetting = [
    {
      label: "Move file to",
      className: "",
      icon: <MovePostIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        moveFileHandler(ele);
      },
    },
    {
      label: "Download",
      className: "",
      icon: <DownloadIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        downloadHandler(ele);
      },
    },

    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        deleteHandler(ele);
      },
    },
  ];
  return (
    <>
      <div className="tw-relative tw-bg-[#F6F6F6] tw-px-4 tw-py-12 tw-rounded-3xl tw-flex tw-justify-center tw-items-center">
        {/* <FolderClosed size={80} /> */}
        {getFileTypeIcon(ele?.name)}
        {(showAllSettings || onlyDownloadSettings) && (
          <ShowMenuList
            data={ele}
            menuList={
              showAllSettings
                ? folderSetting
                : onlyDownloadSettings
                ? folderSetting.filter((ele) => ele.label === "Download")
                : []
            }
          >
            <button
              type="button"
              className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-[1rem]"
            >
              <div className="">
                <ThreeDotMenuIcon fill="#111" size={24} />
              </div>
            </button>
          </ShowMenuList>
        )}
      </div>
      <div className="tw-mt-2">
        <p className="tw-text-primary-black tw-text-xl tw-font-bold tw-break-all tw-line-clamp-2">
          {ele?.name}
        </p>

        {otherLabel && (
          <p className="tw-text-primary-black tw-text-sm">{otherLabel} </p>
        )}
      </div>
    </>
  );
};

export default FileCard;
