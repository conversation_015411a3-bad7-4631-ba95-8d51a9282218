"use client";
import {
  AppleIcon,
  GoogleIcon,
  HideEyeIcon,
  LinkedInIcon,
  SeeEyeIcon,
} from "@/utils/icons";
import { useEffect, useState } from "react";
import CustomTitle from "../Common/CustomTitle";
import CustomButton from "../Common/Custom-Button";
import GlobalForm from "../Common/Custom-Form";
import * as Yup from "yup";
import { getMyProfile, loginAPI } from "@/app/action";
import useApiRequest from "../helper/hook/useApiRequest";
import authStorage from "@/utils/API/AuthStorage";
// import { useRouter } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import SocialLogin from "./SocialLogin";
import Link from "next/link";
import toast from "react-hot-toast";
import { getToken } from "@/utils/function";
import FullScreenLoader from "../Loader/FullScreenLoader";
import { safeToast, setAllowToasts } from "@/utils/safeToast";
import { useProgress } from "@bprogress/next";
import logo from "../../../public/images/logo/logo-primary.svg";
import Image from "next/image";
import WebAppLogo from "../Common/WebAppLogo";

const LoginForm = ({ setActiveForm }) => {
  const api = useApiRequest();
  const router = useRouter();
  const [error, setError] = useState("");
  // const [changeDirection, setChangeDirection] = useState("left");
  const [isShowPass, setIsShowPass] = useState(false);
  const [loading, setLoading] = useState(false);
  const progress = useProgress();

  const onLoginSubmit = async (e, actions) => {
    setLoading(true);
    const payload = e;

    api.sendRequest(
      loginAPI,
      (res) => {
        // console.log(res)
        authStorage.setAuthDetails(res?.token);
        localStorage.removeItem("signup");
        safeToast.success(res?.message || "Successfully SignIn.");
        actions?.setSubmitting(false);
        progress.start(0, 1);
        router.push("/");
      },
      payload,
      "",
      (err) => {
        safeToast.error(err?.message);
        setError(err);
        setLoading(false);
      }
    );
  };

  useEffect(() => {
    setAllowToasts(true);
    if (getToken()?.token) {
      // console.log("here")
      authStorage.setAuthDetails(getToken()?.token);
      api.sendRequest(getMyProfile, (res) => {
        window.location.assign("/");
      });
      // window.location.assign("/")
    }
  }, []);
  return (
    <>
      {/* Loader */}
      {loading && <FullScreenLoader />}
      {/* Logo */}
      <WebAppLogo className="lg:tw-hidden" />
      <CustomTitle
        name="Welcome"
        className=" tw-text-primary-black tw-font-bold tw-text-center  sm:tw-text-left tw-my-4 lg:tw-m-0"
        textClassName="lg:!tw-text-incenti-24 !tw-text-3xl"
      />

      <GlobalForm
        // loading={loading}
        setLoading={setLoading}
        error={error}
        setError={setError}
        fields={[
          { name: "email", label: "Email", type: "email" },
          { name: "password", label: "Password", type: "password" },
          // { name: "file", label: "Upload File", type: "file" },
        ]}
        initialValues={{ email: "", password: "" }}
        validationSchema={{
          email: Yup.string()
            .trim()
            .email("Invalid email")
            .required("Email is required"),
          password: Yup.string().trim().required("Password is required"),
          // .min(8, "Password must be at least 8 characters long")
          // .matches(
          //     /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&._-])[^\s]*$/,
          //     "(A-Z, a-z, 0-9, @$!%*?&._-), at least 1 of each & no spaces."
          // ),
        }}
        // validationSchema={{
        // email: Yup.string().email().required("Email Required"),
        // password: Yup.string().min(6).required("Required"),
        // file: Yup.mixed().required("File is required"),
        // }}
        onSubmit={(e, actions) => {
          onLoginSubmit(e, actions);
        }}
        submitButtonText="Sign In"
        extraButton={
          <>
            <div className="tw-w-full tw-flex tw-justify-end lg:tw-justify-center tw-text-center tw-px-4 ">
              <Link
                href="/forgot-password"
                className="tw-text-sm tw-text-center tw-text-primary-black hover:tw-underline tw-cursor-pointer tw-py-2"
                onClick={() => setActiveForm("forgot")}
              >
                Forgot password?
              </Link>
            </div>
          </>
        }
      />

      {/* Divider */}
      <div className="tw-flex tw-items-center tw-w-[25rem]  tw-gap-2 sm:tw-gap-4 tw-my-4 sm:tw-my-2 tw-px-4 sm:tw-px-0">
        <div className="tw-flex-1 tw-h-px tw-bg-gray-300" />
        <span className="tw-text-xs sm:tw-text-sm tw-text-gray-500 tw-whitespace-nowrap tw-px-2">
          or continue with
        </span>
        <div className="tw-flex-1 tw-h-px tw-bg-gray-300" />
      </div>

      {/* Social Login Buttons */}
      <div className="tw-w-full tw-my-3 sm:tw-my-1.5 tw-px-4 sm:tw-px-0">
        <SocialLogin isRegister={false} />
      </div>

      {/* Sign Up Link */}
      <p className="tw-text-sm tw-text-primary-black tw-text-center sm:tw-text-left tw-px-4 sm:tw-px-0 tw-py-2">
        Don&apos;t have an account?{" "}
        <Link
          href={"/signup"}
          className="tw-text-primary-purple tw-font-bold hover:tw-underline tw-cursor-pointer"
        >
          Sign Up
        </Link>
        {/* <a href="#" className="tw-text-primary-purple tw-font-bold hover:tw-underline">
                    Sign Up
                </a> */}
      </p>
    </>
  );
};

export default LoginForm;
