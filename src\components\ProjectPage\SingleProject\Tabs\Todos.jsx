"use client";
import { deleteTodos, getTodos, updateTodoStatus } from "@/app/action";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import PopUpModal from "@/components/Common/PopUpModal";
import { useProject } from "@/components/context/ProjectContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import TodoSkeleton from "@/components/Loader/TodoSkeleton";
import TodoCard from "@/components/Todo/TodoCard";
import TodoModal from "@/components/Todo/TodoModal";
import authStorage from "@/utils/API/AuthStorage";
import { RESPONSE_STATUS } from "@/utils/function";
import { DotMenuIcon } from "@/utils/icons";
import { safeToast } from "@/utils/safeToast";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useCollapse } from "react-collapsed";
import toast from "react-hot-toast";

const Todos = () => {
  const [dataList, setDataList] = useState([]);
  const [isAddTodo, setIsAddTodo] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [editData, setEditData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [userData, setUserData] = useState(null);
  const api = useApiRequest();
  const deleteApi = useApiRequest(false);
  const { project } = useProject();
  const { getCollapseProps, getToggleProps, isExpanded } = useCollapse({
    duration: 500,
  });

  const completedTodo = useMemo(() => {
    return dataList?.filter((todo) => Boolean(todo?.status));
  }, [dataList]);
  const inCompletedTodo = useMemo(() => {
    return dataList?.filter((todo) => !Boolean(todo?.status));
  }, [dataList]);

  // Reset Data
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };
  const editHandler = (ele) => {
    setEditData(ele);
  };

  // Delete handler
  const deleteHandler = (ele) => {
    // console.log("delete", ele);
    setDeleteData(ele);
  };

  const updateTodoHandler = async (id) => {
    try {
      const response = await updateTodoStatus({
        id,
      });
      if (
        response?.status !== 200 &&
        response?.status !== RESPONSE_STATUS.SUCCESS
      ) {
        throw response;
      }

      safeToast.success(response?.message);
    } catch (error) {
      safeToast.error(error?.message);
      // safeToast.error("Something went wrong");
    }
  };

  const updateTodoState = (id) => {
    setDataList((prevList) => {
      const updatedItem = prevList.find((item) => item.id === id);
      if (!updatedItem) return prevList;

      const newList = prevList.filter((item) => item.id !== id);
      if (updatedItem?.status) {
        return [
          ...newList,
          {
            ...updatedItem,
            status: !updatedItem?.status,
          },
        ];
      } else {
        return [
          {
            ...updatedItem,
            status: !updatedItem?.status,
          },
          ...newList,
        ];
      }
    });
  };

  const fetchData = (projectId) => {
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      ProjectIds: +projectId,
    };

    const user = authStorage.getProfileDetails();

    setUserData(user);

    api.sendRequest(
      getTodos,
      (res) => {
        if (res?.data?.data?.length && pagination?.page > 1) {
          setDataList((prev) => [...prev, ...res?.data?.data]);
        } else if (res?.data?.data?.length && pagination.page === 1) {
          setDataList([...res?.data?.data]);
        } else {
        }
      },
      query
    );
  };
  useEffect(() => {
    if (project && Object.keys(project)) {
      if (project?.id) {
        fetchData(project?.id);
      }
    }
  }, [pagination.page, refresh, project]);
  return (
    <>
      {/* Delete Modal */}
      <PopUpModal
        isLoading={deleteApi.isLoading}
        isOpen={deleteData}
        setIsOpen={setDeleteData}
        mainMessage="Delete Todo"
        subMessage="Are you sure you want to Delete todo Permanently?"
        onConfirm={() => {
          deleteApi.sendRequest(
            deleteTodos,
            () => {
              resetDataList();
              setRefresh((prev) => !prev);
              setDeleteData(null);
            },
            deleteData?.id,

            "Todo Deleted Successfully"
          );
        }}
      />
      <TodoModal
        isOpen={isAddTodo}
        setIsOpen={setIsAddTodo}
        editData={editData}
        setEditData={setEditData}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
        defaultData={
          project?.ParentProject
            ? {
                subProjectId: project?.id,
                projectId: project?.ParentProject?.id,
              }
            : {
                projectId: project?.id,
              } ?? {}
        }
      />
      {/* Add Post */}
      {(project?.UserId === userData?.id ||
        project?.ProjectMembers?.[0]?.access === "write") && (
        <button
          type="button"
          onClick={() => {
            setIsAddTodo(true);
          }}
          className="tw-fixed tw-bottom-16 tw-right-6 lg:tw-bottom-auto lg:tw-absolute lg:-tw-top-[6rem] lg:tw-right-[0rem] tw-z-50 tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
        >
          <DotMenuIcon size={20} />
          <span className="tw-hidden lg:tw-inline">Create</span>
        </button>
      )}
      {dataList?.length === 0 && !api.isLoading && (
        <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No To Do available!" />
        </div>
      )}
      {api.isLoading && pagination.page === 1 ? (
        <TodoSkeleton />
      ) : (
        <div className=" tw-mb-20 lg:tw-mt-6  ">
          <div className=" ">
            {inCompletedTodo?.map((ele) => (
              <div
                // ref={(el) => {
                //   if (el) todoRefs.current[ele.id] = el;
                // }}
                key={ele?.id}
                className="tw-mb-3 sm:tw-mb-4"
              >
                <TodoCard
                  ele={ele}
                  updateTodoHandler={updateTodoHandler}
                  deleteHandler={deleteHandler}
                  editHandler={editHandler}
                  isShowAllSettings={
                    project?.UserId === userData?.id ||
                    (ele?.User?.id === userData?.id &&
                      project?.ProjectMembers?.[0]?.access === "write")
                  }
                  isOnlyEditSetting={
                    // ele?.User?.id === userData?.id &&
                    project?.ProjectMembers?.[0]?.access === "write"
                  }
                  updateSetState={updateTodoState}
                />
              </div>
            ))}
          </div>
          <InfiniteScroll
            threshold={90}
            loadMoreFunction={() => {
              // if (pagination.total > dataList?.length) {
              if (
                pagination.page < Math.ceil(pagination.total / pagination.limit)
              ) {
                setPagination((prev) => ({
                  ...prev,
                  page: prev?.page + 1,
                }));
              }
            }}
            isLoading={api.isLoading}
            loadingComponent={<TodoSkeleton count={2} />}
            timeout={10}
          />
          {completedTodo?.length > 0 && (
            <div
              {...getToggleProps()}
              className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-py-2"
            >
              <p className="tw-text-primary-black tw-text-lg tw-font-semibold">
                Completed ({completedTodo?.length})
              </p>
              <div className="tw-flex-shrink-0">
                {isExpanded ? (
                  <ChevronUp size={20} />
                ) : (
                  <ChevronDown size={20} />
                )}
              </div>
            </div>
          )}
          <div
            {...getCollapseProps()}
            className={`tw-mt-3 tw-mb-20 lg:tw-mb-3 sm:tw-my-4  tw-transition-all tw-duration-300 `}
          >
            {completedTodo?.map((ele) => (
              <div
                // ref={(el) => {
                //   if (el) todoRefs.current[ele.id] = el;
                // }}
                key={ele?.id}
                className="tw-mb-3 sm:tw-mb-4"
              >
                <TodoCard
                  ele={ele}
                  updateTodoHandler={updateTodoHandler}
                  deleteHandler={deleteHandler}
                  editHandler={editHandler}
                  isShowAllSettings={
                    project?.UserId === userData?.id ||
                    (ele?.User?.id === userData?.id &&
                      project?.ProjectMembers?.[0]?.access === "write")
                  }
                  isOnlyEditSetting={
                    // ele?.User?.id === userData?.id &&
                    project?.ProjectMembers?.[0]?.access === "write"
                  }
                  updateSetState={updateTodoState}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default Todos;
