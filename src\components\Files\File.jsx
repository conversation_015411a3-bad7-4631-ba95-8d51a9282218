"use client";
import { CustomContainer } from "../Common/Custom-Display";
import Empty from "../Common/Empty";
import logo from "../../../public/images/logo/logo-primary.svg";
import Image from "next/image";
import {
  AddProjectIcon,
  LeftArrowBackIcon,
  UploadFileIcon,
  UploadImageIcon,
} from "@/utils/icons";
// import { useRouter } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import FolderModal from "../ProjectPage/SingleProject/Tabs/FilesComponent/FolderModal";
import { useContext, useEffect, useRef, useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  addFile,
  deleteFiles,
  deleteFolder,
  getFiles,
  getFolders,
  getOneFolders,
  updateFiles,
  updateFolder,
  updateImageToURL,
} from "@/app/action";
import InfiniteScroll from "../Common/InfiniteScroll";
import FolderCard from "../ProjectPage/SingleProject/Tabs/FilesComponent/FolderCard";
import PopUpModal from "../Common/PopUpModal";
import dayjs from "dayjs";
import FileCard from "../ProjectPage/SingleProject/Tabs/FilesComponent/FileCard";
import {
  blurDataURL,
  downloadFileFromUrl,
  RESPONSE_STATUS,
} from "@/utils/function";
import Loader from "../Loader/Loader";
import { Skeleton } from "../ui/skeleton";
import FileSkeleton from "../Loader/FileSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { safeToast } from "@/utils/safeToast";
import { valContext } from "../context/ValContext";
import MoveToModal from "../HomePage/MoveToModal";
const File = ({ folderId }) => {
  const router = useRouter();
  const [folderDetails, setFolderDetails] = useState(null);
  const [isFolderAdd, setIsFolderAdd] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [folderList, setFolderList] = useState([]);
  const [isFileFetching, setIsFileFetching] = useState(true);
  const [isFolderFetching, setIsFolderFetching] = useState(true);
  const [editFolderData, setEditFolderData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const [folderPagination, setFolderPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [userData, setUserData] = useState(null);
  const [fileList, setFileList] = useState([]);
  const [filePagination, setFilePagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [isFileUploading, setIsFileUploading] = useState(false);
  const [isFolderDataLoading, setIsFolderDataLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [moveData, setMoveData] = useState(null);
  const uploadFileRef = useRef(null);
  const api = useApiRequest();
  const deleteApi = useApiRequest(false);

  // console.log(folderDetails, userData);
  // console.log(folderDetails);

  const fetchFilesData = (id) => {
    setIsFileFetching(true);
    let query = {
      page: filePagination.page,
      limit: filePagination.limit,
      FolderId: +id,
    };
    api.sendRequest(
      getFiles,
      (res) => {
        if (res?.data?.data?.length && filePagination?.page > 1) {
          setFileList((prev) => [...prev, ...res?.data?.data]);
        } else if (res?.data?.data?.length && filePagination.page === 1) {
          setFileList([...res?.data?.data]);
        } else {
        }
        setIsFileFetching(false);
      },
      query,
      "",
      () => {
        setIsFileFetching(false);
      }
    );
  };

  const fetchChildFolderData = (id, ProjectId) => {
    setIsFolderFetching(true);
    let query = {
      page: folderPagination.page,
      limit: folderPagination.limit,
      ParentId: +id,
      ProjectId,
    };
    api.sendRequest(
      getFolders,
      (res) => {
        if (res?.data?.data?.length && folderPagination?.page > 1) {
          setFolderList((prev) => [...prev, ...res?.data?.data]);
        } else if (res?.data?.data?.length && folderPagination.page === 1) {
          setFolderList([...res?.data?.data]);
        } else {
        }
        setIsFolderFetching(false);
      },
      query,
      "",
      () => {
        setIsFolderFetching(false);
      }
    );
  };
  // Uploading File
  const uploadFileHandler = async (e) => {
    setIsFileUploading(true);
    const file = e.target.files[0];
    console.log(file);
    let payload = {
      FolderId: folderId,
      name: file?.name,
    };

    e.target.value = null;
    const formData = new FormData();
    formData.append("file", file);
    try {
      const res = await updateImageToURL(formData);
      if (res?.status !== 200 && res?.status !== RESPONSE_STATUS.SUCCESS) {
        throw res;
      }
      payload = {
        ...payload,
        link: res?.data?.[0]?.link,
      };
    } catch (error) {
      setIsFileUploading(false);
      safeToast.error(error?.message);
      return;
    }

    // console.log(payload, "payload");

    api.sendRequest(
      addFile,
      (res) => {
        // console.log(res?.data);
        setIsFileUploading(false);
        setFileList((prev) => [...prev, res?.data]);
        // setRefresh((prev) => !prev);
        // resetDataList();
      },
      payload,
      "",
      () => {
        setIsFileUploading(false);
      }
    );
  };

  // Move Post handler
  const onMoveHandler = (folder) => {
    let payload = {
      FolderId: folder?.id,
      id: moveData?.id,
    };
    api.sendRequest(
      updateFiles,
      (res) => {
        setRefresh((prev) => !prev);
        resetDataList();
        safeToast.success(res?.message);
      },
      payload,
      "",
      (err) => {
        safeToast.error(err?.message);
      }
    );
  };

  // Reset Data
  const resetDataList = () => {
    setFolderList([]);
    setFileList([]);
    setFolderPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
    setFilePagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Folder Details
  useEffect(() => {
    if (folderId) {
      setIsFolderDataLoading(true);
      const user = authStorage.getProfileDetails();
      setUserData(user);
      api.sendRequest(
        getOneFolders,
        (res) => {
          setFolderDetails(res?.data);
          setIsFolderDataLoading(false);
        },
        {
          id: folderId,
        },
        "",
        () => {
          setIsFolderDataLoading(false);
        }
      );
    }
  }, [folderId]);

  useEffect(() => {
    if (folderId && folderDetails) {
      fetchChildFolderData(folderId, folderDetails?.ProjectId);
    }
  }, [folderPagination.page, folderId, refresh, folderDetails]);

  useEffect(() => {
    if (folderId) {
      fetchFilesData(folderId);
    }
  }, [filePagination.page, folderId, refresh]);

  return (
    <>
      {/* Move File Modal */}
      <MoveToModal
        open={isOpen}
        setIsOpen={setIsOpen}
        onMoveHandler={onMoveHandler}
        forFile={true}
      />
      {/* Delete Modal  */}
      <PopUpModal
        isLoading={deleteApi.isLoading}
        isOpen={deleteData}
        setIsOpen={setDeleteData}
        mainMessage={`Delete ${
          deleteData?.type === "file" ? "File" : "Folder"
        }`}
        subMessage={`Are you sure you want to Delete ${
          deleteData?.type === "file" ? "File" : "Folder"
        } Permanently?`}
        onConfirm={() => {
          deleteApi.sendRequest(
            deleteData?.type === "file" ? deleteFiles : deleteFolder,
            (res) => {
              resetDataList();
              setRefresh((prev) => !prev);
              setDeleteData(null);
              safeToast.success(res?.message);
            },
            deleteData?.id,

            ""
          );
        }}
      />
      {/* Add Folder Modal */}
      <FolderModal
        isOpen={isFolderAdd}
        setIsOpen={setIsFolderAdd}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
        editData={editFolderData}
        setEditData={setEditFolderData}
        defaultData={{
          id: folderDetails?.ProjectId,
        }}
        parentProjectData={folderDetails}
      />
      {/* Add File  */}
      <input
        onChange={uploadFileHandler}
        ref={uploadFileRef}
        type="file"
        className="tw-hidden"
      />
      <CustomContainer className="!tw-pt-[2rem] tw-h-full">
        {/* <Empty /> */}
        <div className="tw-relative tw-w-[7.5rem] tw-h-[2.8rem] ">
          <Image
            placeholder="blur"
            blurDataURL={blurDataURL(120, 80)}
            src={logo}
            alt="logo"
            fill
            className=""
          />
        </div>
        <div className="tw-max-w-[65rem] tw-mx-auto tw-relative">
          {isFolderDataLoading ? (
            <>
              <div className="tw-mt-10 tw-flex tw-justify-between tw-items-center">
                <div />
                <div className="tw-flex tw-gap-5 tw-items-center">
                  <Skeleton
                    className={
                      "tw-hidden lg:tw-block !tw-rounded-full !tw-h-[3.5rem] tw-w-[130px]"
                    }
                  />
                  <Skeleton
                    className={
                      "tw-hidden lg:tw-block !tw-rounded-full !tw-h-[3.5rem] tw-w-[130px]"
                    }
                  />
                </div>
              </div>
              <div>
                <div>
                  <Skeleton className="tw-h-8 tw-w-[160px] tw-my-2 !tw-rounded-xl" />
                </div>
              </div>
              <div className="tw-my-5 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
                <FileSkeleton count={6} />
              </div>
            </>
          ) : folderDetails ? (
            <>
              {
                <div className="tw-mt-10 tw-flex tw-justify-between tw-items-center">
                  <button
                    type="button"
                    onClick={() => {
                      router.back();
                    }}
                    className="tw-mb-4"
                  >
                    <LeftArrowBackIcon />
                  </button>
                  {(folderDetails?.Project?.UserId === userData?.id ||
                    userData?.id === folderDetails?.UserId ||
                    folderDetails?.Project?.ProjectMembers?.[0]?.access ===
                      "write") && (
                    <div className="tw-flex tw-gap-5 tw-items-center">
                      <button
                        type="button"
                        onClick={() => {
                          setIsFolderAdd((prev) => !prev);
                        }}
                        className="tw-fixed tw-bottom-4 tw-right-6 tw-z-[51] lg:tw-z-auto lg:tw-static tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
                      >
                        <AddProjectIcon size={20} className="" />
                        <span className="tw-hidden lg:tw-inline">
                          Create Folder
                        </span>
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          uploadFileRef.current.click();
                        }}
                        className="tw-fixed tw-bottom-20 tw-right-6 tw-z-[51] lg:tw-z-auto lg:tw-static tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
                      >
                        <UploadFileIcon size={20} className="" />
                        <span className="tw-hidden lg:tw-inline">
                          Upload File
                        </span>
                      </button>
                    </div>
                  )}
                </div>
              }
              <div>
                <div>
                  <p className="tw-text-primary-black tw-font-bold tw-text-4xl">
                    {folderDetails?.name}
                  </p>
                </div>
                <div className="tw-my-5">
                  {!isFileUploading &&
                    !isFileFetching &&
                    !isFolderFetching &&
                    folderList?.length === 0 &&
                    fileList?.length === 0 && (
                      <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
                        <Empty
                          iconRequired={false}
                          label="No Files available!"
                        />
                      </div>
                    )}

                  <div className="tw-my-5 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
                    {/* {api.isLoading && <ProjectSkeleton />} */}
                    {!isFolderFetching && folderList?.length > 0
                      ? folderList?.map((ele) => (
                          <div
                            className="tw-cursor-pointer"
                            onClick={() => {
                              router.push(`/files/${ele?.id}`);
                            }}
                            key={ele?.id}
                          >
                            <FolderCard
                              ele={ele}
                              editHandler={(ele) => {
                                setEditFolderData(ele);
                              }}
                              deleteHandler={(ele) => {
                                setDeleteData({
                                  ...ele,
                                  type: "folder",
                                });
                              }}
                              showAllSettings={
                                folderDetails?.Project?.UserId ===
                                  userData?.id ||
                                (ele?.UserId === userData?.id &&
                                  folderDetails?.Project?.ProjectMembers?.[0]
                                    ?.access === "write")
                              }
                              isOnlyEditSetting={
                                folderDetails?.Project?.ProjectMembers?.length >
                                0
                              }
                              // showFileCount={false}
                              // otherLabel={`Added: ${dayjs(
                              //   ele?.createdAt
                              // )?.format("MMM DD, YYYY")}`}
                            />
                          </div>
                        ))
                      : ""}
                    {!isFileFetching && fileList?.length > 0
                      ? fileList?.map((ele) => (
                          <div key={ele?.id}>
                            <FileCard
                              ele={ele}
                              downloadHandler={(ele) => {
                                downloadFileFromUrl(ele?.link, ele?.name);
                              }}
                              deleteHandler={(ele) => {
                                setDeleteData({
                                  ...ele,
                                  type: "file",
                                });
                              }}
                              showAllSettings={
                                folderDetails?.Project?.UserId ===
                                  userData?.id ||
                                folderDetails?.UserId === userData?.id ||
                                (ele?.UserId === userData?.id &&
                                  folderDetails?.Project?.ProjectMembers?.[0]
                                    ?.access === "write")
                              }
                              onlyDownloadSettings={
                                folderDetails?.Project?.ProjectMembers?.length >
                                0
                              }
                              otherLabel={`Added: ${dayjs(
                                ele?.createdAt
                              )?.format("MMM DD, YYYY")}`}
                              moveFileHandler={(ele) => {
                                setIsOpen(true);
                                setMoveData(ele);
                              }}
                            />
                          </div>
                        ))
                      : ""}

                    {isFileUploading && (
                      <div>
                        <div className="tw-relative tw-bg-[#F6F6F6] tw-px-4 tw-py-16 tw-rounded-3xl tw-flex tw-justify-center tw-items-center">
                          <Loader size="50px" />
                        </div>
                        <div className="tw-space-y-2">
                          <Skeleton className="tw-h-6 tw-w-[100%] tw-my-2 !tw-rounded-xl" />
                          <Skeleton className="tw-h-4 tw-w-[160px] " />
                        </div>
                      </div>
                    )}

                    <InfiniteScroll
                      threshold={90}
                      loadMoreFunction={() => {
                        if (
                          Math.ceil(
                            folderPagination.total / folderPagination.limit
                          ) > folderPagination.page
                        ) {
                          setFolderPagination((prev) => ({
                            ...prev,
                            page: prev?.page + 1,
                          }));
                        }
                        if (
                          Math.ceil(
                            filePagination.total / filePagination.limit
                          ) > filePagination.page
                        ) {
                          setFilePagination((prev) => ({
                            ...prev,
                            page: prev?.page + 1,
                          }));
                        }
                      }}
                      isLoading={isFileFetching || isFolderFetching}
                      loadingComponent={<FileSkeleton count={6} />}
                      timeout={10}
                      // loadOff={loadOff}
                    />
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="tw-h-[80vh] tw-flex tw-justify-center tw-items-center">
                <Empty iconRequired={false} label="No Folder Found!" />
              </div>
            </>
          )}
        </div>
      </CustomContainer>
    </>
  );
};

export default File;
