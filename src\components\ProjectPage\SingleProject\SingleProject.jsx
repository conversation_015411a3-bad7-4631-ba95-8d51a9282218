"use client";
import { CustomContainer } from "@/components/Common/Custom-Display";
import {
  blurDataURL,
  privateProjectToggle,
  RESPONSE_STATUS,
  stories,
} from "@/utils/function";
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "next/image";
import { ChevronRight, Flag, Lock, Plus, Trash2 } from "lucide-react";
import {
  EditProfileIcon,
  LeftArrowBackIcon,
  LocationIcon,
  PrivateProjectIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import authStorage from "@/utils/API/AuthStorage";
import SingleProjectSkeleton from "@/components/Loader/SingleProjectSkeleton";
import AboutModal from "./AboutModal";
import Link from "next/link";
import {
  deleteProject,
  followOrUnfollowProject,
  getAllHighlights,
  getOneProject,
  reportProject,
  updateProject,
} from "@/app/action";
import toast from "react-hot-toast";
import Empty from "@/components/Common/Empty";
import noProjectImg from "../../../../public/images/assets/not_folder.png";

import PopUpModal from "@/components/Common/PopUpModal";
import { usePathname } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import ProjectModal from "@/components/HomePage/Form/ProjectModal";
import SubProjectModal from "@/components/HomePage/Form/SubProjectModal";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import ShowMenuList from "@/components/Common/ShowMenuList";
import { ProjectContext } from "@/components/context/ProjectContext";
import ShareModal from "@/components/Common/ShareModal";
import ReportModal from "@/components/Common/ReportModal";
import HighlightsList from "@/components/Common/HighlightsList";
import { useProgress } from "@bprogress/next";
import { safeToast } from "@/utils/safeToast";
import tempImg from "../../../../public/images/assets/default_image.png";
import UserAvatar from "@/components/Common/UserAvatar";
import AddHighlightModal from "@/components/Profile/AddHighlightModal";
import { valContext } from "@/components/context/ValContext";

const SingleProject = ({
  slug,
  data,
  children,
  isOnlyEditSubProject = false,
  deleteData = {
    label: "Delete Project",
    subLabel: "Are you sure you want to Delete Project Permanently.",
  },
  projectsTabs = [
    {
      label: "Posts",
      value: "post",
    },
    {
      label: "Sub-projects",
      value: "sub-projects",
    },
    {
      label: "To Do",
      value: "todos",
    },
    {
      label: "Files",
      value: "files",
    },
    {
      label: "Members",
      value: "members",
    },
    {
      label: "Drafts",
      value: "drafts",
    },
  ],
  origin,
}) => {
  const [projectData, setProjectData] = useState(null);

  const [userData, setUserData] = useState(null);
  const [isFollow, setIsFollow] = useState(false);
  const [editData, setEditData] = useState(null);
  // const [deleteProject, setDeleteProject] = useState(null);
  const [deleteProjectData, setDeleteProjectData] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [isAboutModalOpen, setIsAboutModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hideProject, setHideProject] = useState(null);
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareData, setShareData] = useState(null);
  const router = useRouter();
  const progress = useProgress();
  const path = usePathname().split("/")?.[3];
  const api = useApiRequest(false);
  const headerRef = useRef(null);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [isHighlighAdd, setIsHighlighAdd] = useState(false);
  const [refreshHighlight, setRefreshHighlight] = useState(false);
  const [highlights, setHighlights] = useState([]);

  const { setProject } = useContext(ProjectContext);
  const { subProjectPostCount, setSubProjectPostCount } =
    useContext(valContext);

  const singleProjectAPI = useApiRequest(false);

  // Menus
  const myProjectSetting = [
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        setEditData(ele);
      },
    },
    {
      label: `${!projectData?.hide ? "Hide Private" : "Undo Hide"} Project`,
      className: "",
      icon: <Lock size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        // setIsHideProject(true);
        setHideProject(ele);
      },
    },
    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        setDeleteProjectData(ele);
      },
    },
  ];

  const otherProjectSetting = [
    {
      label: "Share",
      className: "",
      icon: <ShareIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        setIsShareOpen(true);
        setShareData(ele);
      },
    },
    {
      label: "Report this Project",
      className: "",
      icon: <Flag size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        setIsReported(true);
        setReportData(ele);
      },
    },
  ];

  // console.log(projectData)

  // Tabs List

  const followAndUnfollowProjectHandler = async (projectId, isFollow = 1) => {
    // console.log(projectId, isFollow);
    try {
      const response = await followOrUnfollowProject({
        id: projectId,
        isFollow: Boolean(isFollow),
      });
      if (
        response?.status === RESPONSE_STATUS.SUCCESS ||
        response?.status === 200
      ) {
        // setProjectData((prev) => ({
        //   ...prev,
        //   isFollowed: isFollow,
        // }));
        safeToast.success(response?.message);
        return;
      }

      throw response;
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };
  // Fetch Data
  const fetchData = useCallback(() => {
    setIsLoading(true);
    const currentUserData = authStorage.getProfileDetails();
    // if (refresh) {
    api.sendRequest(
      getOneProject,
      (res) => {
        setUserData(currentUserData);
        if (res?.data) {
          const responseData = {
            ...res?.data,
            createdBy:
              +currentUserData?.id === +res?.data?.UserId ? "me" : "User",
          };
          setProjectData(responseData);
          setProject(responseData);
        }
        setIsFollow(Boolean(+res?.data?.isFollowed));
      },
      {
        id: slug,
      }
    );
    // } else if (data && Object.keys(data).length > 0) {
    //   setUserData(currentUserData);
    //   setProjectData({
    //     ...data,
    //     createdBy: +currentUserData?.id === data?.UserId ? "me" : "User",
    //   });
    //   setIsFollow(Boolean(+data?.isFollowed));
    // }
    setIsLoading(false);
  }, [refresh]);

  useEffect(() => {
    fetchData();
  }, [refresh]);

  // Scroll Down
  useEffect(() => {
    if (headerRef.current && isOnlyEditSubProject) {
      headerRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [isOnlyEditSubProject]);
  // usePathname(), useSearchParams(),

  useEffect(() => {
    if (
      isOnlyEditSubProject &&
      +projectData?.postsCount &&
      +projectData?.postsCount > 0
    ) {
      setSubProjectPostCount(+projectData?.postsCount);
    }
  }, [projectData]);

  // Get Highlights
  useEffect(() => {
    if (projectData?.id && projectData?.UserId) {
      api.sendRequest(
        getAllHighlights,
        (res) => {
          setHighlights(res?.data?.data);
        },
        {
          ProjectId: projectData?.id,
          UserId: projectData?.UserId,
        }
      );
    }
  }, [projectData, refreshHighlight]);
  return (
    <div ref={headerRef}>
      {/* Add Highlight Modal */}
      <AddHighlightModal
        isOpen={isHighlighAdd}
        setIsOpen={setIsHighlighAdd}
        setRefresh={setRefresh}
        userId={userData?.id}
        ProjectId={projectData?.id}
      />
      <ReportModal
        title="Why are you reporting this project?"
        isOpen={isReported}
        setIsOpen={setIsReported}
        isLoading={singleProjectAPI?.isLoading}
        onConfirm={(reason) => {
          const payload = {
            reason,
            id: reportData?.id,
          };
          singleProjectAPI.sendRequest(
            reportProject,
            (res) => {
              safeToast.success(res?.message);
              setIsReported(false);
              setRefresh((prev) => !prev);
              setReportData(null);
            },
            payload
          );
        }}
      />
      {/* Share Modal */}
      <ShareModal
        open={isShareOpen}
        onClose={() => {
          setIsShareOpen(false);
        }}
        shareUrl={`${origin}/${
          isOnlyEditSubProject ? "sub-project" : "projects"
        }/${shareData?.slug}`}
        title={`${shareData?.name} \nCheckout this project:`}
      />
      {/* isOnlyEditSubProject if this prop is true then open the modal for sub-project data other wise open for the project data */}
      {/* Project Modal */}
      {/* Edit Sub-project Modal */}
      {isOnlyEditSubProject ? (
        <SubProjectModal
          isOnlyEditSubProject={isOnlyEditSubProject}
          editData={editData}
          setEditData={setEditData}
          editProjectData={editData}
          setRefresh={setRefresh}
          modalTitle={"Edit sub-project"}
          modalSubmitButton={"Update"}
        />
      ) : (
        <ProjectModal
          editData={editData}
          setEditData={setEditData}
          setRefresh={setRefresh}
          modalTitle={"Edit Project"}
          modalSubmitButton={"Update"}
        />
      )}
      {/* Project hide/unhide Confirm Popup */}
      <PopUpModal
        isLoading={singleProjectAPI.isLoading}
        isOpen={hideProject}
        setIsOpen={setHideProject}
        icon={null}
        mainMessage={
          privateProjectToggle(projectData?.name, !projectData?.hide)?.title
        }
        subMessage={
          privateProjectToggle(projectData?.name, !projectData?.hide)?.message
        }
        // subMessage={`Are you sure you want to ${
        //   !projectData?.hide ? "hide" : "Unhide"
        // } your Project?`}
        onConfirm={() => {
          const payload = {
            id: hideProject?.id,
            hide: !hideProject?.hide,
          };
          singleProjectAPI.sendRequest(
            updateProject,
            (res) => {
              // console.log(res);
              safeToast.success(
                privateProjectToggle(projectData?.name, !projectData?.hide)
                  ?.successMessage
              );
              setRefresh((prev) => !prev);
              setHideProject(null);
            },
            payload
          );
        }}
      />
      {/* Delete Confirm Popup */}
      <PopUpModal
        isLoading={singleProjectAPI.isLoading}
        isOpen={deleteProjectData}
        setIsOpen={setDeleteProjectData}
        mainMessage={deleteData?.label}
        subMessage={deleteData?.subLabel}
        onConfirm={() => {
          singleProjectAPI.sendRequest(
            deleteProject,
            () => {
              // progress.start(0, 1);
              if (projectData?.ParentProject) {
                router.push(`/projects/${projectData?.ParentProject?.slug}`);
                return;
              }
              router.push("/projects");
            },
            {
              id: deleteProjectData?.id,
            },
            "Project Deleted Successfully"
          );
        }}
      />
      {/* <SingleProjectSkeleton /> */}
      {api.isLoading || isLoading ? (
        <SingleProjectSkeleton />
      ) : projectData ? (
        <>
          <CustomContainer className="tw-py-4 tw-h-full">
            {/* About Modal */}
            <AboutModal
              isOpen={isAboutModalOpen}
              setIsOpen={setIsAboutModalOpen}
              projectData={projectData}
              loginUser={userData}
            />

            <button
              type="button"
              onClick={() => {
                // progress.start(0, 1);
                progress.start();
                router.back();
              }}
              className="tw-mb-4"
            >
              <LeftArrowBackIcon />
            </button>
            {/* Project Data */}
            {
              <div className="tw-relative tw-w-full tw-h-[21rem] ">
                <Image
                  src={projectData?.image ?? tempImg}
                  alt={"project logo"}
                  fill
                  className="tw-rounded-3xl tw-object-cover"
                  placeholder="blur"
                  priority
                  blurDataURL={blurDataURL(800, 400)}
                />
                <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
                <ShowMenuList
                  data={projectData}
                  menuList={
                    projectData?.createdBy !== "me" &&
                    projectData?.ParentProject?.UserId !== userData?.id
                      ? otherProjectSetting
                      : myProjectSetting?.filter((data) =>
                          projectData?.isPrivate
                            ? data
                            : data?.label === "Edit" || data?.label === "Delete"
                        )
                  }
                >
                  <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-7">
                    <ThreeDotMenuIcon fill="#fff" size={24} />
                  </div>
                </ShowMenuList>
              </div>
            }
            {isOnlyEditSubProject && (
              <div className="tw-flex tw-gap-2 tw-items-center tw-text-primary-black tw-text-base tw-font-medium tw-mt-6">
                <div
                  className={`${
                    projectData?.ParentProject?.name && "tw-cursor-pointer"
                  }`}
                  onClick={() => {
                    if (projectData?.ParentProject?.name) {
                      progress.start();
                      router.push(
                        `/projects/${projectData?.ParentProject?.slug}/sub-projects`
                      );
                    }
                  }}
                >
                  {projectData?.ParentProject?.name || projectData?.name}
                </div>

                <div className="tw-flex tw-gap-2 tw-items-center">
                  <div>
                    <ChevronRight size={15} />
                  </div>
                  <div>{projectData?.name}</div>
                </div>
              </div>
            )}
            <div className="tw-flex tw-justify-between tw-items-center ">
              <div className="tw-flex tw-gap-2 tw-items-center">
                {projectData?.isPrivate && (
                  <div className="tw-bg-primary-purple tw-p-2 tw-rounded-full tw-flex tw-z-40  tw-items-center">
                    <PrivateProjectIcon size={20} />
                  </div>
                )}
                <button
                  onClick={() => setIsAboutModalOpen((prev) => !prev)}
                  className="tw-flex tw-gap-2 tw-items-center"
                >
                  <h1
                    title={projectData?.name}
                    className="tw-my-5 tw-text-4xl tw-line-clamp-1 tw-w-auto tw-text-left tw-font-bold tw-text-primary-black "
                  >
                    {projectData?.name}
                  </h1>
                  <ChevronRight size={30} />
                </button>
              </div>
              {projectData?.ProjectMembers?.length === 0 &&
                userData?.id !== projectData?.User?.id &&
                !isOnlyEditSubProject && (
                  <button
                    type="button"
                    onClick={() => {
                      followAndUnfollowProjectHandler(
                        projectData?.id,
                        !isFollow
                      );
                      setIsFollow((prev) => !prev);
                    }}
                    className={`${
                      isFollow
                        ? "tw-bg-transparent tw-border tw-border-[#787E89] tw-text-[#787E89]"
                        : "tw-bg-primary-purple tw-text-white"
                    }  tw-text-lg tw-py-4 tw-px-7 tw-rounded-full tw-font-semibold`}
                  >
                    {isFollow ? "Following" : "Follow"}
                  </button>
                )}
            </div>
            <div className="-tw-mt-4">
              {projectData?.location && (
                <div className="tw-text-[#787E89] tw-flex tw-gap-1.5 tw-text-sm lg:tw-text-base tw-items-start lg:tw-items-center">
                  <div className="tw-relative tw-top-1 md:tw-top-0">
                    <LocationIcon />
                  </div>
                  <p className="tw-max-w-[22rem] md:tw-max-w-[35rem] lg:tw-max-w-[100%]">
                    {projectData?.location}
                  </p>
                </div>
              )}
              <div className="   tw-text-[#787E89]">
                {isOnlyEditSubProject ? (
                  (projectData?.User?.id === userData?.id ||
                    projectData?.ProjectMembers?.length > 0) &&
                  isOnlyEditSubProject ? (
                    <>
                      {(+projectData?.postsCount > 0 ||
                        +subProjectPostCount > 0) && (
                        <p>
                          {+subProjectPostCount
                            ? subProjectPostCount
                            : +projectData?.postsCount ?? 0}{" "}
                          Posts
                        </p>
                      )}
                    </>
                  ) : null
                ) : (
                  <div className="tw-italic tw-flex tw-gap-2 tw-items-center">
                    <p>Created by</p>
                    {projectData?.createdBy === "me" ? (
                      projectData?.createdBy
                    ) : (
                      <Link
                        href={`/user/${projectData?.User?.slug}`}
                        className="tw-flex  tw-gap-2 tw-items-center"
                      >
                        <UserAvatar
                          imageParentClassName="!tw-w-[2rem] !tw-h-[2rem]"
                          imageUrl={projectData?.User?.image}
                          userName={projectData?.User?.firstName}
                          userNameClassName="!tw-text-base"
                        />
                        <div>
                          <p className="tw-font-medium !tw-text-sm tw-not-italic !tw-text-primary-black">
                            {`${projectData?.User?.firstName ?? ""} ${
                              projectData?.User?.lastName ?? ""
                            }`}
                          </p>
                        </div>
                      </Link>
                    )}
                  </div>
                )}
              </div>
            </div>
            {/* Highlights */}

            {(projectData?.createdBy === "me" || highlights?.length > 0) && (
              <div className="tw-w-full tw-overflow-hidden tw-block tw-mt-8">
                <div className="tw-flex tw-items-start tw-gap-3">
                  {projectData?.createdBy === "me" && (
                    <div className="tw-flex tw-flex-col tw-items-center">
                      <button
                        type="button"
                        onClick={() => {
                          setIsHighlighAdd(true);
                        }}
                        className="tw-relative tw-w-[5.3rem] tw-h-[5.3rem] tw-flex tw-items-center tw-justify-center"
                      >
                        {/* Dashed Circle via SVG */}
                        <svg
                          className="tw-absolute tw-w-full tw-h-full"
                          viewBox="0 0 100 100"
                        >
                          <circle
                            cx="50"
                            cy="50"
                            r="48"
                            fill="#f3e8ff" // Tailwind: tw-bg-purple-100
                            stroke="#a855f7" // Tailwind: tw-border-purple-500
                            strokeWidth="2"
                            strokeDasharray="10 8"
                          />
                        </svg>

                        {/* Inner Button */}
                        <div className="tw-w-10 tw-h-10 tw-rounded-full tw-bg-white tw-border tw-border-gray-700 tw-flex tw-items-center tw-justify-center tw-z-10">
                          <Plus className="tw-text-gray-700" />
                        </div>
                      </button>
                      <p className="tw-w-full tw-text-sm tw-font-bold tw-text-primary-black tw-pt-1">
                        Create New
                      </p>
                    </div>
                  )}

                  {highlights?.length > 0 && (
                    <HighlightsList
                      data={highlights}
                      setReload={setRefreshHighlight}
                      isSettingVisible={projectData?.User?.id === userData?.id}
                    />
                  )}
                </div>
                {/* <HighlightsList data={highlights ?? []} /> */}
              </div>
            )}
            {/* Tabs */}

            <div className=" tw-my-10 ">
              {projectData && (
                <div className="tw-flex  tw-items-center tw-gap-4 tw-overflow-auto">
                  {projectsTabs

                    ?.filter((ele) =>
                      projectData?.createdBy === "User" &&
                      projectData?.ProjectMembers &&
                      projectData?.ProjectMembers.length < 1
                        ? isOnlyEditSubProject
                          ? null
                          : ele?.label === "Posts" ||
                            ele?.label === "Sub-projects" ||
                            ele?.label === "Members"
                        : projectData?.createdBy === "User" &&
                          projectData?.ProjectMembers &&
                          projectData?.ProjectMembers.length > 0
                        ? ele?.label !== "To Do"
                        : ele?.label
                    )
                    ?.map((ele) => (
                      <button
                        type="button"
                        onClick={() => {
                          const baseRoute = `/${
                            isOnlyEditSubProject ? "sub-project" : "projects"
                          }/${projectData?.slug}`;
                          progress.start(0, 1);
                          router.push(
                            ele?.value === "post"
                              ? baseRoute
                              : `${baseRoute}/${ele?.value}`
                          );

                          // setActiveTab(ele?.value);
                          // resetDataList();
                        }}
                        key={ele?.label}
                        className={`tw-py-3 tw-cursor-pointer tw-w-full md:tw-w-auto tw-rounded-full tw-text-center ${
                          ele?.value === (path ?? "post")
                            ? "tw-font-bold tw-bg-[#F6EFFE] tw-text-incenti-purple"
                            : "tw-bg-[#F5F7F8] tw-text-[#2D394A] tw-font-medium"
                        } tw-px-5`}
                      >
                        <p
                          className={`${
                            ele?.label === "Sub-projects" &&
                            "tw-w-[7rem] md:tw-w-auto"
                          } ${
                            ele?.label === "To Do" && "tw-w-[4rem] md:tw-w-auto"
                          }`}
                        >
                          {ele?.label}
                        </p>
                      </button>
                    ))}
                </div>
              )}
            </div>

            {/* <div>{activeTabRender[activeTab]}</div> */}
            <div className="tw-relative">{children}</div>
          </CustomContainer>
        </>
      ) : (
        <div className="tw-h-[80vh] tw-flex tw-justify-center tw-items-center">
          <Empty
            icon={<Image src={noProjectImg} width={261} height={248} />}
            label="Project Not Found"
          />
        </div>
      )}
    </div>
  );
};

export default SingleProject;
