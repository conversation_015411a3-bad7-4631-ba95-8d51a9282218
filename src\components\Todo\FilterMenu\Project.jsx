import { getProject, getSubProjectsFromProjectIds } from "@/app/action";
import Empty from "@/components/Common/Empty";
import SearchBar from "@/components/Common/SearchBar";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { useDebouncedSearch } from "@/components/helper/hook/useDebouncedSearch";
import GlobalLoader from "@/components/Loader/GlobalLoader";
import SelectProjectSkeleton from "@/components/Loader/SelectProjectSkeleton";
import { Skeleton } from "@/components/ui/skeleton";
import authStorage from "@/utils/API/AuthStorage";
import { CloseModalIcon, LeftArrowBackIcon } from "@/utils/icons";
import { SearchIcon } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const Project = ({
  isOpen,
  setSelectedMenu,
  selectedProjects,
  setSelectedProjects,
  selectedSubProjects,
  setSelectedSubProjects,
  isMultiple = true,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [tempSelected, setTempSelected] = useState([]);
  const [tempSelectedSubProject, setTempSelectedSubProject] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [search, setSearch] = useState("");
  const debounceRef = useRef(null);
  const api = useApiRequest();
  const [isLoading, setIsLoading] = useState(true);

  // const onSearch = (value) => {
  //   setSearch(value);
  //   if (debounceRef.current) {
  //     clearTimeout(debounceRef.current);
  //   }
  //   debounceRef.current = setTimeout(() => {
  //     resetData();
  //     setSearchQuery(value);
  //   }, 500);
  // };

  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const onSearch = useDebouncedSearch({
    setSearchQuery, // required
    resetData, // optional
    setSearch, // optional
  });

  // const handleSelect = (value, setState) => {
  //   setState((prev) => {
  //     if (prev?.find((ele) => ele?.id === value?.id)) {
  //       return isMultiple ? prev?.filter((item) => item?.id !== value?.id) : [];
  //     } else if (isMultiple) {
  //       return [...prev, value];
  //     } else {
  //       return [value];
  //     }
  //   });
  // };
  const handleSelect = (project, setState) => {
    setState((prev) => {
      const isAlreadySelected = prev?.find((ele) => ele?.id === project?.id);

      if (isAlreadySelected) {
        // Deselect parent -> remove subprojects too
        setTempSelectedSubProject((prevSub) =>
          prevSub.filter(
            (sub) => !project?.subProject?.some((sp) => sp.id === sub.id)
          )
        );
        return isMultiple
          ? prev.filter((item) => item?.id !== project?.id)
          : [];
      } else {
        // Select parent -> add all subprojects
        if (isMultiple) {
          setTempSelectedSubProject((prevSub) => {
            const newSubProjects =
              project?.subProject?.filter(
                (sp) => !prevSub.some((prev) => prev.id === sp.id)
              ) || [];
            return [...prevSub, ...newSubProjects];
          });
          return [...prev, project];
        } else {
          setTempSelectedSubProject(project?.subProject || []);
          return [project];
        }
      }
    });
  };

  // const handleSelectSubProject = (value) => {
  //   setTempSelected((prev) => {
  //     if (prev?.find((ele) => ele?.id === value?.id)) {
  //       return isMultiple ? prev?.filter((item) => item?.id !== value?.id) : [];
  //     } else if (isMultiple) {
  //       return [...prev, value];
  //     } else {
  //       return [value];
  //     }
  //   });
  // };

  // Fetch Projects
  const fetchProjects = useCallback(async () => {
    setIsLoading(true);
    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
    };

    const userDetails = authStorage.getProfileDetails();
    if (userDetails?.id) {
      queryParams.UserId = userDetails.id;
    }

    if (searchQuery) {
      queryParams.searchQuery = searchQuery;
    }

    api.sendRequest(
      getProject,
      async (res) => {
        const projectList = res?.data?.data || [];

        // Fetch subprojects for each project in parallel
        const subProjectData = await Promise.all(
          projectList.map(async (ele) => {
            try {
              const response = await getSubProjectsFromProjectIds({
                ProjectIds: ele?.id,
              });
              return {
                ...ele,
                subProject: response?.data?.data || [],
              };
            } catch (err) {
              console.error(`Error fetching subprojects for ${ele?.id}`, err);
              return {
                ...ele,
                subProject: [],
              };
            } finally {
            }
          })
        );
        setIsLoading(false);
        // ✅ Use the enriched data
        if (pagination.page === 1) {
          setDataList(subProjectData);
        } else {
          setDataList((prev) => [...prev, ...subProjectData]);
        }

        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
      },
      queryParams
    );
  }, [pagination.page, searchQuery]);

  // const fetchProjects = useCallback(async () => {
  //   let queryParams = {
  //     page: pagination.page,
  //     limit: pagination.limit,
  //   };

  //   const userDetails = authStorage.getProfileDetails();
  //   if (Object.keys(userDetails)?.length > 0) {
  //     queryParams = { ...queryParams, UserId: userDetails?.id };
  //   }

  //   if (searchQuery) {
  //     queryParams = {
  //       ...queryParams,
  //       searchQuery,
  //     };
  //   }
  //   api.sendRequest(
  //     getProject,
  //     async (res) => {
  //       const subProjectData = await Promise.all(
  //         res?.data?.data?.map(async (ele) => {
  //           const response = await getSubProjectsFromProjectIds({
  //             ProjectIds: ele?.id,
  //           });

  //           return {
  //             ...ele,
  //             subProject: response?.data?.data,
  //           };
  //         })
  //       );

  //       console.log(subProjectData);

  //       if (pagination.page === 1) {
  //         setDataList(res?.data?.data);
  //       } else {
  //         setDataList((prev) => [...prev, ...res?.data?.data]);
  //       }
  //       setPagination((prev) => ({
  //         ...prev,
  //         total: res?.data?.totalRecords,
  //       }));
  //     },

  //     queryParams
  //   );
  // }, [pagination.page, searchQuery]);

  useEffect(() => {
    setTempSelected(selectedProjects ?? []);
    setTempSelectedSubProject(selectedSubProjects ?? []);
  }, [selectedProjects, selectedSubProjects]);

  useEffect(() => {
    fetchProjects();
  }, [pagination.page, searchQuery]);

  useEffect(() => {
    resetData();
  }, [isOpen]);
  return (
    <>
      <div className="tw-flex tw-justify-between tw-items-center">
        <button
          type="button"
          onClick={() => {
            setSelectedMenu(null);
          }}
          className=""
        >
          <LeftArrowBackIcon />
        </button>
        <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
          Projects
        </p>
        <button
          type="button"
          className="tw-text-primary-purple tw-font-bold"
          onClick={() => {
            setSelectedProjects(tempSelected);
            setSelectedSubProjects(tempSelectedSubProject);
            setSelectedMenu(null);
          }}
        >
          Done
        </button>
      </div>

      <div className="tw-my-2">
        {/* <div className="tw-flex tw-mt-4 tw-gap-2 tw-items-center tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8]">
          <SearchIcon color="#787E89" />
          <input
            type="text"
            id="simple-search"
            className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500 `}
            disabled={dataList?.length === 0}
            autoComplete="off"
            placeholder="Search"
            value={search}
            onChange={(e) => {
              onSearch(e.target.value);
            }}
          />
          {search && (
            <button
              onClick={() => {
                setSearch("");
                setSearchQuery("");
                resetData();
              }}
              className="tw-ml-2"
            >
              <CloseModalIcon size={16} />
            </button>
          )}
        </div> */}
        <SearchBar
          search={search}
          setSearch={setSearch}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          onSearch={onSearch}
          resetData={resetData}
        />
        {isLoading && pagination.page === 1 && <SelectProjectSkeleton />}
        {dataList?.length > 0 && (
          <InfiniteScroll
            height={310}
            dataLength={dataList?.length ?? 0}
            // show-scrollbar css class to show the scroll bar
            className="infinite-scrollbar tw-px-1"
            hasMore={
              Math.ceil(pagination.total / pagination.limit) > pagination.page
            }
            next={() =>
              setPagination((prev) => ({
                ...prev,
                page: prev.page + 1,
              }))
            }
            loader={<SelectProjectSkeleton count={3} />}
          >
            {dataList.map((ele) => (
              <div className="tw-w-full tw-my-5" key={ele.id}>
                <button
                  type="button"
                  className=" tw-w-full tw-flex tw-justify-between tw-items-center"
                  onClick={() => {
                    handleSelect(ele, setTempSelected);
                  }}
                >
                  <p
                    className={`tw-text-xl tw-text-primary-black tw-font-medium ${
                      tempSelected?.find((data) => data?.id === ele.id) &&
                      "!tw-text-primary-purple"
                    }`}
                  >
                    {ele.name}
                  </p>
                  <div>
                    <input
                      className="tw-scale-150 tw-cursor-pointer"
                      type="checkbox"
                      // checked={tempSelected.includes(ele.id)}
                      checked={
                        !!tempSelected?.find((data) => data?.id === ele?.id)
                      }
                      readOnly
                    />
                  </div>
                </button>
                {/* Sub Project Details */}
                {!!tempSelected?.find((data) => data?.id === ele?.id) && (
                  <div className="tw-pl-2">
                    {ele?.subProject?.map((ele) => (
                      <div
                        key={ele.id}
                        className="tw-flex tw-items-center tw-gap-3"
                      >
                        <div className="tw-bg-primary-black tw-h-1 tw-w-1" />
                        <button
                          type="button"
                          className="tw-my-2 tw-w-full tw-flex tw-justify-between tw-items-center "
                          onClick={() =>
                            handleSelect(ele, setTempSelectedSubProject)
                          }
                        >
                          <p
                            className={` tw-text-primary-black tw-font-medium ${
                              tempSelectedSubProject.find(
                                (data) => data?.id === ele.id
                              ) && "!tw-text-primary-purple"
                            }`}
                          >
                            {ele.name}
                          </p>
                          <div>
                            <input
                              className="tw-scale-150 tw-cursor-pointer"
                              type="checkbox"
                              checked={
                                !!tempSelectedSubProject?.find(
                                  (data) => data?.id === ele?.id
                                )
                              }
                              readOnly
                            />
                          </div>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </InfiniteScroll>
        )}
        {!isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[20rem]">
            <Empty
              iconRequired={false}
              label={"No Project"}
              subLabel={"No project yet!"}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default Project;
