"use client";
import { ChevronLeft } from "lucide-react";
import { useEffect, useRef, useState, useMemo, useCallback } from "react";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  addHighlights,
  getUserStories,
  updateHighlights,
  updateImageToURL,
} from "@/app/action";
import {
  base64ToFile,
  convertUTCtoLocal,
  createProxyImageUrl,
  getDifference,
  isExistInData,
} from "@/utils/function";
import AvatarEditor from "react-avatar-editor";
import { safeToast } from "@/utils/safeToast";
import FullScreenLoader from "../Loader/FullScreenLoader";
import Image from "next/image";
import Empty from "../Common/Empty";
import authStorage from "@/utils/API/AuthStorage";

// Function to create a CORS-friendly image URL

const renderStoryContent = (story, extraClass = "") => {
  const { overlayImage, thumbNailImage } = story;
  // console.log(story, "story");

  return (
    <div className={`tw-w-full tw-relative tw-h-[20rem] ${extraClass}`}>
      {/* <div
        style={{
          width: "100%",
          height: "100%",
          position: "relative",
          backgroundImage: `url(${overlayImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
        className={`${fontClass}`}
      ></div> */}
      <Image
        src={thumbNailImage ?? overlayImage}
        alt="story"
        fill
        className="!tw-object-cover"
        priority
      />
    </div>
  );
};

const AddHighlightModal = ({
  isOpen,
  setIsOpen,
  setRefresh,
  userId,
  ProjectId = null,
  editData = null,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState("");
  const [selectedStories, setSelectedStories] = useState([]);
  const [selectedCoverImage, setSelectedCoverImage] = useState(null);
  const [storyList, setStoryList] = useState([]);
  const [steps, setSteps] = useState(0);
  const [imagePreloaded, setImagePreloaded] = useState(false);
  const [preloadedImageUrl, setPreloadedImageUrl] = useState(null);
  // 0 -> Enter Highlight Name
  // 1 -> Select Stories
  // 2 -> Select Cover Image
  const api = useApiRequest();
  const editorRef = useRef(null);

  // Memoize the current cover image URL to prevent unnecessary re-renders
  // Prefer thumbnail for faster loading, fallback to overlay image
  const currentImageUrl = useMemo(() => {
    if (!selectedCoverImage) return null;
    return (
      selectedCoverImage?.thumbNailImage ?? selectedCoverImage?.overlayImage
    );
  }, [selectedCoverImage]);

  // Preload image when selectedCoverImage changes
  useEffect(() => {
    if (currentImageUrl && steps === 2) {
      setImagePreloaded(false);
      setPreloadedImageUrl(null);

      const proxyUrl = createProxyImageUrl(currentImageUrl);

      // Preload the image using native browser Image constructor
      const img = document.createElement("img");
      img.crossOrigin = "anonymous";
      img.onload = () => {
        setImagePreloaded(true);
        setPreloadedImageUrl(proxyUrl);
      };
      img.onerror = () => {
        setImagePreloaded(true); // Still set to true to show the editor
        setPreloadedImageUrl(proxyUrl); // Use the proxy URL anyway
      };
      img.src = proxyUrl;
    }
  }, [currentImageUrl, steps]);

  // Memoized AvatarEditor component to prevent unnecessary re-renders
  const MemoizedAvatarEditor = useMemo(() => {
    if (!preloadedImageUrl || !imagePreloaded) {
      return (
        <div className="tw-flex tw-justify-center tw-items-center tw-w-[320px] tw-h-[320px] tw-bg-gray-100 tw-rounded-full">
          <div className="tw-animate-spin tw-rounded-full tw-h-8 tw-w-8 tw-border-b-2 tw-border-primary-purple"></div>
        </div>
      );
    }

    return (
      <AvatarEditor
        ref={editorRef}
        image={preloadedImageUrl}
        crossOrigin="anonymous"
        width={320}
        height={320}
        border={20}
        color={[255, 255, 255, 0.6]}
        borderRadius={160}
        scale={1}
      />
    );
  }, [preloadedImageUrl, imagePreloaded]);

  // Memoized handler for cover image selection to prevent unnecessary re-renders
  const handleCoverImageSelect = useCallback((story) => {
    setSelectedCoverImage(story);
  }, []);

  // Preload images for selected stories to improve performance when user reaches step 2
  useEffect(() => {
    if (selectedStories.length > 0 && steps === 1) {
      selectedStories.forEach((story) => {
        const imageUrl = story?.thumbNailImage ?? story?.overlayImage;
        if (imageUrl) {
          const proxyUrl = createProxyImageUrl(imageUrl);
          const img = document.createElement("img");
          img.crossOrigin = "anonymous";
          img.src = proxyUrl; // This will cache the image
        }
      });
    }
  }, [selectedStories, steps]);

  // console.log(selectedStories);
  // reset Modal
  const resetModal = useCallback(() => {
    setIsOpen(false);
    setName("");
    setIsLoading(false);
    // setIsStoryVisible(false);
    setSelectedStories([]);
    setSelectedCoverImage(null);
    setImagePreloaded(false);
    setPreloadedImageUrl(null);
    // setSteps(0);
  }, [setIsOpen]);

  const handleSave = async () => {
    if (editorRef.current) {
      setIsLoading(true);
      const canvas = editorRef.current?.getImageScaledToCanvas();
      // console.log(canvas);
      const croppedImage = await canvas?.toDataURL(); // base64 image
      // console.log(croppedImage, "croppedImage");

      let payload = {};
      const finalImage = base64ToFile(croppedImage, "image");

      const formData = new FormData();
      formData.append("file", finalImage);

      try {
        const res = await updateImageToURL(formData);
        // setSelectedImage(res?.data?.[0]?.link);
        payload = {
          image: res?.data?.[0]?.link,
        };
      } catch (error) {
        safeToast.error(error?.message);
        return;
      }

      if (editData) {
        payload = {
          ...payload,
          title: name,
          AddStoriesIds: getDifference(
            editData?.HighlightedStories?.map((ele) => ele?.Story?.id),
            selectedStories?.map((ele) => ele?.id)
          ),
          RemoveStoriesIds: getDifference(
            selectedStories?.map((ele) => ele?.id),
            editData?.HighlightedStories?.map((ele) => ele?.Story?.id)
          ),
        };
        // Add the New Stories
        if (
          getDifference(
            editData?.HighlightedStories?.map((ele) => ({
              ...ele?.Story,
            })),
            selectedStories?.map((ele) => ele?.id)
          )
        ) {
        }
      } else {
        payload = {
          ...payload,
          title: name,
          StoryIds: selectedStories?.map((ele) => ele?.id),
        };
      }

      if (ProjectId) {
        payload = {
          ...payload,
          ProjectId,
        };
      }

      if (editData) {
        payload = {
          ...payload,
          id: editData?.id,
        };
      }

      const apiCall = editData ? updateHighlights : addHighlights;

      api.sendRequest(
        apiCall,
        (res) => {
          safeToast.success(res?.message);
          setRefresh((prev) => !prev);
          resetModal();
        },
        payload,
        "",
        (err) => {
          setIsLoading(false);
          safeToast.error(err?.message);
        }
      );

      // // uploadCoverImageHandler(payload);

      // // console.log(finalImage);
    }
  };

  useEffect(() => {
    if (isOpen) {
      setSteps(0);
      const user = authStorage.getProfileDetails();
      api.sendRequest(
        getUserStories,
        (res) => {
          setStoryList(res?.data?.data);
        },
        {
          id: user?.id,
        }
      );
      if (editData) {
        // console.log(editData);
        setName(editData?.title);
        setSelectedStories(
          editData?.HighlightedStories?.map((ele) => ({
            ...ele?.Story,
          }))
        );
      }
    } else {
      setStoryList([]);
    }
  }, [isOpen]);

  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-m-0 md:tw-m-[1.2rem] !tw-w-[35rem] !tw-rounded-[1.25rem] !tw-mt-1 !tw-px-0",
          closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
          },
        }}
        center
        focusTrapped={false}
        open={isOpen}
        onClose={() => {
          if (!isLoading) {
            resetModal();
          }
        }}
      >
        {isLoading && (
          <FullScreenLoader extraClassName="!tw-h-[100%] !tw-rounded-[1.25rem]" />
        )}
        <div className=" tw-font-medium tw-pb-2  tw-border-b tw-relative">
          {steps !== 0 && (
            <button
              type="button"
              onClick={() => {
                // if (selectedCoverImage) {
                //   setSelectedCoverImage(null);
                //   return;
                // }
                // setIsStoryVisible(false);
                setSteps((prev) => {
                  if (prev === 2) {
                    return 1;
                  }
                  if (prev === 1) {
                    return 0;
                  }
                });
              }}
              className="tw-absolute tw-left-3"
            >
              <ChevronLeft />
            </button>
          )}
          <p className="tw-text-center tw-text-xl tw-text-primary-black">
            {steps === 2
              ? "Select Cover"
              : steps === 1
              ? "Stories"
              : editData
              ? "Edit Highlight"
              : "New Highlight"}
          </p>
        </div>
        <div>
          {steps === 2 ? (
            <div className="tw-overflow-y-auto tw-max-h-[35rem] show-scrollbar">
              <div className="tw-flex tw-justify-center tw-items-center tw-py-4">
                {MemoizedAvatarEditor}
              </div>
              <div className="tw-grid tw-grid-cols-3 tw-gap-0.5 tw-overflow-y-auto tw-max-h-[18rem] show-scrollbar">
                {selectedStories?.map((ele) => (
                  <div
                    onClick={() => handleCoverImageSelect(ele)}
                    className={`tw-relative tw-w-full tw-h-full tw-cursor-pointer`}
                    key={ele?.id}
                  >
                    <div
                      className={`hover:tw-opacity-60 ${
                        selectedCoverImage?.id === ele?.id && "tw-opacity-60"
                      } `}
                    >
                      {renderStoryContent(ele, "!tw-h-[20rem]")}
                    </div>
                    <div className="tw-absolute tw-top-1.5 tw-right-3">
                      <input
                        className="tw-scale-150"
                        type="checkbox"
                        checked={selectedCoverImage?.id === ele?.id}
                        readOnly
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : steps === 1 ? (
            storyList.length > 0 ? (
              <div className="tw-grid tw-grid-cols-3 tw-gap-0.5 tw-overflow-y-auto tw-h-[35rem] show-scrollbar">
                {storyList?.map((ele) => (
                  <div
                    onClick={() => {
                      setSelectedStories((prev) => {
                        if (
                          prev?.filter((data) => data?.id === ele?.id)?.length >
                          0
                        ) {
                          return [
                            ...prev?.filter((data) => data?.id !== ele?.id),
                          ];
                        } else {
                          return [...prev, ele];
                        }
                      });
                    }}
                    className={`tw-relative tw-w-full tw-h-full `}
                    key={ele?.id}
                  >
                    <div className="tw-absolute tw-top-2 tw-left-2 tw-bg-white tw-p-2 tw-rounded-md tw-text-black tw-z-[999] tw-flex tw-flex-col tw-items-center tw-gap-0 tw-font-medium">
                      <p className="tw-font-semibold">
                        {convertUTCtoLocal(ele?.createdAt, "DD")}
                      </p>
                      <p className="-tw-mt-1">
                        {convertUTCtoLocal(ele?.createdAt, "MMM")}
                      </p>
                    </div>
                    <div
                      className={`hover:tw-opacity-60 ${
                        isExistInData(selectedStories, "id", ele?.id) &&
                        "tw-opacity-60"
                      } `}
                    >
                      {renderStoryContent(ele)}
                    </div>
                    <div className="tw-absolute tw-bottom-1.5 tw-right-3">
                      <input
                        className="tw-scale-150"
                        type="checkbox"
                        checked={isExistInData(selectedStories, "id", ele?.id)}
                        readOnly
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <>
                <div className="tw-flex tw-justify-center tw-items-center tw-h-[50dvh]">
                  <Empty
                    className="tw-text-xl tw-font-semibold "
                    label={"No Stories available!"}
                    iconRequired={false}
                  />
                </div>
              </>
            )
          ) : (
            <div className="tw-my-2 tw-mx-5 tw-py-2">
              <input
                type={"text"}
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                }}
                placeholder="Highlight Name" // Empty space to prevent browser autofill styling issues
                className="tw-w-full tw-text-primary-black tw-text-lg tw-bg-[#F1F2F3] tw-outline-none tw-py-3 tw-rounded-lg tw-ps-3"
              />
            </div>
          )}
        </div>
        <div className="tw-text-center tw-border-t tw-pt-3 -tw-mb-1">
          <button
            type="button"
            onClick={() => {
              setSteps((prev) => {
                if (prev === 0 && name) {
                  return 1;
                }
                if (prev === 1 && selectedStories.length > 0) {
                  if (selectedStories.length > 0) {
                    if (editData) {
                      setSelectedCoverImage({
                        overlayImage: editData?.image,
                      });
                    } else {
                      setSelectedCoverImage(
                        selectedStories[selectedStories.length - 1]
                      );
                    }
                  }
                  return 2;
                }

                if (prev === 2) {
                  handleSave();
                }

                return prev;
              });

              // if (name) {
              //   setIsStoryVisible(true);
              // }
            }}
            className={`tw-w-full tw-text-lg tw-font-semibold ${
              (steps === 0 && name) ||
              (steps === 1 && selectedStories.length > 0)
                ? "tw-text-primary-purple"
                : "tw-text-primary-black"
            }`}
          >
            {steps === 2 ? "Done" : "Next"}
          </button>
        </div>
      </Modal>
    </>
  );
};

export default AddHighlightModal;
