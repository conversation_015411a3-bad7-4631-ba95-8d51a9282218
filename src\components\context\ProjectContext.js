"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import { getOneProject } from "@/app/action";

export const ProjectContext = createContext();

export const useProject = () => {
    const context = useContext(ProjectContext);
    if (!context) {
        throw new Error("useProject must be used within a ProjectProvider");
    }
    return context;
};

export const ProjectProvider = ({ children, initialProject, projectId }) => {
    const [project, setProject] = useState(initialProject);
    const [error, setError] = useState(null);
    const api = useApiRequest(!initialProject)

    useEffect(() => {
        // only fetch if no initial project (client-side nav fallback)
        if (!initialProject) {
            const fetchProject = async () => {

                api.sendRequest(getOneProject, (res) => {
                    setProject(res?.data)
                }, {
                    id: projectId
                }, "",
                    (err) => {
                        setError(err)
                    })
            };

            fetchProject();
        }
    }, [initialProject]);

    return (
        <ProjectContext.Provider value={{ project, setProject, loading: api.isLoading, error }}>
            {children}
        </ProjectContext.Provider>
    );
};
