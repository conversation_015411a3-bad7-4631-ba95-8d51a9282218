"use client";

import { useContext, useEffect, useState } from "react";
import SidebarPage from "./SidebarPage";
import HeaderPage from "./HeaderPage";
import logo from "../../../public/images/logo/logo.png";
import Image from "next/image";
import { valContext } from "../context/ValContext";
import { Edit2Icon } from "lucide-react";
import { Collapsed2Icon } from "@/utils/icons";
import authStorage from "@/utils/API/AuthStorage";

export default function DashboardLayout({ children }) {
  const [isDesktopSidebarOpen, setIsDesktopSidebarOpen] = useState(true);
  const [searchValue, setSearchValue] = useState("");
  const { setGlobalSearch, setIsMobile, setLoginUserData } =
    useContext(valContext);

  useEffect(() => {
    if (sessionStorage.getItem("isMobileDevice")) {
      if (sessionStorage.getItem("isMobileDevice") === "true") {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
    } else {
      const updateIsMobile = () => {
        setIsMobile(window.innerWidth <= 1023);
        sessionStorage.setItem(
          "isMobileDevice",
          (window.innerWidth <= 1023)?.toString()
        );
      };
      updateIsMobile(); // Initialize on mount
      window.addEventListener("resize", updateIsMobile);
      return () => window.removeEventListener("resize", updateIsMobile);
    }
  }, []);

  useEffect(() => {
    const user = authStorage.getProfileDetails();
    setLoginUserData(user);
  }, []);

  return (
    <div className="tw-flex tw-flex-col lg:tw-flex-row  tw-h-full lg:tw-bg-gray-100 tw-bg-white  ">
      {/* Desktop Sidebar */}
      <SidebarPage
        isDesktopSidebarOpen={isDesktopSidebarOpen}
        setIsDesktopSidebarOpen={setIsDesktopSidebarOpen}
        setSearchValue={setSearchValue}
        setGlobalSearch={setGlobalSearch}
      />

      <div className="lg:tw-w-[calc(100%-16.75rem)] tw-bg-white tw-w-full ">
        <HeaderPage
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          setGlobalSearch={setGlobalSearch}
          isClose={false}
        />
        <main className="tw-bg-white tw-w-full tw-overflow-hidden">
          <div
            className={`"lg:!tw-max-w-[110vw]"
            } !tw-p-0 !tw-h-full custom-scrollbar`}
          >
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
