import { NextResponse } from "next/server";

export function middleware(req) {
    const token = req.cookies.get("SAID")?.value;
    const profileCookie = req.cookies.get("PROFILE")?.value;
    const profile = profileCookie ? JSON.parse(profileCookie) : null;
    const { pathname } = req.nextUrl;


    const protectedRoutes = ["/", "/account-review"];

    if (!token && protectedRoutes.includes(pathname)) {
        return NextResponse.redirect(new URL("/login", req.url));
    }

    if (pathname === "/send-otp") {
        const qParam = req.nextUrl.searchParams.get("q");
        const allowedQParams = ["forgot-password", "create-account"];
        if (!allowedQParams.includes(qParam)) {
            return NextResponse.redirect(new URL("/login", req.url));
        }
    }

    if (profile && pathname === "/forgot-password") {
        return NextResponse.redirect(new URL("/", req.url));
    }




    return NextResponse.next();
}

export const config = {
    matcher: [
        "/",
        "/login",
        "/register",
        "/send-otp",
        "/forgot-password",
        "/account-review",
        "/profile",
        "/projects"
    ],
};
