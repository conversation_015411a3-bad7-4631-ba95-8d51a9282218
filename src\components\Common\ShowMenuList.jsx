import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
const ShowMenuList = ({
  menuList = [],
  children,
  data,
  align = "end",
  extraClassName = "",
  extraOnclick = () => {},
}) => {
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
        <DropdownMenuContent
          align={align}
          className={`!tw-rounded-2xl !tw-px-0 !tw-pt-4 !tw-pb-1 ${extraClassName}`}
          onClick={(e) => {
            e.stopPropagation();
            extraOnclick();
          }}
        >
          {menuList?.map((ele) => (
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                ele.onClick(data);
              }}
              className="tw-mb-1.5 tw-px-5 tw-cursor-pointer"
              key={ele.label}
            >
              {ele?.element ? (
                ele?.element
              ) : (
                <div className=" tw-flex tw-gap-3 tw-items-center">
                  <div>{ele.icon}</div>
                  <p className={`tw-text-sm ${ele.className}`}>{ele.label}</p>
                </div>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default ShowMenuList;
