import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import useApiRequest from "../helper/hook/useApiRequest";
import { useFormik } from "formik";
import * as Yup from "yup";
import FloatingLabelInput from "../HomePage/Form/FloatingLabelInput";
import CustomButton from "../Common/Custom-Button";
import Project from "./FilterMenu/Project";
import SubProject from "./FilterMenu/SubProject";
import { useContext, useEffect, useRef, useState } from "react";
import { Calendar, ChevronDown, ChevronUp, X } from "lucide-react";
import FloatingLabelTextArea from "../HomePage/Form/FloatingLabelTextArea";
import {
  CloseModalIcon,
  DocIcon,
  PdfIcon,
  TodoUploadImageIcon,
} from "@/utils/icons";
import TeamMemberList from "./Component/TeamMemberList";
import { blurDataURL, RESPONSE_STATUS } from "@/utils/function";
import Image from "next/image";
import toast from "react-hot-toast";
import { addTodo, editTodo, updateImageToURL } from "@/app/action";
import dayjs from "dayjs";
import PrioritySelector from "./Component/PrioritySelector";
import { useRouter } from "nextjs-toploader/app";
import { safeToast } from "@/utils/safeToast";
import ModalProjects from "./Component/ModalProjects";
import { valContext } from "../context/ValContext";

const TodoModal = ({
  isInHomePage = false,
  isOpen,
  setIsOpen = () => {},
  editData,
  setEditData = () => {},
  setRefresh = () => {},
  reFetchData = () => {},
  defaultData = null,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isAddMore, setIsAddMore] = useState(false);
  const [selectedProject, setSelectedProject] = useState([]);
  const [selectedSubProject, setSelectedSubProject] = useState([]);
  const [selectedMember, setSelectedMember] = useState([]);
  const [selectedMenu, setSelectedMenu] = useState(null);
  const [docsList, setDocsList] = useState([]);
  const { isMobile } = useContext(valContext);
  const api = useApiRequest(false);
  const dateInputRef = useRef(null);
  const router = useRouter();
  // validationSchema;
  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().required("Task Name is required"),
  });

  const filterMenuRender = {
    projects: (
      <ModalProjects
        isOpen={isOpen}
        isMultiple={false}
        setSelectedMenu={setSelectedMenu}
        selectedProjects={selectedProject}
        setSelectedProjects={setSelectedProject}
      />
    ),
    subProjects: (
      <SubProject
        isOpen={isOpen}
        isMultiple={false}
        setSelectedMenu={setSelectedMenu}
        selectedProjects={selectedProject}
        selectedSubProjects={selectedSubProject}
        setSelectedSubProjects={setSelectedSubProject}
      />
    ),
    member: (
      <TeamMemberList
        isOpen={isOpen}
        isMultiple={false}
        setSelectedMenu={setSelectedMenu}
        selectedMember={selectedMember}
        setSelectedMember={setSelectedMember}
        projectId={
          selectedSubProject?.length > 0
            ? selectedSubProject?.[0]?.id
            : selectedProject?.length > 0
            ? selectedProject?.[0]?.id
            : null
        }
      />
    ),
  };

  // On Form Submit
  const onFormSubmitHandler = async (values) => {
    setIsLoading(true);
    let payload = {
      ...values,
      name: values?.name?.trim(),
      description:
        !values?.description || values?.description === ""
          ? null
          : values?.description?.trim(),
      dueDate: values?.dueDate
        ? dayjs(values?.dueDate).format("YYYY-MM-DD")
        : null,
    };

    payload = {
      ...payload,
      ProjectId: selectedProject?.[0]?.id ?? null,
    };
    if (selectedSubProject?.length > 0) {
      payload = {
        ...payload,
        ProjectId: selectedSubProject?.[0]?.id ?? null,
      };
    }
    payload = {
      ...payload,
      AssignedUserId: selectedMember?.[0]?.id ?? null,
    };

    if (docsList?.length > 0) {
      const formData = new FormData();

      docsList?.forEach((ele) => {
        if (ele?.src) {
          formData.append("file", ele?.src);
        }
      });
      // Convert File -> AWS URL
      if (formData.getAll("file").length > 0) {
        try {
          const res = await updateImageToURL(formData);
          if (res?.status !== 200 && res?.status !== RESPONSE_STATUS.SUCCESS) {
            throw res;
          }

          // console.log(res, "File response");
          payload = {
            ...payload,
            attachments: res?.data,
          };
        } catch (error) {
          console.dir(error, "error");
          safeToast.error(error?.message);
          // safeToast.error("Something went wrong");
          setIsLoading(false);
          return;
        }
      }
    } else {
      payload = {
        ...payload,
        attachments: null,
      };
    }
    // Edit Time and No New Files are Uploaded
    if (editData && !payload?.attachments) {
      payload = {
        ...payload,
        attachments: docsList?.map((ele) => ({
          link: ele?.link,
          originalname: ele?.name,
        })),
      };
    } else if (
      editData &&
      payload?.attachments &&
      payload?.attachments?.length > 0
    ) {
      payload = {
        ...payload,
        attachments: [
          ...payload?.attachments,
          ...docsList
            ?.filter((ele) => ele?.link)
            ?.map((ele) => ({
              link: ele?.link,
              originalname: ele?.name,
            })),
        ],
      };
    }

    if (editData) {
      payload = {
        ...payload,
        id: editData?.id,
      };
    }

    console.log(payload);

    api.sendRequest(
      editData ? editTodo : addTodo,
      (res) => {
        if (isInHomePage && !editData) {
          router.push("/to-dos");
        }
        setIsLoading(false);
        reFetchData();
        // setRefresh((prev) => !prev);
        setRefresh((prev) => (typeof prev === "undefined" ? true : !prev));
        resetModal();

        safeToast.success(res?.message);
      },
      payload,
      "",
      () => {
        setIsLoading(false);
      }
    );

    // console.log(payload);
  };

  const formik = useFormik({
    enableReinitialize: true, // Allows form values to update when `open` changes
    initialValues: {
      name: "",
      priority: 1,
    },
    validationSchema,
    onSubmit: async (values) => {
      // console.log("Updated Profile:", values);
      onFormSubmitHandler(values);
    },
  });

  const handleDocs = (e) => {
    if (docsList.length >= 3) {
      safeToast.error("You can only upload upto 3 files");
      return;
    }

    const file = e.target.files[0];
    e.target.value = null;

    if (!file) return;
    const allowedTypes = [
      "application/pdf",
      "application/msword", // .doc
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
    ];
    // console.log(file, !allowedTypes.includes(file.type));

    if (!allowedTypes.includes(file.type)) {
      safeToast.error(
        "Unsupported file format. Please select a .doc, .docx, or .pdf file."
      );
      return;
    }

    setDocsList((prev) => {
      const newId = prev.length > 0 ? prev[prev.length - 1]?.id + 5 : 1;
      return [...prev, { id: newId, name: file?.name, src: file }];
    });
    safeToast.success("File added successfully");
    // setSelectedImage(e.target.files[0]);
  };

  const projectValidationMessage = "Please Select Project First";

  // Reset Modal
  const resetModal = () => {
    setEditData(null);
    setSelectedProject([]);
    setSelectedSubProject([]);
    setSelectedMember([]);
    setIsOpen(false);
    setIsLoading(false);
    setIsAddMore(false);
    formik.resetForm();
    setDocsList([]);
  };

  useEffect(() => {
    if (editData) {
      formik.setFieldValue("name", editData?.name);
      if (editData?.dueDate) {
        formik.setFieldValue(
          "dueDate",
          dayjs(editData?.dueDate).format("YYYY-MM-DD")
        );
      }
      formik.setFieldValue("priority", editData?.priority);
      formik.setFieldValue("description", editData?.description ?? "");
      if (editData?.Files?.length > 0) {
        setDocsList(editData?.Files);
      }
      if (editData?.AssignedUser) {
        setSelectedMember([editData?.AssignedUser]);
      }
      if (editData?.Project?.ParentProject?.name && editData?.Project?.name) {
        setSelectedProject([editData?.Project?.ParentProject]);
        setSelectedSubProject([editData?.Project]);
      } else if (
        !editData?.Project?.ParentProject?.name &&
        editData?.Project?.name
      ) {
        setSelectedProject([editData?.Project]);
      }
    }
  }, [editData]);

  useEffect(() => {
    if (defaultData?.projectId) {
      setSelectedProject([
        {
          id: defaultData?.projectId,
        },
      ]);
    }
    if (defaultData?.subProjectId) {
      setSelectedSubProject([
        {
          id: defaultData?.subProjectId,
        },
      ]);
      setSelectedProject([
        {
          id: defaultData?.projectId,
        },
      ]);
    }
  }, [defaultData]);

  useEffect(() => {
    if (isOpen || editData) {
      setSelectedMenu(null);
    }
  }, [isOpen, editData]);
  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[95vw] !tw-w-full sm:!tw-max-w-[90vw] md:!tw-max-w-[80vw] lg:!tw-max-w-[40rem]   !tw-rounded-[1.25rem] !tw-mt-1 !tw-mx-2 sm:!tw-mx-4 md:!tw-mx-6 lg:!tw-mx-0",
          closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
            // maxWidth: "95vw",
            // width: "100%",
          },
        }}
        focusTrapped={false}
        open={editData ? editData !== null : isOpen}
        closeIcon={selectedMenu && <></>}
        onClose={() => {
          if (!isLoading) {
            resetModal();
          }
        }}
      >
        <div className="tw-py-3 tw-px-4 sm:tw-py-4 sm:tw-px-6 md:tw-py-5 md:tw-px-8 lg:tw-px-12 xl:tw-px-14">
          {selectedMenu ? (
            <>{filterMenuRender[selectedMenu]}</>
          ) : (
            <div>
              <p className="tw-text-xl md:tw-text-2xl tw-text-center tw-font-semibold ">
                {editData ? "Edit" : "Create"} To Do
              </p>
              <p className="tw-text-center tw-text-primary-black tw-font-light tw-text-base tw-mb-4">
                List your next To Do Item
              </p>
              {/* Form */}
              <div className="tw-h-[60vh] sm:tw-h-[65vh] md:tw-h-[70vh] lg:tw-h-[35rem] tw-overflow-auto tw-pr-2">
                <form onSubmit={formik.handleSubmit} className="tw-mt-4">
                  {/* Project Name */}
                  <FloatingLabelInput
                    label="Task Name*"
                    name="name"
                    formik={formik}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                      {formik.errors.name}
                    </p>
                  )}
                  {/* Optional Fields */}
                  <div
                    onClick={() => {
                      setIsAddMore((prev) => !prev);
                    }}
                    className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-py-2"
                  >
                    <p className="tw-text-primary-black tw-text-lg">
                      Add More (Optional)
                    </p>
                    {!isAddMore ? (
                      <ChevronDown size={20} />
                    ) : (
                      <ChevronUp size={20} />
                    )}
                  </div>

                  <div
                    className={`tw-overflow-hidden tw-transition-all tw-duration-300 ${
                      isAddMore
                        ? "tw-max-h-[2000px] tw-opacity-100"
                        : "tw-max-h-0 tw-opacity-0"
                    }`}
                  >
                    {/* Description */}
                    <FloatingLabelTextArea
                      label="Description"
                      name="description"
                      formik={formik}
                    />
                    <div className="tw-flex tw-justify-end tw-mb-4">
                      <p className="tw-text-sm tw-text-[#787E89]">
                        {formik.values.description?.length ?? 0}/250
                      </p>
                    </div>

                    <PrioritySelector
                      value={formik.values.priority}
                      setFieldValue={formik.setFieldValue}
                    />

                    {!defaultData && (
                      <button
                        type="button"
                        onClick={() => {
                          dateInputRef.current?.showPicker();
                        }}
                        className="tw-relative tw-overflow-hidden tw-w-full tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-mb-4 tw-bg-[#F1F2F3] tw-p-5 tw-rounded-2xl tw-text-primary-black"
                      >
                        <div>
                          <div
                            className={`tw-absolute tw-left-4 sm:tw-left-5 tw-transition-all tw-duration-200 tw-text-gray-500
      ${
        formik.values.dueDate ? "tw-top-2 tw-text-xs" : "tw-top-6 tw-text-base"
      }`}
                          >
                            Due Date
                          </div>
                          {formik.values.dueDate && (
                            <div className="tw-relative tw-top-1 tw-font-semibold tw-text-base tw-truncate">
                              {dayjs(formik.values.dueDate).format(
                                "MMMM DD, YYYY"
                              )}
                            </div>
                          )}
                        </div>
                        <input
                          name="dueDate"
                          value={formik.values.dueDate}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          ref={dateInputRef}
                          type="date"
                          min={dayjs().format("YYYY-MM-DD")}
                          className="tw-absolute tw-w-full tw-h-full tw-left-0 tw-top-0 tw-z-10 tw-opacity-0 tw-cursor-pointer"
                        />
                        <div>
                          <Calendar stroke="#2D394A" />
                        </div>
                      </button>
                    )}

                    {/* Project Selection */}
                    {!defaultData && (
                      <button
                        type="button"
                        onClick={() => setSelectedMenu("projects")}
                        className="tw-relative tw-w-full tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-mb-4 tw-bg-[#F1F2F3] tw-p-5 tw-rounded-2xl tw-text-primary-black"
                      >
                        <div>
                          <div
                            className={`tw-absolute tw-left-4 sm:tw-left-5 tw-transition-all tw-duration-200 tw-text-gray-500
      ${
        selectedProject?.length > 0
          ? "tw-top-2 tw-text-xs"
          : "tw-top-6 tw-text-base"
      }`}
                          >
                            Select Project
                          </div>
                          <div className="tw-relative tw-top-1 tw-font-semibold tw-text-base tw-truncate">
                            {selectedProject?.[0]?.name}
                          </div>
                        </div>
                        <div>
                          <ChevronDown stroke="#2D394A" />
                        </div>
                      </button>
                    )}
                    {/* Sub Project */}
                    {!defaultData && (
                      <button
                        type="button"
                        onClick={() => {
                          if (selectedProject.length > 0) {
                            setSelectedMenu("subProjects");
                          } else {
                            safeToast.error(projectValidationMessage);
                          }
                        }}
                        className={`tw-relative tw-w-full tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-mb-4 tw-bg-[#F1F2F3] tw-p-5 tw-rounded-2xl tw-text-primary-black ${
                          selectedProject?.length === 0 &&
                          "!tw-cursor-not-allowed"
                        }`}
                      >
                        <div>
                          <div
                            className={`tw-absolute tw-left-4 sm:tw-left-5 tw-transition-all tw-duration-200 tw-text-gray-500
      ${
        selectedSubProject?.length > 0
          ? "tw-top-2 tw-text-xs"
          : "tw-top-6 tw-text-base"
      }`}
                          >
                            Select Sub Project
                          </div>
                          <div className="tw-relative tw-top-1 tw-font-semibold tw-text-base tw-truncate">
                            {selectedSubProject?.[0]?.name}
                          </div>
                        </div>
                        <div>
                          <ChevronDown stroke="#2D394A" />
                        </div>
                      </button>
                    )}
                    {/* Team Member */}
                    <button
                      type="button"
                      onClick={() => {
                        if (selectedProject.length > 0) {
                          setSelectedMenu("member");
                        } else {
                          safeToast.error(projectValidationMessage);
                        }
                      }}
                      className={`${
                        selectedProject?.length === 0
                          ? "!tw-cursor-not-allowed"
                          : "tw-cursor-pointer"
                      } tw-relative tw-w-full tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-mb-4 tw-bg-[#F1F2F3] tw-p-5 tw-rounded-2xl tw-text-primary-black`}
                    >
                      <div>
                        <div
                          className={`tw-left-4 sm:tw-left-5 tw-transition-all tw-duration-200 tw-text-gray-500 tw-top-6 tw-text-base`}
                        >
                          Assign to a Team Member
                        </div>
                      </div>
                      <div>{/* <ChevronDown stroke="#2D394A" /> */}</div>
                    </button>
                    {selectedMember?.length > 0 && (
                      <div className="tw-flex tw-items-center tw-gap-2 tw-bg-[#F6EFFE] tw-max-w-full sm:tw-max-w-fit tw-px-3 tw-py-2 tw-rounded-lg tw-mb-4">
                        <div
                          className={`tw-relative tw-rounded-full tw-w-6 tw-h-6 sm:tw-w-[1.75rem] sm:tw-h-[1.75rem] tw-flex-shrink-0 ${
                            !selectedMember?.[0]?.image &&
                            "tw-bg-primary-purple tw-text-white"
                          }`}
                        >
                          {selectedMember?.[0]?.image ? (
                            <Image
                              src={selectedMember?.[0]?.image}
                              alt="user"
                              fill
                              className="!tw-rounded-full"
                              placeholder="blur"
                              blurDataURL={blurDataURL(300, 300)}
                            />
                          ) : (
                            <span className="tw-text-primary-1100 tw-font-merriweather tw-font-bold tw-text-lg md:tw-text-2xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                              {selectedMember?.[0]?.firstName?.charAt(0)}
                            </span>
                          )}
                        </div>
                        <div className="tw-flex tw-flex-col tw-items-start tw-min-w-0 tw-flex-1">
                          <p className="tw-font-medium tw-text-sm tw-not-italic !tw-text-primary-black tw-truncate tw-w-full">
                            {`${selectedMember?.[0]?.firstName ?? ""} ${
                              selectedMember?.[0]?.lastName ?? ""
                            }`}
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedMember([]);
                          }}
                          className="tw-flex-shrink-0 tw-p-1 tw-rounded tw-transition-colors hover:tw-bg-purple-200"
                        >
                          <X size={16} className="sm:tw-w-5 sm:tw-h-5" />
                        </button>
                      </div>
                    )}
                    {/* Upload Files */}
                    <div className="tw-relative tw-overflow-hidden tw-mb-4">
                      <div className="tw-mb-4 sm:tw-mb-6">
                        <p className="tw-text-primary-black tw-text-base md:tw-text-lg tw-font-semibold">
                          Upload Files
                        </p>
                        <p className="tw-text-secondary-text tw-text-sm">
                          (PDF, DOC | limit of 3 files)
                        </p>
                      </div>

                      {/* Upload Area */}
                      <div className="tw-border-2 tw-border-dashed tw-border-gray-300 tw-rounded-xl tw-p-4 sm:tw-p-6 tw-bg-gray-50 tw-transition-colors hover:tw-border-primary-purple hover:tw-bg-purple-50">
                        <div className="tw-cursor-pointer tw-relative tw-overflow-hidden tw-flex tw-flex-col tw-items-center tw-justify-center tw-text-center">
                          <div className="tw-mb-3 tw-scale-75 sm:tw-scale-90 md:tw-scale-100">
                            <TodoUploadImageIcon />
                          </div>
                          <p className="tw-text-base tw-text-gray-600 tw-mb-1">
                            Click to upload files
                          </p>
                          <p className="tw-text-sm tw-text-gray-500">
                            PDF, DOC, DOCX files only
                          </p>
                          <input
                            type="file"
                            accept=".doc, .docx, .pdf"
                            className="tw-absolute !tw-cursor-pointer tw-opacity-0 tw-inset-0 tw-w-full tw-h-full tw-z-10"
                            onChange={handleDocs}
                          />
                        </div>
                      </div>
                    </div>
                    {docsList?.length > 0 && (
                      <div className="tw-flex tw-pt-14 tw-gap-3 tw-justify-center tw-items-center">
                        {docsList?.map((ele) => (
                          <div
                            key={ele?.id}
                            className="tw-relative tw-flex tw-flex-col tw-items-center tw-bg-[#F1F2F3] tw-pt-5 tw-pb-4  tw-rounded-lg"
                          >
                            <button
                              type="button"
                              className="tw-absolute -tw-top-3 -tw-right-2"
                              onClick={() => {
                                setDocsList((prev) =>
                                  prev?.filter((data) => data?.id !== ele?.id)
                                );
                              }}
                            >
                              <CloseModalIcon
                                stroke="black"
                                fill="#d7d4b2"
                                size={isMobile ? 18 : 25}
                              />
                            </button>
                            <div className="tw-px-6">
                              {ele?.name?.includes("pdf") ? (
                                <PdfIcon
                                  height={isMobile ? 60 : 80}
                                  width={isMobile ? 40 : 60}
                                />
                              ) : (
                                <DocIcon
                                  height={isMobile ? 60 : 80}
                                  width={isMobile ? 40 : 60}
                                />
                              )}
                            </div>
                            <p className="tw-line-clamp-1 tw-pt-2 tw-max-w-[5rem] tw-font-light tw-text-sm tw-text-secondary-text ">
                              {ele?.name}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Submit Button */}
                  <div className="tw-flex tw-justify-center tw-mb-4 tw-mt-6 sm:tw-mt-8">
                    <CustomButton
                      loading={isLoading}
                      className={
                        "!tw-px-6 sm:!tw-px-9 !tw-py-3 sm:!tw-py-[14px] "
                      }
                      type="submit"
                      count={8}
                    >
                      <span className={"!tw-text-base"}>
                        {editData ? "Update" : "Create"}
                      </span>
                    </CustomButton>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default TodoModal;
