import { getOneProject } from "@/app/action";
import { ProjectProvider } from "@/components/context/ProjectContext";
import SingleProject from "@/components/ProjectPage/SingleProject/SingleProject";
import { getServerOrigin } from "@/utils/ServerFunctions";

const layout = async ({
    tabs,
    params
}) => {
    const { slug } = await params
    const subProjectData = await getOneProject({
        id: slug
    })
    const origin = await getServerOrigin();
    return (
        <>
            <ProjectProvider initialProject={subProjectData?.data} projectId={slug}>
                <SingleProject
                    origin={origin}
                    data={subProjectData?.data}
                    slug={slug}
                    isOnlyEditSubProject={true}
                    deleteData={{
                        label: "Delete Sub-Project",
                        subLabel: "Are you sure you want to Delete Sub-Project Permanently.",
                    }}
                    projectsTabs={[
                        {
                            label: "Posts",
                            value: "post",
                        },

                        {
                            label: "To Do",
                            value: "todos",
                        },
                        {
                            label: "Files",
                            value: "files",
                        },

                        {
                            label: "Members",
                            value: "members",
                        },
                        {
                            label: "Drafts",
                            value: "drafts",
                        },

                    ]}
                >
                    {tabs}
                </SingleProject>
            </ProjectProvider>

        </>
    );
}

export default layout;