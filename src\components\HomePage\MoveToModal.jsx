import {
  getFolders,
  getProject,
  getSubProjectsFromProjectIds,
} from "@/app/action";
import CustomButton from "@/components/Common/Custom-Button";
import Empty from "@/components/Common/Empty";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import ProjectSkeleton from "@/components/Loader/ProjectSkeleton";
import SubProjectsCard from "@/components/SubProjects/SubProjectsCard";
import authStorage from "@/utils/API/AuthStorage";
import { blurDataURL } from "@/utils/function";
import { CheckMarkIcon, LeftArrowBackIcon } from "@/utils/icons";
import { SearchIcon } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import SubProjectModal from "./Form/SubProjectModal";
import tempImg from "../../../public/images/assets/default_image.png";
import FolderCard from "../ProjectPage/SingleProject/Tabs/FilesComponent/FolderCard";
import FileSkeleton from "../Loader/FileSkeleton";

const MoveToModal = ({
  data = {},
  open,
  setIsOpen,
  onMoveHandler = () => {},
  forFile = false,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  // Use this state for sub project and folder list both
  const [subProjectList, setSubProjectList] = useState([]);
  const [subProjectPagination, setSubProjectPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [tempSelection, setTempSelection] = useState(null);
  const [tempSelectedProject, setTempSelectedProject] = useState(null);
  const [isSubProject, setIsSubProject] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDataFetchedFirstTime, setIsDataFetchedFirstTime] = useState(false);
  const api = useApiRequest();
  const projectListAPI = useApiRequest(false);
  const debounceRef = useRef(null);
  const resetModal = () => {
    setIsOpen(false);

    // Wait for modal close animation to finish before resetting state to avoid flicker
    setTimeout(() => {
      setDataList([]);
      setSubProjectList([]);
      setPagination({
        page: 1,
        limit: 20,
        total: 0,
      });
      setSubProjectPagination({
        page: 1,
        limit: 20,
        total: 0,
      });
    }, 300);
    setTempSelectedProject(null);
    setTempSelection(null);
    setSearchQuery("");
  };

  // On search
  const onSearch = (value) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = setTimeout(() => {
      setDataList([]);
      setPagination({
        page: 1,
        limit: 20,
        total: 0,
      });

      setSearchQuery(value);
    }, 500);
  };

  // Fetch Projects
  const fetchProjects = useCallback(async () => {
    // ✅ Set loading before API call
    const userDetails = authStorage.getProfileDetails();
    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
      UserId: userDetails?.id,
    };
    if (searchQuery) {
      queryParams = {
        ...queryParams,
        searchQuery,
      };
    }

    api.sendRequest(
      getProject,
      (res) => {
        const items = res?.data?.data?.filter(
          (ele) =>
            ele?.UserId === userDetails?.id ||
            ele?.ProjectMembers?.[0]?.access === "write"
        );
        if (items?.length > 0) setIsDataFetchedFirstTime(true);
        if (pagination.page === 1) {
          setDataList(items);
        } else {
          setDataList((prev) => [...prev, ...items]);
        }
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
      },

      queryParams
    );
  }, [pagination.page, searchQuery]);

  // Fetch Sub Projects
  const fetchSubProjects = useCallback(
    async (project) => {
      const userDetails = authStorage.getProfileDetails();

      // ✅ Set loading before API call
      let queryParams = {
        page: subProjectPagination.page,
        limit: subProjectPagination.limit,
        ParentId: project?.id,
        UserId: userDetails?.id,
      };

      // console.log(queryParams);
      try {
        await projectListAPI.sendRequest(
          getProject,
          (res) => {
            const items = res?.data?.data?.filter(
              (ele) =>
                project?.UserId === userDetails?.id ||
                ele?.UserId === userDetails?.id ||
                (ele?.ProjectMembers?.length > 0 &&
                  ele?.ProjectMembers?.[0]?.access === "write")
            );
            if (subProjectPagination.page === 1) {
              setSubProjectList(items);
            } else {
              setSubProjectList((prev) => [...prev, ...items]);
            }
            setSubProjectPagination((prev) => ({
              ...prev,
              total: res?.data?.totalRecords,
            }));
          },

          queryParams
        );
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        // ✅ Set loading false after API response (success or error)
      }
    },
    [subProjectPagination.page]
  );
  // Fetch Project Folder
  const fetchProjectFolders = useCallback(
    async (project) => {
      // ✅ Set loading before API call
      let queryParams = {
        ProjectId: project?.id,
      };

      // console.log(queryParams);
      try {
        projectListAPI.sendRequest(
          getFolders,
          (res) => {
            const items = res?.data?.data;
            if (subProjectPagination.page === 1) {
              setSubProjectList(items);
            } else {
              setSubProjectList((prev) => [...prev, ...items]);
            }
            setSubProjectPagination((prev) => ({
              ...prev,
              total: res?.data?.totalRecords,
            }));
          },

          queryParams
        );
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        // ✅ Set loading false after API response (success or error)
      }
    },
    [subProjectPagination.page]
  );

  // Fetch Projects
  useEffect(() => {
    if (open) {
      fetchProjects();
    }
  }, [pagination.page, open, searchQuery]);
  return (
    <>
      {/* Sub Project Modal */}
      <SubProjectModal
        isOnlyCreateSubProject
        open={isSubProject}
        setOpen={setIsSubProject}
        reFetchData={() => {
          setSubProjectList([]);
          setSubProjectPagination({
            page: 1,
            limit: 20,
            total: 0,
          });
          fetchSubProjects(tempSelectedProject);
        }}
        // setRefresh={setRefresh}
        modalTitle={"Create "}
        modalSubmitButton={"Save"}
        editProjectData={{
          ...tempSelectedProject,
        }}
      />
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[50rem] !tw-rounded-[1.25rem] !tw-m-0 md:!tw-m-2",
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        focusTrapped={false}
        open={open}
        onClose={() => resetModal()}
      >
        <div className="tw-py-5 tw-mx-6 lg:tw-mx-14 tw-relative">
          <div className="">
            {tempSelectedProject && (
              <button
                type="button"
                onClick={() => {
                  setTempSelection(null);
                  setSearchQuery("");
                  setTempSelectedProject(null);
                  setSubProjectList([]);
                  setSubProjectPagination({
                    page: 1,
                    limit: 20,
                    total: 0,
                  });
                  // setDataList([]);
                  // setPagination({
                  //   page: 1,
                  //   limit: 20,
                  //   total: 0,
                  // });
                  // fetchProjects();
                }}
                className="tw-absolute tw-top-6 tw-left-5"
              >
                <LeftArrowBackIcon />
              </button>
            )}
            <p className="tw-text-primary-black tw-font-semibold tw-text-2xl tw-text-center">
              {tempSelectedProject && forFile
                ? "Move to Folder"
                : "Move to Project"}
            </p>
            {/* <div /> */}
          </div>
          {/* Search */}
          {!tempSelectedProject && isDataFetchedFirstTime && (
            <div className="tw-flex tw-my-4 tw-gap-2 tw-items-center tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8]">
              <SearchIcon color="#787E89" />
              <input
                type="text"
                id="simple-search"
                className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500  `}
                autoComplete="off"
                placeholder="Search"
                onChange={(e) => {
                  onSearch(e.target.value);
                }}
              />
            </div>
          )}

          {/* data */}
          {tempSelectedProject ? (
            forFile ? (
              <>
                {projectListAPI.isLoading &&
                  subProjectPagination.page === 1 && (
                    <div className="tw-my-5 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
                      <FileSkeleton count={3} />
                    </div>
                  )}
                <div className="tw-my-5 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3  tw-gap-7 tw-overflow-y-auto  tw-w-full ">
                  {subProjectList?.map((ele) => (
                    <div
                      className="tw-cursor-pointer"
                      onClick={() => {
                        setTempSelection(ele);
                      }}
                      key={ele?.id}
                    >
                      <div className="tw-relative tw-overflow-hidden">
                        <FolderCard ele={ele} showAllSettings={false} />
                        {tempSelection?.id === ele?.id && (
                          <div className="tw-absolute tw-top-4 tw-right-4 tw-z-20">
                            <CheckMarkIcon size={22} />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                {!projectListAPI.isLoading && subProjectList?.length === 0 && (
                  <div className="tw-my-10">
                    <Empty
                      iconRequired={false}
                      label="No Folders in Project!"
                    />
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="tw-mt-4">
                  <div
                    className="tw-cursor-pointer tw-relative tw-flex tw-flex-col tw-items-center"
                    onClick={() => {
                      setTempSelection((prev) =>
                        prev?.id === tempSelectedProject?.id
                          ? null
                          : tempSelectedProject
                      );
                    }}
                  >
                    {tempSelection?.id === tempSelectedProject?.id && (
                      <div className="tw-absolute tw-left-[19.75rem] lg:tw-left-[27.25rem] tw-top-[0.4rem] tw-z-20">
                        <CheckMarkIcon size={22} />
                      </div>
                    )}
                    <div className="tw-relative tw-w-[19rem] tw-h-[12rem] tw-rounded-3xl">
                      <Image
                        fill
                        className={`!tw-rounded-3xl !tw-object-cover tw-border-2 ${
                          tempSelection?.id === tempSelectedProject?.id
                            ? "!tw-border-[#10BE5B]"
                            : "!tw-border-transparent"
                        }`}
                        src={tempSelectedProject?.image ?? tempImg}
                        alt={tempSelectedProject?.name ?? "sub-project"}
                        placeholder="blur"
                        blurDataURL={blurDataURL(290, 180)}
                      />
                      {/* <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
             
               {ele?.isPrivate && (
                 <div className="tw-absolute tw-bottom-2 tw-right-3 tw-bg-primary-purple tw-p-2 tw-rounded-full">
                   <PrivateProjectIcon size={20} />
                 </div>
               )}
             </div> */}
                    </div>

                    <p className="tw-text-2xl tw-font-bold tw-my-1 tw-break-all ">
                      {tempSelectedProject?.name ?? ""}
                    </p>
                  </div>

                  {/* </Link> */}
                </div>
                <p className="tw-text-center tw-text-xl tw-font-semibold tw-my-4">
                  or move to Sub-Project
                </p>
                {projectListAPI.isLoading &&
                  subProjectPagination.page === 1 && (
                    <div className="tw-grid tw-grid-cols-2 tw-gap-5 tw-mt-3">
                      <ProjectSkeleton count={4} />.
                    </div>
                  )}
                {subProjectList?.length > 0 ? (
                  <InfiniteScroll
                    height={250}
                    dataLength={subProjectList?.length ?? 0}
                    className="infinite-scrollbar"
                    hasMore={
                      Math.ceil(
                        subProjectPagination.total / subProjectPagination.limit
                      ) > subProjectPagination?.page
                    }
                    next={() =>
                      setSubProjectList((prev) => ({
                        ...prev,
                        page: prev.page + 1,
                      }))
                    }
                    loader={
                      <div className="tw-grid tw-grid-cols-2 tw-gap-5">
                        <ProjectSkeleton count={4} />.
                      </div>
                    }
                  >
                    <div className="tw-grid tw-grid-cols-2 tw-gap-5 ">
                      {subProjectList?.map((ele) => (
                        <div
                          className={`tw-relative `}
                          key={ele?.id}
                          onClick={() => {
                            // setTempSelection(ele);
                            setTempSelection((prev) =>
                              prev?.id === ele?.id ? null : ele
                            );
                          }}
                        >
                          {tempSelection?.id === ele?.id && (
                            <div className="tw-absolute tw-right-4 tw-top-2 tw-z-20">
                              <CheckMarkIcon size={22} />
                            </div>
                          )}
                          <SubProjectsCard
                            ele={ele}
                            countKey={null}
                            countType={null}
                            settingMenu={false}
                            imageClass={`tw-border-2 ${
                              tempSelection?.id === ele?.id
                                ? "!tw-border-[#10BE5B]"
                                : "!tw-border-transparent"
                            }`}
                          />
                        </div>
                      ))}
                    </div>
                  </InfiniteScroll>
                ) : (
                  <div className="tw-flex tw-justify-center tw-flex-col tw-items-center tw-pb-20">
                    {subProjectList?.length === 0 && (
                      <div className="tw-h-[10rem] tw-flex tw-justify-center tw-items-center">
                        <Empty
                          iconRequired={false}
                          label="No Sub-Project Found"
                        />
                      </div>
                    )}
                    <CustomButton
                      className={"!tw-px-9 !tw-py-[14px]"}
                      type="button"
                      count={8}
                      onClick={() => {
                        // console.log("here");
                        setIsSubProject(true);
                      }}
                    >
                      <span className={"!tw-text-base"}>
                        Create Sub-Project
                      </span>
                    </CustomButton>
                  </div>
                )}
              </>
            )
          ) : (
            <>
              {api.isLoading && pagination.page === 1 && (
                <div className="tw-grid tw-grid-cols-2 tw-gap-5 lg:tw-mt-5">
                  <ProjectSkeleton count={4} />.
                </div>
              )}
              {dataList?.length > 0 && (
                <InfiniteScroll
                  height={500}
                  dataLength={dataList?.length ?? 0}
                  className="infinite-scrollbar"
                  // hasMore={pagination.total > dataList?.length}
                  hasMore={
                    pagination.page <
                    Math.ceil(pagination.total / pagination.limit)
                  }
                  next={() =>
                    setPagination((prev) => ({
                      ...prev,
                      page: prev.page + 1,
                    }))
                  }
                  loader={
                    <div className="tw-grid tw-grid-cols-2 tw-gap-5">
                      <ProjectSkeleton count={4} />.
                    </div>
                  }
                >
                  <div className="tw-grid tw-grid-cols-2 tw-gap-5 ">
                    {dataList?.map((ele) => (
                      <div
                        className={`tw-relative `}
                        key={ele?.id}
                        onClick={() => {
                          //   setTempSelection(ele);
                          setTempSelectedProject(ele);
                          if (!forFile) {
                            fetchSubProjects(ele);
                          } else {
                            fetchProjectFolders(ele);
                          }
                        }}
                      >
                        <SubProjectsCard
                          ele={ele}
                          countKey="subProjectsCount"
                          countType="Sub-projects"
                          settingMenu={false}
                          imageClass={`tw-border-2 ${
                            tempSelection?.id === ele?.id
                              ? "!tw-border-[#10BE5B]"
                              : "!tw-border-transparent"
                          }`}
                        />
                      </div>
                    ))}
                  </div>
                </InfiniteScroll>
              )}
            </>
          )}
          {!api.isLoading && dataList?.length === 0 && (
            <div className="tw-flex tw-justify-center tw-items-center">
              <Empty
                label="Let's get started, create your project today!"
                className="tw-text-center"
              />
            </div>
          )}
          {!api.isLoading && tempSelection && (
            <div className="tw-w-full tw-flex tw-justify-center tw-absolute  tw-bottom-4 tw-z-30 ">
              <CustomButton
                className={"!tw-px-9 !tw-py-[14px]"}
                type="button"
                count={8}
                onClick={() => {
                  // console.log("here");
                  // setSelectedProject(tempSelection);
                  onMoveHandler(tempSelection, data);
                  resetModal();
                }}
              >
                <span className={"!tw-text-base"}>Move Here</span>
              </CustomButton>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default MoveToModal;
