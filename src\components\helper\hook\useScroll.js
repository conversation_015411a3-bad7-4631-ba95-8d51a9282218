import { useCallback, useState } from "react";

const useScroll = ({
    root = null,
    rootMargin = "0px",
    threshold = 0
} = {}) => {
    const [observer, setOserver] = useState();
    const [isIntersecting, setIntersecting] = useState(false);

    // console.log(root, threshold, 'root values')

    const measureRef = useCallback(
        (node) => {
            if (node) {
                const observer = new IntersectionObserver(
                    ([entry]) => {
                        setIntersecting(entry.isIntersecting);
                    },
                    { root, rootMargin, threshold }
                );

                observer.observe(node);
                setOserver(observer);
            }
        },
        [root, rootMargin, threshold]
    );

    return { measureRef, isIntersecting, observer };
};

export default useScroll;
