import Image from "next/image";
import logo from "../../../public/images/logo/logo-primary.svg";
import darkLogo from "../../../public/images/logo/logo.png";

const WebAppLogo = ({
  isDark = false,
  width = 100,
  height = 40,
  className = "",
}) => {
  return (
    <>
      <div className={`${className} tw-flex tw-justify-center tw-items-center`}>
        <Image
          src={isDark ? darkLogo : logo}
          alt="logo"
          width={width}
          height={height}
        />
      </div>
    </>
  );
};

export default WebAppLogo;
