"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import CustomTitle from "../Common/CustomTitle";
import CustomButton from "../Common/Custom-Button";
import {
  BookmarkFillOutIcon,
  BookmarkIcon,
  EditPencilIcon,
  HeartFillOutIcon,
  HeartIcon,
  MeassageFillOutIcon,
  MessageIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import ExplorePostList from "./ExplorePostList";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import { CustomContainer } from "../Common/Custom-Display";
import FormModal from "./FormModal";
import useApiRequest from "../helper/hook/useApiRequest";
import { getAllStories } from "@/app/action";
import Stories from "./Stories";
import StoriesSkeleton from "../Loader/StoriesSkeleton";

const HomePage = ({ origin }) => {
  const [storyList, setStoryList] = useState();
  const [isOpen, setIsOpen] = useState(false);
  const [refresh, setRefresh] = useState(false);
  // This state is when block the some user need to refetch the stores data
  const [reload, setReload] = useState(false);
  const api = useApiRequest();

  const handleStoryClick = (id) => {
    setStoryList((prevStories) =>
      prevStories.map((story) =>
        story.id === id ? { ...story, viewed: true } : story
      )
    );
  };

  useEffect(() => {
    api.sendRequest(getAllStories, (res) => {
      setStoryList(res?.data);
    });
  }, [reload]);

  return (
    <>
      <FormModal
        setRefresh={setRefresh}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
      />
      <CustomContainer className="tw-py-0 lg:tw-py-4">
        <div className="tw-flex  tw-justify-center tw-flex-col tw-items-start">
          {/* Story Section */}

          {api.isLoading ? (
            <StoriesSkeleton />
          ) : (
            <Stories storyList={storyList} />
          )}

          {/* Explore Post Section */}
          <div className="tw-w-full lg:tw-pt-6 lg:tw-pb-3 tw-flex tw-justify-between tw-items-center">
            <CustomTitle
              className="tw-hidden lg:tw-block"
              name="Explore Post"
            />
            <button
              type="button"
              onClick={() => setIsOpen((prev) => !prev)}
              className="tw-fixed lg:tw-static tw-bottom-[8.5rem] tw-right-4 tw-z-50 tw-bg-primary-purple tw-border-primary-purple tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white lg:!tw-px-5 lg:!tw-py-[1.1rem] tw-p-[1.1rem] !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold tw-group tw-transition-transform tw-duration-300 hover:tw-scale-105"
            >
              <EditPencilIcon className="tw-h-[20px] tw-w-[20px] tw-font-semibold" />
              <p className="tw-hidden lg:tw-block">Create</p>
            </button>
          </div>

          {/* Scrollable PostCard List */}
          <div className="tw-w-full  tw-rounded-[30px] 320:tw-rounded-[20px] tw-text-inherit">
            <div className="tw-inline-flex tw-flex-col tw-gap-5 tw-w-full">
              <ExplorePostList
                origin={origin}
                reload={refresh}
                setStoryRefresh={setReload}
              />
            </div>
          </div>
        </div>
      </CustomContainer>
    </>
  );
};

export default HomePage;
