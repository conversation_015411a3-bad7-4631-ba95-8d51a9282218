import { getOneUser } from "@/app/action";
import ProfileWrapper from "@/components/Profile/ProfileWrapper";

const UserLayout = async ({ children, tabs, params }) => {
  const { slug } = await params;
  let userData = {};
  // console.log(slug);
  try {
    const res = await getOneUser(slug);

    if (res?.status !== 200 && res?.status !== "success") {
      throw res;
    }
    userData = {
      ...res,
    };
  } catch (error) {
    // console.log(error, "error");
  }

  // console.log(slug);

  return (
    <>
      <ProfileWrapper
        profileTabs={[
          {
            label: "Posts",
            value: "post",
          },
          {
            label: "Projects",
            value: "projects",
          },
        ]}
        tabs={tabs}
        userIdSlug={slug}
        profileData={userData}
      />
    </>
  );
};

export default UserLayout;
