import { getOneProject } from "@/app/action";
import ProjectWrapper from "@/components/ProjectPage/ProjectWrapper";
import { getServerOrigin } from "@/utils/ServerFunctions";


export default async function layout({ tabs, params }) {
  const { slug } = await params
  const projectData = await getOneProject({
    id: slug
  })
  const origin = await getServerOrigin();

  // console.log("Project Data -----------> ", projectData)
  return (
    <>
      {/* Root layout that includes both children and tabs */}

      <ProjectWrapper origin={origin} tabs={tabs} projectData={projectData} slug={slug} />
    </>
  );
}
