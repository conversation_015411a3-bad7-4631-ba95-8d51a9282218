"use client";

import UserAvatar from "@/components/Common/UserAvatar";
import { blurDataURL, removeTargetAttributeFromLink } from "@/utils/function";
import Image from "next/image";
import Link from "next/link";
import moment from "moment";
import CustomTitle from "@/components/Common/CustomTitle";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ShowMenuList from "@/components/Common/ShowMenuList";
import PostImageCarousel from "../PostImageCarousel";
import {
  EditProfileIcon,
  HierarchyIcon,
  PinIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";

const MobilePostCard = ({
  data,
  postNavigation,
  path,
  postState,
  progress,
  isUserLinkActive,
  icons,
  setPostId,
  setType,
  otherProjectSetting,
  blockerUser,
  setRefresh,
  refetchData,
  blockImg,
  tempProjectImg,
  tempCommunityImg,
  isBlockUser,
  getPostDetails,
  setEditData,
  hasEditAccessOnly,
  myPostSetting,
  CommunityId,
  allSettings,
  userData,
  className = "",
  router,
  description,
}) => {
  return (
    <>
      <div
        onClick={() => {
          postNavigation(data?.slug);
        }}
        className={`tw-pt-3 tw-pb-8 tw-border-b tw-border-[#787e8957] lg:tw-hidden ${className}`}
      >
        <div className="tw-flex tw-justify-between tw-items-center tw-mb-2">
          <div className="tw-inline-block">
            <div
              onClick={(e) => {
                e.stopPropagation();
                progress.start(0, 1);
                if (isUserLinkActive) {
                  if (userData?.id === data?.User?.id) {
                    router.push(`/profile`);
                  } else {
                    router.push(`/user/${data?.User?.slug}`);
                  }
                }
              }}
              className={`${
                isUserLinkActive && "tw-cursor-pointer"
              } tw-flex tw-flex-row tw-items-center tw-gap-2`}
            >
              <UserAvatar
                imageUrl={data?.User?.image}
                userName={data?.User?.firstName}
                userNameClassName={"!tw-text-3xl"}
              />
              <div className="">
                <h1 className="tw-text-sm md:tw-text-base tw-font-semibold">{`${
                  data?.User?.firstName
                } ${data?.User?.lastName ? data?.User?.lastName : ""}`}</h1>
                {data?.Community && (
                  <div className="tw-flex tw-flex-col tw-gap-2 ">
                    <Link
                      href={`/communities/${data?.Community?.slug}`}
                      className="tw-flex tw-gap-2 tw-items-start"
                    >
                      <div className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px]  tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center">
                        <Image
                          src={data?.Community?.image ?? tempCommunityImg}
                          alt="Community"
                          fill
                          placeholder="blur"
                          blurDataURL={blurDataURL(20, 20)}
                          className="tw-object-cover tw-rounded-full"
                        />
                      </div>
                      <div>
                        <p className="tw-font-semibold tw-text-[13px]">
                          {data?.Community?.name}
                          {/* {data?.Project?.name} */}
                        </p>
                      </div>
                    </Link>

                    {data?.Project?.ParentProject && (
                      <Link
                        href={`/sub-project/${data?.Project?.slug}`}
                        className="tw-flex tw-gap-2 tw-items-center tw-relative tw-left-[1.8rem]"
                      >
                        <HierarchyIcon size={18} />
                        <p className="tw-font-semibold tw-text-[13px]">
                          {data?.Project?.name}
                          {/* {postData?.Project?.name} */}
                        </p>
                      </Link>
                    )}
                  </div>
                )}
                {data?.Project && (
                  <div className="tw-flex tw-flex-row tw-gap-1 tw-items-center">
                    <div
                      className={`tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px] ${
                        !data?.Project?.image &&
                        !data?.Project?.ParentProject?.image &&
                        "tw-bg-primary-purple"
                      } tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center`}
                    >
                      {
                        <Image
                          src={
                            data?.Project?.ParentProject?.image ??
                            data?.Project?.image ??
                            tempProjectImg
                          }
                          alt="Project"
                          fill
                          placeholder="blur"
                          blurDataURL={blurDataURL(20, 20)}
                          className="tw-object-cover tw-rounded-full"
                        />
                      }
                    </div>
                    <p className="tw-font-semibold tw-text-[13px]">
                      {data?.Project?.ParentProject?.name ??
                        data?.Project?.name}
                    </p>
                  </div>
                )}
                <p className="tw-text-primary-black tw-text-opacity-70 tw-text-[11px] md:tw-text-[13px]">
                  {moment(data?.createdAt)?.format("MMM DD, YYYY")}
                </p>
              </div>
            </div>
          </div>
          <div className="tw-flex tw-gap-2 tw-items-center">
            {postState?.pinPost &&
              (path.includes("/profile") ||
                path.includes("projects") ||
                path.includes("sub-project")) && (
                <div className="tw-bg-[#787e895c] tw-backdrop-blur-xl tw-p-2.5 exs:tw-p-2 tw-rounded-full">
                  <PinIcon stroke="#fff" size={15} />
                </div>
              )}
            <ShowMenuList
              data={data}
              menuList={
                userData?.id === data?.User?.id || allSettings
                  ? CommunityId
                    ? myPostSetting?.filter(
                        (ele) => ele.label === "Edit" || ele.label === "Delete"
                      )
                    : myPostSetting
                  : hasEditAccessOnly
                  ? [
                      {
                        label: "Edit",
                        className: "",
                        icon: <EditProfileIcon size={17} stroke="#2D394A" />,
                        onClick: (ele) => {
                          getPostDetails(ele?.id, setEditData);
                        },
                      },

                      ...otherProjectSetting,
                    ]
                  : isBlockUser
                  ? [
                      ...otherProjectSetting,
                      {
                        label: "Block User",
                        className: "tw-text-[#EF3B41]",
                        // icon: <LogOut stroke="#EF3B41" size={17} />,
                        icon: (
                          <div>
                            <Image
                              src={blockImg}
                              alt="block"
                              height={16}
                              width={16}
                            />
                          </div>
                        ),
                        onClick: (ele) => {
                          // getPostDetails(ele?.id, setEditData);
                          // console.log(ele?.User?.id);
                          blockerUser(ele?.User?.id, () => {
                            setRefresh((prev) => !prev);
                            refetchData();
                          });
                        },
                      },
                    ]
                  : otherProjectSetting
              }
            >
              <div className="tw-text-light-gray-700 xl:tw-text-base lg:tw-text-sm tw-text-12px tw-flex tw-justify-end tw-cursor-pointer tw-items-center">
                <ThreeDotMenuIcon />
              </div>
            </ShowMenuList>
          </div>
        </div>
        {/* Post Images */}
        {data?.media?.length > 0 && (
          <div className="tw-mt-2 tw-relative">
            <PostImageCarousel imagArr={data?.media} />
          </div>
        )}
        {/* Title */}
        <p className="tw-text-primary-black tw-mt-2 tw-text-base tw-line-clamp-2 tw-font-semibold">
          {data?.title}
        </p>
        {/* Description */}
        {description && (
          <div className="tw-flex tw-justify-between tw-gap-0.5 tw-items-end tw-mt-1.5">
            <div
              id="post-description"
              // tw-max-w-[21.5rem] exs:tw-max-w-[18rem]
              className="!tw-p-0  tw-line-clamp-1 tw-w-full  tw-break-all"
              dangerouslySetInnerHTML={{
                __html: description,
              }}
            />

            {/* <span
              onClick={(e) => {}}
              className="tw-text-incenti-purple tw-font-semibold tw-cursor-pointer"
            >
              see more
            </span> */}
          </div>
        )}
        {/* Icons */}

        <div className="tw-flex tw-justify-between tw-items-center tw-mt-6">
          <div className="tw-flex tw-gap-x-6">
            {icons
              ?.filter((ele) => ele?.name !== "Wishlist")
              ?.map((icon, index) => (
                <div key={index}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            icon.onClick(data?.id);
                          }}
                          className="tw-flex tw-gap-1 tw-items-center"
                        >
                          {icon?.icon}
                          {icon?.count && (
                            <span
                              onClick={(e) => {
                                e.stopPropagation();
                                if (icon?.name === "Like") {
                                  // console.log("hello");
                                  setPostId(data?.id);
                                  setType({
                                    isOpen: true,
                                    type: "User",
                                  });
                                }
                              }}
                              className=""
                            >
                              {icon?.count}
                            </span>
                          )}
                        </button>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>{icon?.name}</TooltipContent>
                  </Tooltip>
                </div>
              ))}
          </div>
          <div className="tw-flex tw-gap-x-6">
            {icons
              ?.filter((ele) => ele?.name === "Wishlist")
              ?.map((icon, index) => (
                <div key={index}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            icon.onClick(data?.id);
                          }}
                          className="tw-flex tw-gap-1 tw-items-center"
                        >
                          {icon?.icon}
                          {icon?.count && (
                            <span
                              onClick={(e) => {
                                e.stopPropagation();
                                if (icon?.name === "Like") {
                                  // console.log("hello");
                                  setPostId(data?.id);
                                  setType({
                                    isOpen: true,
                                    type: "User",
                                  });
                                }
                              }}
                              className=""
                            >
                              {icon?.count}
                            </span>
                          )}
                        </button>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>{icon?.name}</TooltipContent>
                  </Tooltip>
                </div>
              ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default MobilePostCard;
