"use client";
import { useState, useEffect } from "react";

const useScrollContent = (headings) => {
  const [activeId, setActiveId] = useState("");

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      for (const id of headings) {
        const element = document.getElementById(id);

        if (element) {
          const elementPosition = element.offsetTop - 10;
          const elementHeight = element.offsetHeight;

          if (
            scrollPosition >= elementPosition &&
            scrollPosition < elementPosition + elementHeight
          ) {
            setActiveId(`#${id}`);
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [headings]);

  return { activeId };
};

export default useScrollContent;
