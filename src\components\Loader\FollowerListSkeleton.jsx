import { Skeleton } from "../ui/skeleton";

const FollowerListSkeleton = ({ count = 4 }) => {
  return (
    <>
      {/* <div className="">
        <Skeleton className="tw-py-6 tw-my-4 !tw-rounded-full tw-w-[100%] " />
      </div> */}
      {Array.from({ length: count }, (_, i) => i + 1).map((ele) => (
        <div
          key={ele}
          className="tw-flex tw-items-center tw-justify-between  tw-mb-2"
        >
          <div
            key={ele}
            className="tw-flex tw-items-center tw-flex-row tw-gap-2 "
          >
            <Skeleton className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[48px] tw-h-[48px] tw-bg-gray-300 !tw-rounded-full tw-overflow-hidden tw-cursor-pointer" />
            <div className="">
              <Skeleton className="tw-text-base tw-font-semibold tw-h-4 tw-mt-0.5 tw-mb-2 tw-w-[10rem] " />

              {/* <Skeleton className="tw-h-4 tw-w-[11rem] " /> */}
            </div>
          </div>
          <div>
            <Skeleton className="tw-py-5 !tw-rounded-full tw-w-[6.5rem] " />
          </div>
        </div>
      ))}
    </>
  );
};

export default FollowerListSkeleton;
