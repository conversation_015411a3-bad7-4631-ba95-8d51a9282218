import {
  AddProjectIcon,
  DotMenuIcon,
  Edit2Icon,
  StoriesIcon,
} from "@/utils/icons";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import PostModal from "./Form/PostModal";
import ProjectModal from "./Form/ProjectModal";
import TodoModal from "../Todo/TodoModal";

const FormModal = ({
  isOpen,
  setIsOpen,
  setRefresh,
  isInHomePage = true,
  reFetchData = () => {},
}) => {
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  // console.log(openModal);
  const toggleFormModal = () => setIsOpen(false);
  const icons = [
    {
      id: 1,
      icon: <Edit2Icon size={26} />,
      label: "Write",
      onClick: () => {
        setOpenModal({
          isOpen: true,
          type: "post",
        });
      },
    },
    // {
    //   id: 2,
    //   icon: <StoriesIcon size={24} fill="#fff" />,
    //   label: "Stroy",
    //   onClick: () => {
    //     setOpenModal({
    //       isOpen: true,
    //       type: "story",
    //     });
    //   },
    // },
    {
      id: 3,
      icon: <DotMenuIcon size={24} stroke="#fff" />,
      label: "To Do",
      onClick: () => {
        setOpenModal({
          isOpen: true,
          type: "todos",
        });
      },
    },
    {
      id: 4,
      icon: <AddProjectIcon size={24} />,
      label: "Project",
      onClick: () => {
        setOpenModal({
          isOpen: true,
          type: "project",
        });
      },
    },
  ];
  const renderModal = {
    post: (
      <PostModal
        reFetchData={reFetchData}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        onlyForPost
        postForProject={true}
      />
    ),
    project: (
      <ProjectModal
        isInHomePage={isInHomePage}
        reFetchData={reFetchData}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        modalTitle={"Create Project"}
        modalSubmitButton={"Create"}
      />
    ),
    todos: (
      <TodoModal
        isInHomePage={isInHomePage}
        reFetchData={reFetchData}
        setRefresh={setRefresh}
        isOpen={openModal.isOpen}
        setIsOpen={setOpenModal}
      />
    ),
  };
  return (
    <>
      {openModal.type && renderModal[openModal.type]}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="!tw-w-[100%] lg:!tw-w-[30rem]  !tw-p-10 !tw-rounded-3xl">
          <DialogHeader>
            <DialogTitle className="!tw-text-center !tw-font-light !tw-text-3xl !tw-mx-auto !tw-max-w-[17rem]">
              What do you want to <span className="tw-font-bold">create?</span>
            </DialogTitle>
            <div className="tw-flex tw-gap-8 tw-flex-wrap tw-justify-between !tw-mx-auto !tw-max-w-[25rem] tw-py-5">
              {icons?.map((ele) => (
                <button
                  type="buttons"
                  onClick={() => {
                    ele.onClick();
                    toggleFormModal();
                  }}
                  key={ele.id}
                  className="tw-flex tw-flex-col tw-items-center tw-gap-2"
                >
                  <div className="tw-bg-primary-purple tw-p-6 tw-flex tw-justify-center tw-items-center tw-rounded-full">
                    {ele.icon}
                  </div>
                  <p className="tw-font-medium  tw-text-primary-black">
                    {ele.label}
                  </p>
                </button>
              ))}
            </div>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FormModal;
