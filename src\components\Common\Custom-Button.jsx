import React from "react";
import GlobalLoader from "../Loader/GlobalLoader";

const CustomButton = ({
  count = 1,
  loading,
  setLoading,
  className,
  children,
  ...props
}) => {
  // Case 1 -> Used in the Auth Form
  // Case 8 -> Drawer and with customization feature
  switch (count) {
    case 1:
      return (
        <button
          disabled={loading}
          className={`tw-mx-5 tw-px-10 tw-py-[1.1rem] lg:tw-py-[1.1rem] tw-justify-between tw-gap-5 tw-items-center  tw-flex tw-relative tw-overflow-hidden tw-font-bold tw-text-white tw-border tw-border-incenti-purple tw-bg-incenti-purple tw-active:tw-text-white tw-text-incenti-sm tw-h-auto tw-rounded-[50px] tw-group tw-transition-transform tw-duration-300 hover:tw-shadow-lg hover:tw-transform hover:tw-scale-105 ${
            loading && "tw-cursor-not-allowed tw-opacity-70"
          }`}
          {...props}
        >
          {loading && <GlobalLoader />}
          <div>
            <span className="tw-absolute tw-bottom-0 tw-left-0 tw-flex tw-w-full tw-h-0 tw-mb-0 tw-text-incenti-purple tw-opacity-100 group-hover:tw-h-full"></span>
            <span className="tw-relative tw-flex tw-items-center">
              {children}
            </span>
          </div>
        </button>
      );
    case 2:
      return (
        <button
          className="tw-w-full tw-rounded-full tw-bg-incenti-purple tw-px-6 tw-py-3 tw-text-white tw-transition hover:tw-bg-purple-700"
          {...props}
        >
          {children}
        </button>
      );
    case 3:
      return (
        <button
          className="tw-relative tw-inline-flex tw-items-center tw-justify-start tw-px-5 tw-py-2 tw-overflow-hidden tw-font-bold tw-rounded-lg tw-group tw-text-incenti-sm"
          {...props}
        >
          <span className="tw-w-32 tw-h-32 tw-rotate-45 tw-translate-x-12 tw--translate-y-2 tw-absolute tw-left-0 tw-top-0 tw-bg-incenti-purple tw-opacity-[3%]"></span>
          <span className="tw-absolute tw-top-0 tw-left-0 tw-w-48 tw-h-48 tw--mt-1 tw-transition-all tw-duration-500 tw-ease-in-out tw-rotate-45 tw--translate-x-56 tw--translate-y-24 tw-bg-incenti-purple tw-opacity-100 group-hover:tw--translate-x-8"></span>
          <span className="tw-relative tw-w-full tw-text-left tw-text-incenti-purple tw-transition-colors tw-duration-200 tw-ease-in-out group-hover:tw-text-white">
            {children}
          </span>
          <span className="tw-absolute tw-inset-0 tw-border tw-border-incenti-purple tw-rounded-lg"></span>
        </button>
      );
    case 4:
      return (
        <button
          className="custom-hover-slide-button tw-group tw-font-bold"
          {...props}
        >
          <span className="custom-hover-slide group-hover:tw-h-full"></span>
          <span className="custom-hover-slide-text group-hover:tw-text-white">
            {children}
          </span>
        </button>
      );
    case 5:
      return (
        <button
          className="custom-hover-slide-button !tw-bg-white !tw-text-incenti-orange tw-border !tw-border-incenti-orange tw-active:!tw-text-white tw-group tw-font-bold"
          {...props}
        >
          <span className="custom-hover-slide group-hover:tw-h-full !tw-bg-incenti-orange"></span>
          <span className="custom-hover-slide-text group-hover:tw-text-white">
            {children}
          </span>
        </button>
      );
    case 6:
      return (
        <button
          className="custom-hover-slide-button tw-group tw-xs:tw-w-full tw-font-bold"
          {...props}
        >
          <span className="custom-hover-slide group-hover:tw-h-full"></span>
          <span className="custom-hover-slide-text group-hover:tw-text-white">
            {children}
          </span>
        </button>
      );
    case 7:
      return (
        <button
          className="tw-text-incenti-purple tw-active:tw-text-white tw-active:tw-bg-incenti-purple tw-border tw-border-incenti-purple tw-py-1 tw-px-2 tw-transition tw-ease-in-out tw-duration-300 tw-text-incenti-sm tw-rounded-[35px] tw-active:tw-scale-[0.7] hover:tw-scale-110 tw-text-xs"
          {...props}
        >
          {children}
        </button>
      );
    case 8:
      return (
        <button
          disabled={loading}
          className={`${className} tw-px-10 tw-py-[1.1rem] tw-justify-between tw-gap-5 tw-items-center  tw-flex tw-relative tw-overflow-hidden tw-font-bold tw-text-white tw-border tw-border-incenti-purple tw-bg-incenti-purple tw-active:tw-text-white tw-text-incenti-sm tw-h-auto tw-rounded-[50px] tw-group tw-transition-transform tw-duration-300 hover:tw-shadow-lg hover:tw-transform hover:tw-scale-105 ${
            loading && "tw-cursor-not-allowed tw-opacity-70"
          }`}
          {...props}
        >
          {loading && <GlobalLoader />}
          <div>
            <span className="tw-absolute tw-bottom-0 tw-left-0 tw-flex tw-w-full tw-h-0 tw-mb-0 tw-text-incenti-purple tw-opacity-100 group-hover:tw-h-full"></span>
            <span className="tw-relative tw-flex tw-items-center">
              {children}
            </span>
          </div>
        </button>
      );
    default:
      return (
        <button
          disabled={loading}
          className={`tw-text-incenti-purple tw-active:tw-text-white tw-active:tw-bg-incenti-purple tw-border tw-border-incenti-purple tw-py-2 tw-px-5 tw-transition tw-ease-in-out tw-duration-300 tw-text-incenti-sm tw-font-bold tw-rounded-lg tw-active:tw-scale-[0.7] hover:tw-scale-110 ${
            loading && "tw-cursor-not-allowed tw-opacity-70"
          }`}
          {...props}
        >
          {/* {children} */}
          {loading && <GlobalLoader />}
          <div>
            <span className="tw-absolute tw-bottom-0 tw-left-0 tw-flex tw-w-full tw-h-0 tw-mb-0 tw-text-incenti-purple tw-opacity-100 group-hover:tw-h-full"></span>
            <span className="tw-relative tw-flex tw-items-center">
              {children}
            </span>
          </div>
        </button>
      );
  }
};

export default CustomButton;
