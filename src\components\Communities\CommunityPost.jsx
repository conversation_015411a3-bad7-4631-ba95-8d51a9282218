import { useEffect, useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  followUser,
  getAllPost,
  getCommentList,
  pinPost,
  unFollowUser,
  updatePostLike,
  updatePostWishList,
} from "@/app/action";
import { CustomGrid } from "../Common/Custom-Display";
import PostCard from "../HomePage/PostCard";
import InfiniteScroll from "../Common/InfiniteScroll";
import ExplorePostSkeleton from "../Loader/ExplorePostSkeleton";
import Empty from "../Common/Empty";
import { RESPONSE_STATUS } from "@/utils/function";
import toast from "react-hot-toast";
import { safeToast } from "@/utils/safeToast";

const CommunityPost = ({
  CommunityId,
  userId,
  dataList,
  setDataList,
  pagination,
  setPagination,
  userData,
  resetDataList,
  origin,
}) => {
  const [loadOff, setLoadOff] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const api = useApiRequest();

  // Update Like
  const updateLike = async (id) => {
    const res = await updatePostLike(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  const updateWishList = async (id) => {
    const res = await updatePostWishList(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };
  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };
  // Get Data
  const delayedSearch = async (CommunityId) => {
    // setIsLoading(true);
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      CommunityId,
    };

    if (Object.keys(query)?.length > 0) {
      query.sortOrder = "DESC";
    }

    try {
      api.sendRequest(
        getAllPost,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [...prev, ...res?.data?.data]);
            setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setDataList(() => [...res?.data?.data]);
            setLoadOff(false);
          } else {
            setLoadOff(true);
          }
          // setIsLoading
        },

        { ...query }
      );
    } catch (error) {
      console.error("Error fetching blog data:", error);
      // setScrollLoader(false);
    } finally {
      // setScrollLoader(false);
    }
    // setScrollLoader(false);
  };

  useEffect(() => {
    const fetchData = async (CommunityId) => {
      await delayedSearch(CommunityId);
    };

    if (CommunityId && userId) {
      fetchData(CommunityId);
    }
  }, [pagination.page, CommunityId, userId, refresh]);
  return (
    <>
      <div>
        <>
          <CustomGrid
            data={dataList}
            className="tw-gap-4 sm:tw-gap-8 "
            Component={({ data: dataProps }) => {
              return PostCard({
                data: { ...dataProps },
                titleForEditPostModal: "Post for Community",
                CommunityId,
                userData: userData,
                isUserLinkActive: true,
                origin: origin,
                isShowFollowButton: false,
                refetchData: resetDataList,
                setRefresh: setRefresh,
                getLikeData: async (id) => {
                  updateLike(id);
                },
                getWishlistData: async (id) => {
                  updateWishList(id);
                },
                getCommentData: async (
                  id,
                  setCommentList,
                  setIsCommentLoading
                ) => {
                  try {
                    const response = await getCommentList(id);
                    if (response?.status === RESPONSE_STATUS.SUCCESS) {
                      setCommentList(response?.data?.data);
                    } else {
                      throw response;
                    }
                  } catch (error) {
                    console.log(error, "error");
                    safeToast.error(error?.message);
                  } finally {
                    setIsCommentLoading(false);
                  }
                },
                followAndUnfollowHandler: (userId, isFollow) => {
                  followAndUnfollowHandler(userId, isFollow);
                },
                pinPostHandler: async (id, setState) => {
                  try {
                    const response = await pinPost(id);

                    if (
                      response?.status !== 200 &&
                      response?.status !== RESPONSE_STATUS.SUCCESS
                    ) {
                      throw response;
                    }
                    // safeToast.success(response?.message);
                    resetDataList();
                    setRefresh((prev) => !prev);
                  } catch (error) {
                    safeToast.error(error?.message);
                    // setState((prev) => !prev);
                    // setState((prev) => ({
                    //   ...prev,
                    //   pinPost: !prev?.pinPost,
                    // }));
                    return;
                  }
                },
              });
            }}
            xs={1}
            sm={1}
            md={1}
            lg={1}
            xl={1}
          />
          <InfiniteScroll
            threshold={90}
            loadMoreFunction={() => {
              if (
                Math.ceil(pagination.total / pagination.limit) > pagination.page
              ) {
                setPagination((prev) => ({
                  ...prev,
                  page: prev?.page + 1,
                }));
              }
            }}
            isLoading={api.isLoading}
            loadingComponent={<ExplorePostSkeleton count={2} />}
            timeout={10}
            loadOff={loadOff}
          />
        </>
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
            <Empty iconRequired={false} label="No Community Post available!" />
          </div>
        )}
      </div>
    </>
  );
};

export default CommunityPost;
