// components/VideoPlayer/HLSPlayer.jsx
"use client";
import { useEffect, useRef } from "react";
import Hls from "hls.js";
import Image from "next/image";

const HLSPlayer = ({ url, action, header, overlayImage, customHeader }) => {
  const videoRef = useRef(null);

  useEffect(() => {
    if (videoRef.current && Hls.isSupported()) {
      const hls = new Hls();
      hls.loadSource(url);
      hls.attachMedia(videoRef.current);

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        videoRef.current.play();
      });

      return () => {
        hls.destroy();
      };
    } else if (videoRef.current.canPlayType("application/vnd.apple.mpegurl")) {
      videoRef.current.src = url;
      videoRef.current.addEventListener("loadedmetadata", () => {
        videoRef.current.play();
      });
    }
  }, [url]);

  return (
    <div className="tw-relative tw-w-full tw-h-full ">
      {/* Use custom header if provided, otherwise fallback to default header */}
      {customHeader ||
        (header && (
          <div className="tw-absolute tw-top-2 tw-left-0 tw-w-full tw-flex tw-items-center tw-gap-2.5 tw-p-4 tw-z-10 tw-bg-gradient-to-b tw-from-black/60 tw-to-transparent">
            <div className="tw-relative tw-w-12 tw-h-12 tw-rounded-full">
              <Image
                src={header?.profileImage}
                alt="User"
                className=" !tw-rounded-full !tw-object-cover tw-border-2 tw-border-white"
                fill
              />
            </div>
            <div>
              <p className="tw-text-[#ffffffe6] tw-font-medium ">
                {header?.heading}
              </p>
              <p className="tw-text-[#fffc]  tw-text-[.6rem] ">
                {header?.subheading}
              </p>
            </div>
          </div>
        ))}
      {overlayImage && (
        <div className="tw-absolute tw-inset-0 tw-z-10">
          <Image
            src={overlayImage}
            alt="Overlay"
            fill
            className="tw-object-cover tw-pointer-events-none"
            priority
          />
        </div>
      )}
      <video
        ref={videoRef}
        className="tw-w-full tw-h-full tw-object-cover"
        onEnded={action}
      />
    </div>
  );
};

export default HLSPlayer;
