"use client";
import { InputOTP } from "antd-input-otp";
import { PencilIcon } from "lucide-react";
import React, { Suspense, useContext, useEffect, useState } from "react";
import CustomButton from "../Common/Custom-Button";
import CustomTitle from "../Common/CustomTitle";
import authStorage from "@/utils/API/AuthStorage";
import useApiRequest from "../helper/hook/useApiRequest";
import { useSearchParams } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import {
  forgotPasswordSend,
  signUpAPI,
  verifyForgotPasswordOTP,
  verifySignUpOTP,
} from "@/app/action";
import toast from "react-hot-toast";
import Link from "next/link";
import { valContext } from "../context/ValContext";
import FullScreenLoader from "../Loader/FullScreenLoader";
import { safeToast } from "@/utils/safeToast";
import { useProgress } from "@bprogress/next";
import Image from "next/image";
import logo from "../../../public/images/logo/logo-primary.svg";
import WebAppLogo from "../Common/WebAppLogo";

const VerifyOTP = ({ setActiveForm }) => {
  const router = useRouter();
  const progress = useProgress();
  const api = useApiRequest();
  const [otp, setOtp] = useState([]);
  const [timer, setTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // const email = "<EMAIL>";
  const [email, setEmail] = useState("");
  const [type, setType] = useState("");
  const searchParams = useSearchParams();
  const params = searchParams.get("q");
  const { setForgotEmail } = useContext(valContext);

  useEffect(() => {
    setEmail(localStorage.getItem("forgotemail"));
    setType(localStorage.getItem("type"));
  }, []);

  useEffect(() => {
    if (timer > 0 && !canResend) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else if (timer === 0) {
      setCanResend(true);
    }
  }, [timer]);

  const handleChange = (index, value) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 5) {
        const nextInput = document.querySelector(
          `input[name='otp-${index + 1}']`
        );
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleKeyDown = (index, e) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      const prevInput = document.querySelector(
        `input[name='otp-${index - 1}']`
      );
      if (prevInput) {
        prevInput.focus();
        const newOtp = [...otp];
        newOtp[index - 1] = "";
        setOtp(newOtp);
      }
    }
  };

  // Resend OTP Handler
  const handleResend = () => {
    const payload = {
      email: localStorage.getItem("forgotemail"),
    };
    if (params === "create-account") {
      payload.password = localStorage.getItem("password");
    }
    api.sendRequest(
      params === "create-account" ? signUpAPI : forgotPasswordSend,
      (res) => {
        authStorage?.setAuthDetails(res?.token);
        localStorage?.setItem("forgotemail", payload?.email);
        // authStorage.setAuthDetails(res?.token)
        safeToast.success(res?.message || "Successfully SignIn.");
        // actions?.setSubmitting(false);
        // router.push("/send-otp");
      },
      payload,
      "",
      (error) => {
        safeToast.error(error?.message || "Something went wrong!");
        // actions?.setSubmitting(false);
      }
    );
    setTimer(30);
    setCanResend(false);
    // console.log("Resending OTP...");
  };

  // Verify OTP Handler
  const handleVerify = async (ons) => {
    const otpString = otp.join("");
    if (otpString.length === 0) {
      safeToast.error("Please Enter OTP");
    } else if (otpString.length === 6) {
      setIsLoading(true);
      // console.log("Verifying OTP:", otpString);
      api.sendRequest(
        type === "signup" ? verifySignUpOTP : verifyForgotPasswordOTP,
        async (res) => {
          // setEmail(userDetail.email);
          // console.log(res)
          authStorage.deleteAuthDetails();
          authStorage.setAuthDetails(res?.token);
          localStorage.setItem("isNewUser", res?.isNewUser);
          // safeToast.success("Successfully verified OTP.");
          progress.start(0, 1);
          if (type === "signup") {
            // edit form pr redirection pending che
            // isNewUser: true
            localStorage.setItem("isNewUser", res?.isNewUser);
            authStorage.setAuthDetails(res?.token);
            // localStorage.setItem("token", res?.token);
            router.push("/create-profile");
            safeToast.success(
              "Your email is verified. Pleas complete your registration."
            );
            localStorage.setItem("type", "done");
          } else {
            router.push("/reset-password");
          }
          setIsLoading(false);
        },
        { OTP: otpString },
        "",
        (error) => {
          console.log(error, "here");
          safeToast.error(error?.message || "Something went wrong!");
          // setError(error?.german || "Something went wrong.")
          // actions.setSubmitting(false);
          setIsLoading(false);
          setOtp([]);
        }
      );
      // setActiveForm("resetPass")
    }
    // else {
    //     safeToast.error("Invalid OTP")
    // }
  };

  return (
    <Suspense>
      {isLoading && <FullScreenLoader />}
      <div className=" tw-flex tw-flex-col tw-items-center tw-justify-center  tw-p-4 tw-w-full">
        <div className="tw-w-full tw-max-w-md tw-space-y-6">
          <div className="tw-text-center tw-space-y-2">
            {/* <div className="lg:tw-hidden tw-flex tw-justify-center tw-items-center">
              <Image src={logo} alt="logo" width={110} height={40} />
            </div> */}
            <WebAppLogo className="lg:tw-hidden" />
            <CustomTitle
              name="Verify OTP"
              textClassName="lg:!tw-text-incenti-24 !tw-text-4xl"
              extraButton={
                <div className="tw-mx-4">
                  <p className="tw-text-[#666666] tw-mt-2 tw-font-normal tw-text-base ">
                    Enter the 6-digit OTP we sent to the email address{" "}
                    <span className="tw-text-[#1A1A1A] tw-font-medium tw-inline-flex tw-items-center">
                      {email}
                      <button
                        className="tw-ml-1 tw-text-[#7F56D9] hover:tw-text-[#6941C6] tw-transition-colors"
                        aria-label="Edit email"
                        onClick={() => {
                          progress.start(0, 1);
                          if (params === "forgot-password") {
                            router.push("/forgot-password");
                          } else {
                            router.push("/signup");
                          }
                          authStorage.deleteAuthDetails();
                          setForgotEmail(email);
                        }}
                      >
                        <PencilIcon className="tw-h-4 tw-w-4" />
                      </button>
                    </span>
                  </p>
                </div>
              }
              className="tw-py-4 lg:tw-py-0 tw-text-3xl tw-text-primary-black tw-font-bold tw-text-center tw-flex tw-flex-col"
            />
          </div>

          <div className="tw-space-y-6">
            <div className="tw-flex tw-gap-3 tw-justify-center">
              <InputOTP
                onChange={(value) => {
                  console.log(value);
                  setOtp(value);
                }}
                value={otp}
                // autoSubmit={handleVerify}
                inputType="numeric"
                length={6}
                inputClassName="focus:!tw-border-primary-purple"
              />
            </div>
            <div className="tw-w-full tw-flex tw-justify-center tw-items-center">
              <CustomButton
                // loading={isLoading}
                onClick={handleVerify}
              >
                Verify OTP
              </CustomButton>
            </div>

            <div className="tw-text-center tw-space-y-4">
              <div>
                Back to{" "}
                <Link
                  onClick={() => {
                    setForgotEmail("");
                    authStorage.deleteAuthDetails();
                  }}
                  href={type == "signup" ? "/signup" : "/login"}
                  className="tw-text-[#7F56D9] hover:tw-text-[#6941C6] hover:tw-underline tw-font-semibold tw-transition-colors tw-cursor-pointer"
                >
                  &nbsp;{type == "signup" ? "Sign Up" : "Sign In"}
                </Link>
              </div>

              <div className="tw-text-sm tw-text-[#666666]">
                Didn&apos;t receive the code?{" "}
                {canResend ? (
                  <button
                    onClick={handleResend}
                    className="tw-text-[#7F56D9] hover:tw-text-[#6941C6] tw-transition-colors tw-font-medium"
                  >
                    Resend
                  </button>
                ) : (
                  <span>
                    Resend in{" "}
                    <span className="tw-text-[#7F56D9] tw-font-medium">
                      {String(Math.floor(timer / 60)).padStart(2, "0")}:
                      {String(timer % 60).padStart(2, "0")}
                    </span>
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Suspense>
  );
};

export default VerifyOTP;
