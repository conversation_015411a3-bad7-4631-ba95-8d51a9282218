const FloatingLabelTextArea = ({
  label,
  name,
  formik,
  rows = 6,
  maxLength = 250,
}) => {
  return (
    <div className="tw-relative tw-mb-4 tw-bg-[#F1F2F3] tw-pt-5 tw-pb-2 tw-px-5 tw-rounded-2xl">
      <textarea
        name={name}
        id={name}
        value={formik.values[name]}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        placeholder=" " // Ensures placeholder-shown behavior for peer styles
        maxLength={maxLength}
        rows={rows}
        className="tw-peer tw-font-semibold tw-w-full tw-resize-none tw-text-primary-black tw-text-lg tw-bg-[#F1F2F3] tw-outline-none tw-pt-3 tw-pb-1"
      />
      <label
        htmlFor={name}
        className={`tw-absolute tw-left-5 tw-text-lg tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none
                  peer-placeholder-shown:tw-top-6 peer-placeholder-shown:tw-text-lg 
                  ${
                    formik.values[name]
                      ? "tw-top-2 tw-text-xs"
                      : "peer-focus:tw-top-2 peer-focus:tw-text-xs"
                  } `}
      >
        {label}
      </label>
    </div>
  );
};
export default FloatingLabelTextArea;
