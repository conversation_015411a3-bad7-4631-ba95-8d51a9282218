"use client";
import {
  EditProfileIcon,
  PrivateProjectIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import ShowMenuList from "../Common/ShowMenuList";
import Image from "next/image";
import { blurDataURL } from "@/utils/function";
import { Flag, Lock, Trash2 } from "lucide-react";
import useApiRequest from "../helper/hook/useApiRequest";
import { getOneProject, updateProject } from "@/app/action";
import ShareModal from "../Common/ShareModal";
import { useState } from "react";
import tempProjectImg from "../../../public/images/assets/default_image.png";

const MainProjectCard = ({
  ele,
  setIsDelete = () => {},
  setEditData = () => {},
  reFetchData = () => {},
  isMenuVisible = true,
  setHideProject = () => {},
  shareHandler = () => {},
  loginUserData,
  reportHandler = () => {},
}) => {
  const api = useApiRequest(false);

  const otherProjectSetting = [
    {
      label: "Share",
      className: "",
      icon: <ShareIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        // setIsShareOpen(true);
        shareHandler(ele);
      },
    },
    {
      label: "Report this Project",
      className: "",
      icon: <Flag size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        reportHandler(ele);
      },
    },
  ];

  const myProjectSetting = [
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        api.sendRequest(
          getOneProject,
          (res) => {
            // console.log(res);
            setEditData(res?.data);
          },
          ele
        );
      },
    },
    {
      label: `${!ele?.hide ? "Hide Private" : "Undo Hide"} Project`,
      className: "",
      icon: <Lock size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        setHideProject(ele);
      },
    },
    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        setIsDelete(ele);
      },
    },
  ];

  return (
    <>
      <div>
        {
          <div className="tw-relative tw-w-full tw-h-[12rem] tw-rounded-3xl">
            <Image
              fill
              className="!tw-rounded-3xl !tw-object-cover"
              src={ele?.image ?? tempProjectImg}
              alt={ele?.name}
              placeholder="blur"
              blurDataURL={blurDataURL(290, 180)}
            />
            <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
            {isMenuVisible && (
              <ShowMenuList
                data={ele}
                menuList={
                  // selectedCategory !== "created"
                  //   ? otherProjectSetting
                  //   : ele?.ProjectMembers?.[0]?.access === "write"
                  //   ? myProjectSetting?.filter((ele) => ele?.label === "Edit")
                  // : myProjectSetting?.filter((data) =>
                  //     ele?.isPrivate
                  //       ? data
                  //       : data?.label === "Edit" || data?.label === "Delete"
                  //   )
                  ele?.UserId === loginUserData?.id
                    ? myProjectSetting?.filter((data) =>
                        ele?.isPrivate
                          ? data
                          : data?.label === "Edit" || data?.label === "Delete"
                      )
                    : otherProjectSetting
                }
              >
                <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-4">
                  <ThreeDotMenuIcon fill="#fff" size={24} />
                </div>
              </ShowMenuList>
            )}
          </div>
        }
        {/* <p className="tw-text-primary-black tw-my-1">
          {+ele?.subProjectsCount ?? 0} Sub-projects
        </p> */}
        <div className="tw-flex tw-gap-2  tw-my-1">
          {ele?.isPrivate && (
            <div className="tw-bottom-2 tw-right-3 tw-bg-primary-purple tw-p-2 tw-rounded-full tw-h-full">
              <PrivateProjectIcon size={18} />
            </div>
          )}
          <p
            title={ele?.name}
            className="tw-text-2xl tw-font-bold tw-line-clamp-2 tw-break-all"
          >
            {ele?.name ?? ""}
          </p>
        </div>
      </div>
    </>
  );
};

export default MainProjectCard;
