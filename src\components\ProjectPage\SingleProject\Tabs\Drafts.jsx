"use client";
import Empty from "@/components/Common/Empty";
import { useEffect, useState } from "react";
import DraftsCard from "./DraftsComponent/DraftsCard";
import DraftSkeleton from "@/components/Loader/DraftSkeleton";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { getAllPost } from "@/app/action";
import { useProject } from "@/components/context/ProjectContext";
import authStorage from "@/utils/API/AuthStorage";
import PostModal from "@/components/HomePage/Form/PostModal";
import { Edit2Icon } from "@/utils/icons";

const Drafts = () => {
  const [dataList, setDataList] = useState([]);
  const [userData, setUserData] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  const [refresh, setRefresh] = useState(false);
  const api = useApiRequest();
  const { project } = useProject();

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };
  const fetchData = async (projectId) => {
    const user = authStorage.getProfileDetails();
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      ProjectId: +projectId,
      // UserId: +project?.UserId,
      isPublished: 0,
    };
    setUserData(user);
    if (Object.keys(query)?.length > 0) {
      query.sortOrder = "DESC";
    }

    try {
      api.sendRequest(
        getAllPost,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [...prev, ...res?.data?.data]);
            // setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setDataList(() => [...res?.data?.data]);
            // setLoadOff(false);
          } else {
            // setLoadOff(true);
          }
        },

        { ...query }
      );
    } catch (error) {
      console.error("Error fetching blog data:", error);
      // setScrollLoader(false);
    } finally {
      // setScrollLoader(false);
    }
    // setScrollLoader(false);
  };

  useEffect(() => {
    if (project) {
      fetchData(project?.id);
    }
  }, [pagination.page, project, refresh]);
  return (
    <>
      <PostModal
        reFetchData={resetDataList}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        postForProject={!project?.ParentProject}
        postForSubProject={
          project?.ParentProject &&
          Object.keys(project?.ParentProject).length > 0
        }
        formData={{
          ...(project ?? {}),
        }}
        // formData={
        //   project?.ParentProject
        //     ? {
        //         ...(project?.ParentProject ?? {}),
        //       // id: project?.id,
        //       }
        //     : {
        //         ...(project ?? {}),
        //       }
        // }
        isRemoveAble={false}
      />
      {/* Add Post */}
      {(project?.UserId === userData?.id ||
        project?.ProjectMembers?.[0]?.access === "write") && (
        <button
          type="button"
          onClick={() => {
            setOpenModal({
              isOpen: true,
              type: "post",
            });
          }}
          className="tw-fixed tw-bottom-16 tw-right-6 lg:tw-bottom-auto lg:tw-absolute lg:-tw-top-[6rem] lg:tw-right-[0rem] tw-z-50 tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
        >
          <Edit2Icon size={22} />
          <span className="tw-hidden lg:tw-inline">Create</span>
        </button>
      )}
      <div className="tw-mb-20 lg:tw-mb-0">
        {dataList?.map((ele) => (
          <div className="tw-mb-8" key={ele?.id}>
            <DraftsCard
              resetDataList={resetDataList}
              setRefresh={setRefresh}
              userData={userData}
              projectData={project}
              data={ele}
            />
          </div>
        ))}
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            if (
              pagination.page < Math.ceil(pagination.total / pagination.limit)
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<DraftSkeleton count={2} />}
          timeout={10}
          // loadOff={loadOff}
        />
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
            <Empty iconRequired={false} label="No Drafts available!" />
          </div>
        )}
      </div>
    </>
  );
};

export default Drafts;
