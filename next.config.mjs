/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    crossOrigin: "anonymous",
    images: {
        unoptimized: false,
        remotePatterns: [
            {
                protocol: "http",
                hostname: "*",
            },
            {
                protocol: "https",
                hostname: "*",
            },
            {
                protocol: "https",
                hostname: "cdn-dev-staging.incenti.ai",
            },
        ],
    },
    experimental: {
        serverActions: {
            bodySizeLimit: '2gb',
        },
        largePageDataBytes: 1024 * 1000 * 1000
    },
    async redirects() {
        return [

            {
                source: "/projects",
                destination: "/projects/created",
                permanent: true,
            },
            // {
            //     source: "/projects/[slug]",
            //     destination: "/project/[slug]",
            //     permanent: true,
            // },
            {
                source: "/communities",
                destination: "/communities/created",
                permanent: true,
            },

        ];
    },
    async rewrites() {



        //  Static Project rewrite rules
        const projectRouteRewrites = [

            {
                source: '/projects/created',
                destination: '/projects?category=created',
            },

            {
                source: '/projects/followed',
                destination: '/projects?category=followed',
            },

            {
                source: '/projects/explore',
                destination: '/projects?category=explore',
            },


        ];


        //  Static Community rewrite rules
        const communityRouteRewrites = [

            {
                source: '/communities/created',
                destination: '/communities?category=created',
            },

            {
                source: '/communities/joined',
                destination: '/communities?category=joined',
            },

            {
                source: '/communities/explore',
                destination: '/communities?category=explore',
            },


        ];


        return [
            ...projectRouteRewrites,
            ...communityRouteRewrites,
        ];
    },
    async headers() {
        return [
            {
                source: '/.well-known/apple-app-site-association',
                headers: [
                    {
                        key: 'Content-Type',
                        value: 'application/json',
                    },
                    { key: "Cache-Control", value: "public, max-age=3600", },
                ],
            },

        ]
    }
};

export default nextConfig;
