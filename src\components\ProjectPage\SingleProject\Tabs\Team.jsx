"use client";
import Empty from "@/components/Common/Empty";
import { useProject } from "@/components/context/ProjectContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { AddTeamIcon } from "@/utils/icons";
import { useEffect, useMemo, useState } from "react";
import TeamCard from "./TeamComponent/TeamCard";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import TeamSkeleton from "@/components/Loader/TeamSkeleton";
import {
  deleteTeamMember,
  followUser,
  getTeam,
  unFollowUser,
  updateTeamAccess,
} from "@/app/action";
import TeamModal from "./TeamComponent/TeamModal";
import authStorage from "@/utils/API/AuthStorage";
import toast from "react-hot-toast";
import PopUpModal from "@/components/Common/PopUpModal";
import { RESPONSE_STATUS } from "@/utils/function";
import { safeToast } from "@/utils/safeToast";
import { useRouter } from "nextjs-toploader/app";
import { useProgress } from "@bprogress/next";

const Team = ({ isSubProject = false }) => {
  const [dataList, setDataList] = useState([]);
  const [userData, setUserData] = useState(null);
  const [deleteTeamData, setDeleteTeamData] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [refresh, setRefresh] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  const api = useApiRequest();
  const singleApi = useApiRequest(false);
  const { project } = useProject();

  const router = useRouter();
  const progress = useProgress();
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // console.log(deleteTeamData);

  // Follow & Following Handler
  const followAndUnfollowHandler = async (
    userId,
    isFollow = true,
    setState
  ) => {
    let apiCall;

    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      // console.log(response);
      // toast.success(response?.message);
      if (
        response?.status === RESPONSE_STATUS.SUCCESS ||
        response?.status === 200
      ) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      setState((prev) => !prev);
      safeToast.error(error?.message);
    }
  };

  // Update Handler
  const updateAccessHandler = (payload) => {
    singleApi.sendRequest(
      updateTeamAccess,
      (res) => {
        safeToast.success(res?.message);
      },
      payload
    );
  };

  // Fetch Team Data
  useEffect(() => {
    const queryParams = {
      page: pagination.page,
      limit: pagination.limit,
      id: project?.id,
    };
    const loginUser = authStorage.getProfileDetails();
    setUserData(loginUser);
    api.sendRequest(
      getTeam,
      (res) => {
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
        if (res?.data?.length && pagination?.page > 1) {
          setDataList((prev) => [...prev, ...res?.data]);
        } else if (res?.data?.length && pagination.page === 1) {
          setDataList((prev) => [...res?.data]);
        } else {
        }
      },
      queryParams
    );
  }, [pagination.page, refresh]);

  const removeText = useMemo(() => {
    if (!project || !userData) return "Leave Team";
    return project.UserId === userData.id ? "Remove Team Member" : "Leave Team";
  }, [project?.UserId, userData?.id]);

  return (
    <>
      {/* Remove Team Member */}
      <PopUpModal
        isLoading={singleApi.isLoading}
        isOpen={deleteTeamData}
        setIsOpen={setDeleteTeamData}
        mainMessage={removeText}
        subMessage={`Are you sure you want to ${removeText} Permanently?`}
        onConfirm={() => {
          singleApi.sendRequest(
            deleteTeamMember,
            (res) => {
              // console.log(res, project.UserId === userData.id);
              safeToast.success(
                project.UserId === userData.id
                  ? res?.message
                  : project?.ParentProject
                  ? "Sub Project Left Successfully"
                  : "Project Left Successfully"
              );
              if (project.UserId !== userData.id) {
                progress.start();
                if (project?.ParentProject) {
                  router.push(`/projects/${project?.ParentProject?.slug}`);
                  return;
                }
                router.push("/projects");
                return;
              }
              setRefresh((prev) => !prev);
              resetDataList();
              setDeleteTeamData(null);
            },
            {
              id: `${deleteTeamData?.id}/${deleteTeamData?.UserId}`,
            },
            ""
          );
        }}
      />
      {/* Add Team */}
      <TeamModal
        isOpen={isAdd}
        setIsOpen={setIsAdd}
        isSubProject={isSubProject}
        setRefresh={setRefresh}
        refetchData={resetDataList}
        data={project}
        loginUser={userData}
      />
      {/* Add Users */}
      {(project?.UserId === userData?.id ||
        project?.ProjectMembers?.[0]?.access === "write") && (
        <button
          type="button"
          onClick={() => {
            setIsAdd(true);
          }}
          className="tw-fixed tw-bottom-16 tw-right-6 lg:tw-bottom-auto lg:tw-absolute lg:-tw-top-[6rem] lg:tw-right-[0rem] tw-z-50 tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
        >
          <AddTeamIcon size={20} />
          <span className="tw-hidden lg:tw-inline">Add</span>
        </button>
      )}
      {!api.isLoading && dataList.length === 0 && (
        <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Member available!" />
        </div>
      )}
      <div className="tw-mb-20 lg:!tw-my-5 tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
        {dataList?.map((ele) => (
          <div key={ele?.id}>
            <TeamCard
              updateAccessHandler={updateAccessHandler}
              ele={ele}
              projectData={project}
              loginUserData={userData}
              removeHandler={(ele) => {
                setDeleteTeamData(ele);
              }}
              followAndUnfollowHandler={followAndUnfollowHandler}
            />
          </div>
        ))}
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            // if (pagination.total > dataList?.length) {
            if (
              pagination.page < Math.ceil(pagination.total / pagination.limit)
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<TeamSkeleton count={6} />}
          timeout={10}
        />
      </div>
    </>
  );
};

export default Team;
