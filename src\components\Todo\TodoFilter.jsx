import { CheckMarkIcon } from "@/utils/icons";
import { ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import Priority from "./FilterMenu/Priority";
import toast from "react-hot-toast";
import SubProject from "./FilterMenu/SubProject";
import Project from "./FilterMenu/Project";
import CustomButton from "../Common/Custom-Button";
import { safeToast } from "@/utils/safeToast";

const TodoFilter = ({
  isOpen,
  setIsOpen = () => {},
  resetDataList = () => {},
  setRefresh = () => {},
  setFilterValues = () => {},
}) => {
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [selectedMenu, setSelectedMenu] = useState(null);
  const [selectedPriority, setSelectedPriority] = useState([]);
  const [selectedProjects, setSelectedProjects] = useState([]);
  const [selectedSubProjects, setSelectedSubProjects] = useState([]);

  const filterList = [
    // {
    //   label: "All to-dos",
    //   value: "all",
    // },
    {
      label: "My To Do",
      value: "my",
    },
    // {
    //   label: "Completed to-dos",
    //   value: "completed",
    // },
    {
      label: "Assigned to Others",
      value: "toFriends",
    },
    {
      label: "Assigned by Others",
      value: "byFriends",
    },
  ];

  const filterListTwo = [
    {
      label: "Priority",
      value: "priority",
    },
    {
      label: "Projects",
      value: "projects",
    },
    // {
    //   label: "Sub-Projects",
    //   value: "subProjects",
    //   isDisable: selectedProjects.length < 1,
    // },
  ];

  const filterValues = {
    all: {},
    my: {
      created: 1,
    },
    completed: {
      status: 1,
    },
    toFriends: {
      assignedToFriends: 1,
    },
    byFriends: {
      assignedToMe: 1,
    },
  };

  const filterHandler = () => {
    let valuesObj = {
      ...filterValues[selectedFilter],
    };

    if (selectedPriority.length > 0) {
      valuesObj = {
        ...valuesObj,
        priorities: selectedPriority?.toString(),
      };
    }
    if (selectedProjects.length > 0) {
      valuesObj = {
        ...valuesObj,
        ProjectIds: [...selectedProjects, ...selectedSubProjects]
          ?.map((ele) => ele?.id)
          ?.toString(),
      };
    }
    // if (selectedSubProjects.length > 0) {
    //   valuesObj = {
    //     ...valuesObj,
    //     ProjectIds: selectedSubProjects?.map((ele) => ele?.id)?.toString(),
    //   };
    // }

    resetDataList();
    setFilterValues(valuesObj);
    setIsOpen(false);
  };

  const secondFilterArrayValues = {
    priority: selectedPriority.length,
    projects: selectedProjects.length,
    subProjects: selectedSubProjects.length,
  };

  const filterMenuRender = {
    priority: (
      <Priority
        isOpen={isOpen}
        setSelectedMenu={setSelectedMenu}
        selectedPriority={selectedPriority}
        setSelectedPriority={setSelectedPriority}
      />
    ),
    projects: (
      <Project
        isOpen={isOpen}
        setSelectedMenu={setSelectedMenu}
        selectedProjects={selectedProjects}
        setSelectedProjects={setSelectedProjects}
        selectedSubProjects={selectedSubProjects}
        setSelectedSubProjects={setSelectedSubProjects}
      />
    ),
    // subProjects: (
    //   <SubProject
    //     isOpen={isOpen}
    //     setSelectedMenu={setSelectedMenu}
    //     selectedProjects={selectedProjects}
    //     selectedSubProjects={selectedSubProjects}
    //     setSelectedSubProjects={setSelectedSubProjects}
    //   />
    // ),
  };

  // When Reset/Close Modal
  const resetModal = () => {
    setIsOpen(false);
    setSelectedFilter("all");
    setSelectedMenu(null);
    setSelectedPriority([]);
    setSelectedProjects([]);
    setSelectedSubProjects([]);
  };

  useEffect(() => {
    if (isOpen) {
      setSelectedMenu(null);
    }
  }, [isOpen]);
  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[95vw] !tw-w-full sm:!tw-max-w-[90vw] md:!tw-max-w-[35rem] lg:!tw-max-w-[35rem] !tw-max-h-[90vh] sm:!tw-max-h-[85vh] md:!tw-max-h-[40rem] lg:!tw-h-auto !tw-rounded-[1.25rem] tw-overflow-visible !tw-mx-2 sm:!tw-mx-4",
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
            maxWidth: "95vw",
            width: "100%",
          },
        }}
        focusTrapped={false}
        closeIcon={selectedMenu && <></>}
        open={isOpen}
        onEscKeyDown={() => {
          setIsOpen(false);
          // setSelectedMenu(null);
        }}
        onClose={() => {
          //   if (!isLoading) {
          // resetModal();
          setIsOpen(false);
          // setSelectedMenu(null);
          //   }
        }}
      >
        <div className="tw-px-4 sm:tw-px-6 md:tw-px-7 tw-py-4 sm:tw-py-5 md:tw-py-6">
          {selectedMenu ? (
            <div className="">{filterMenuRender[selectedMenu]}</div>
          ) : (
            <div className="tw-flex tw-flex-col tw-h-full">
              <p className="tw-text-xl md:tw-text-2xl tw-text-center tw-font-bold tw-text-primary-black tw-mb-4 sm:tw-mb-6">
                Filter by
              </p>

              <div className="tw-flex-1 tw-space-y-1 sm:tw-space-y-2">
                {filterList?.map((ele) => (
                  <button
                    type="button"
                    className="tw-w-full tw-flex tw-justify-between tw-items-center tw-p-3 sm:tw-p-4 tw-rounded-lg tw-transition-colors hover:tw-bg-gray-50 tw-text-left"
                    key={ele?.value}
                    onClick={() => {
                      setSelectedFilter(ele?.value);
                    }}
                  >
                    <p
                      className={`tw-text-lg md:tw-text-xl tw-text-primary-black tw-font-medium tw-break-words tw-flex-1 tw-pr-3 ${
                        ele?.value === selectedFilter &&
                        "!tw-text-primary-purple"
                      }`}
                    >
                      {ele?.label}
                    </p>
                    {ele?.value === selectedFilter && (
                      <div className="tw-flex-shrink-0">
                        <svg
                          className="tw-text-primary-purple !tw-rounded-full"
                          width={18}
                          height={18}
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="3"
                          strokeLinecap="round"
                          viewBox="0 0 24 24"
                        >
                          <path d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                  </button>
                ))}
                {filterListTwo?.map((ele) => (
                  <button
                    type="button"
                    className="tw-w-full tw-flex tw-justify-between tw-items-center tw-p-3 sm:tw-p-4 tw-rounded-lg tw-transition-colors hover:tw-bg-gray-50 tw-text-left"
                    key={ele?.value}
                    onClick={() => {
                      if (
                        ele?.value === "subProjects" &&
                        selectedProjects?.length === 0
                      ) {
                        safeToast.error("Not selected Project");
                        return;
                      }
                      setSelectedMenu(ele?.value);
                    }}
                  >
                    <p
                      className={`tw-text-lg md:tw-text-xl tw-text-primary-black tw-font-medium tw-break-words tw-flex-1 tw-pr-3`}
                    >
                      {ele?.label}
                    </p>
                    <div className="tw-flex tw-gap-2 sm:tw-gap-3 tw-items-center tw-flex-shrink-0">
                      {secondFilterArrayValues[ele?.value] !== 0 && (
                        <div className="tw-bg-primary-purple tw-rounded-full tw-text-center tw-w-6 tw-h-6 sm:tw-w-7 sm:tw-h-7 md:tw-w-8 md:tw-h-8 tw-flex tw-items-center tw-justify-center tw-font-semibold tw-text-white tw-font-merriweather tw-text-xs sm:tw-text-sm">
                          {secondFilterArrayValues[ele?.value]}
                        </div>
                      )}
                      <div>
                        <ChevronRight className="tw-w-5 tw-h-5 sm:tw-w-6 sm:tw-h-6" />
                      </div>
                    </div>
                  </button>
                ))}
              </div>

              <div className="tw-flex tw-flex-col lg:tw-flex-row tw-my-10 tw-gap-5 tw-justify-center tw-items-center">
                <button
                  type="button"
                  className="tw-text-primary-black tw-font-semibold tw-order-2 lg:tw-order-1"
                  onClick={() => {
                    resetModal();
                    resetDataList();
                    setFilterValues({});
                  }}
                >
                  Clear All
                </button>
                <CustomButton
                  count={8}
                  type="button"
                  className={`lg:!tw-px-12 lg:!tw-py-[1.3rem] !tw-text-base tw-font-semibold tw-order-1 lg:tw-order-2`}
                  onClick={filterHandler}
                >
                  Apply
                </CustomButton>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default TodoFilter;
