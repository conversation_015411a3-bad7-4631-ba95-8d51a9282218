const FloatingLabelInput = ({ label, name, type = "text", formik, disabled = false }) => {
  return (
    <div className="tw-relative tw-mb-4 tw-bg-[#F1F2F3] tw-pt-5 tw-pb-2 tw-px-5 tw-rounded-2xl">
      <input
        type={type}
        name={name}
        id={name}
        disabled={disabled}
        value={formik.values[name]}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        placeholder=" " // Empty space to prevent browser autofill styling issues
        className="tw-peer tw-font-semibold tw-w-full tw-text-primary-black tw-text-lg tw-bg-[#F1F2F3] tw-outline-none tw-pt-3 tw-pb-1  tw-border-gray-300 focus:tw-border-primary"
      />
      <label
        htmlFor={name}
        className={`tw-absolute tw-left-5  tw-text-lg tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none 
                  peer-placeholder-shown:tw-top-6 peer-placeholder-shown:tw-text-lg 
                  ${
                    formik.values[name]
                      ? "tw-top-2 tw-text-xs"
                      : "peer-focus:tw-top-2 peer-focus:tw-text-xs"
                  }   `}
      >
        {label}
      </label>
    </div>
  );
};

export default FloatingLabelInput;
