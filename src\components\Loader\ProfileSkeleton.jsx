import { CustomContainer } from "../Common/Custom-Display";
import { Skeleton } from "../ui/skeleton";
import ExplorePostSkeleton from "./ExplorePostSkeleton";
const ProfileSkeleton = () => {
  return (
    <>
      <div>
        <CustomContainer className="tw-py-4 tw-mt-4 tw-h-full">
          <div className="tw-relative">
            <Skeleton className="tw-relative tw-w-full tw-h-[20rem] !tw-rounded-3xl" />
            <div className="tw-absolute tw-top-[77%] tw-left-[33%] 425:tw-left-[35%] md:tw-left-[40%] lg:tw-left-[44%] 2xl:tw-left-[45%]">
              <div
                className={`tw-relative tw-w-[8rem]  tw-h-[8rem] tw-rounded-full `}
              >
                <Skeleton
                  className={"tw-w-[8rem]  tw-h-[8rem] !tw-rounded-full"}
                />
              </div>
            </div>
          </div>

          <div className="tw-flex tw-flex-col tw-gap-3 tw-items-center tw-mt-16">
            {/* Name */}
            <Skeleton className=" !tw-rounded-xl tw-h-8 tw-w-[18rem]" />

            {/* Email */}
            {/* <Skeleton className="tw-h-4 tw-w-[15rem] " /> */}
            {/* Followers and Following Count */}
            <div className="tw-flex tw-gap-3 tw-justify-between tw-items-center tw-text-primary-black tw-font-bold">
              <Skeleton className="tw-h-4 tw-w-[10rem] tw-my-2" />
              <div className="tw-h-1 tw-w-1 tw-rounded-full tw-bg-primary-black" />
              <Skeleton className="tw-h-4 tw-w-[10rem] tw-my-2" />
            </div>
            {/* About */}
            <Skeleton className=" !tw-rounded-xl tw-h-5 tw-w-[27rem] lg:tw-w-[40rem]" />
            {/* Edit Profile */}
            <Skeleton className=" tw-h-14 tw-w-[8rem] !tw-rounded-full" />
          </div>

          {/* Highlights */}
          <div className="tw-w-full tw-overflow-hidden tw-block tw-my-8">
            <Skeleton className="tw-h-4 !tw-rounded-lg tw-w-[7rem] tw-mb-3" />
            <div className="tw-flex tw-gap-4 tw-items-center">
              {Array.from({ length: 7 }, (_, i) => i + 10)?.map((ele) => (
                <div key={ele}>
                  <Skeleton className={"tw-h-20 tw-w-20 !tw-rounded-full "} />
                  <Skeleton className={"tw-h-3 tw-w-[5rem] tw-mt-2"} />
                </div>
              ))}
            </div>
          </div>
          {/* Tabs */}
          <div className="tw-flex tw-justify-between tw-items-center tw-mb-8">
            <div className="tw-flex  tw-items-center tw-gap-4 ">
              {Array.from({ length: 4 }, (_, i) => i + 1)?.map((ele) => (
                <Skeleton
                  type="button"
                  key={ele}
                  className={`tw-py-6 tw-cursor-pointer !tw-rounded-full tw-px-12`}
                />
              ))}
            </div>
            {/* <Skeleton
              className={`tw-py-7 tw-cursor-pointer !tw-rounded-full tw-px-14`}
            /> */}
          </div>
          {/* <ExplorePostSkeleton /> */}
        </CustomContainer>
      </div>
    </>
  );
};

export default ProfileSkeleton;
