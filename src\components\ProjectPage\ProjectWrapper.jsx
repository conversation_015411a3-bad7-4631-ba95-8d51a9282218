"use client";

import { ProjectProvider } from "../context/ProjectContext";
import SingleProject from "./SingleProject/SingleProject";

const ProjectWrapper = ({ tabs, projectData, slug, origin }) => {
  return (
    <>
      <ProjectProvider initialProject={projectData?.data} projectId={slug}>
        <SingleProject origin={origin} data={projectData?.data} slug={slug}>
          {/* Tabs */}
          {projectData?.data && tabs}
        </SingleProject>
      </ProjectProvider>
    </>
  );
};

export default ProjectWrapper;
