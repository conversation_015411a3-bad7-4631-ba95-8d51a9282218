import {
  getProject,
  getSubProjectsFromProjectIds,
  getTeam,
  getTeamUsers,
} from "@/app/action";
import Empty from "@/components/Common/Empty";
import UserAvatar from "@/components/Common/UserAvatar";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import SelectProjectSkeleton from "@/components/Loader/SelectProjectSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { blurDataURL } from "@/utils/function";
import {
  CloseModalIcon,
  LeftArrowBackIcon,
  NoMemberFound,
} from "@/utils/icons";
import { SearchIcon } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const TeamMemberList = ({
  isOpen,
  selectedMember,
  setSelectedMember,
  setSelectedMenu,
  isMultiple = true,
  projectId,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [tempSelected, setTempSelected] = useState([]);
  const [isDataFetchFirstTime, setIsDataFetchFirstTime] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [search, setSearch] = useState("");
  const debounceRef = useRef(null);
  const api = useApiRequest();

  // console.log(dataList, "Project Id", tempSelected);

  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const onSearch = (value) => {
    setSearch(value);
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = setTimeout(() => {
      resetData();
      setSearchQuery(value);
    }, 500);
  };

  const handleSelect = (value) => {
    setTempSelected((prev) => {
      if (prev?.find((ele) => ele?.id === value?.id)) {
        return isMultiple ? prev?.filter((item) => item?.id !== value?.id) : [];
      } else if (isMultiple) {
        return [...prev, value];
      } else {
        return [value];
      }
    });
  };

  // Fetch Projects
  const fetchTeamMember = useCallback(async () => {
    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
      id: projectId,
    };
    if (searchQuery) {
      queryParams = {
        ...queryParams,
        searchQuery,
      };
    }

    api.sendRequest(
      getTeam,
      (res) => {
        if (res?.data?.length > 0) {
          setIsDataFetchFirstTime(true);
        }
        if (pagination.page === 1) {
          setDataList(res?.data?.filter((ele) => ele?.status));
        } else {
          setDataList((prev) => [
            ...prev,
            ...res?.data?.filter((ele) => ele?.status),
          ]);
        }
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
      },

      queryParams
    );
  }, [pagination.page, searchQuery]);

  useEffect(() => {
    setTempSelected(selectedMember ?? []);
  }, [selectedMember]);

  useEffect(() => {
    fetchTeamMember();
  }, [pagination.page, searchQuery]);

  useEffect(() => {
    resetData();
  }, [isOpen]);

  return (
    <>
      <div className="tw-flex tw-justify-between tw-items-center">
        <button
          type="button"
          onClick={() => {
            setSelectedMenu(null);
          }}
          className=""
        >
          <LeftArrowBackIcon />
        </button>
        <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
          Assign Friends
        </p>
        <button
          type="button"
          className="tw-text-primary-purple tw-font-bold"
          onClick={() => {
            setSelectedMember(tempSelected);
            setSelectedMenu(null);
            setIsDataFetchFirstTime(false);
            resetData();
          }}
        >
          Done
        </button>
      </div>

      <div className="tw-my-2">
        {isDataFetchFirstTime && (
          <div className="tw-flex tw-mt-4 tw-gap-2 tw-items-center tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8]">
            <SearchIcon color="#787E89" />
            <input
              type="text"
              id="simple-search"
              className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500  `}
              autoComplete="off"
              placeholder="Search"
              value={search}
              onChange={(e) => {
                onSearch(e.target.value);
              }}
            />
            {search && search !== "" && (
              <button
                onClick={() => {
                  setSearch("");
                  setSearchQuery("");
                  resetData();
                }}
                className="tw-ml-2"
              >
                <CloseModalIcon size={16} />
              </button>
            )}
          </div>
        )}
        {api.isLoading && pagination.page === 1 && <SelectProjectSkeleton />}
        {dataList?.length > 0 && (
          <InfiniteScroll
            height={405}
            dataLength={dataList?.length ?? 0}
            // show-scrollbar css class to show the scroll bar
            className="infinite-scrollbar tw-px-1"
            hasMore={
              Math.ceil(pagination.total / pagination.limit) > pagination.page
            }
            next={() =>
              setPagination((prev) => ({
                ...prev,
                page: prev.page + 1,
              }))
            }
            loader={<SelectProjectSkeleton count={3} />}
          >
            {dataList.map((ele) => (
              <button
                type="button"
                className="tw-my-3 tw-w-full tw-flex tw-justify-between tw-items-center"
                key={ele.id}
                onClick={() => {
                  handleSelect(ele?.User);
                }}
              >
                <div className="tw-flex tw-items-center tw-gap-3 ">
                  {/* <div
                    className={`tw-relative tw-rounded-full tw-w-[3.25rem] tw-h-[3.25rem] ${
                      !ele?.User?.image && "tw-bg-primary-purple tw-text-white "
                    }`}
                  >
                    {ele?.User?.image ? (
                      <Image
                        src={ele?.User?.image}
                        alt="user"
                        fill
                        className="!tw-rounded-full !tw-object-cover"
                        placeholder="blur"
                        blurDataURL={blurDataURL(300, 300)}
                      />
                    ) : (
                      <span className="tw-text-primary-1100  tw-font-merriweather tw-font-bold md:tw-text-2xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                        {ele?.User?.firstName?.charAt(0)}
                      </span>
                    )}
                  </div> */}
                  <UserAvatar
                    imageUrl={ele?.User?.image}
                    userName={ele?.User?.firstName}
                  />
                  <div className="tw-flex tw-flex-col tw-items-start">
                    <p className="tw-font-bold tw-text-lg tw-not-italic !tw-text-primary-black">
                      {`${ele?.User?.firstName ?? ""} ${
                        ele?.User?.lastName ?? ""
                      }`}
                    </p>
                    {/* <p className="tw-text-sm  tw-max-w-[15.5rem] tw-break-words tw-not-italic !tw-text-[#787E89]">
                      {ele?.email ?? ""}
                    </p> */}
                  </div>
                </div>
                <div>
                  {!!tempSelected?.find(
                    (data) => data?.id === ele?.User?.id
                  ) && (
                    <div>
                      <svg
                        className=" tw-text-primary-purple !tw-rounded-full"
                        width={22}
                        height={22}
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="3"
                        strokeLinecap="round"
                        viewBox="0 0 24 24"
                      >
                        <path d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  )}
                </div>
              </button>
            ))}
          </InfiniteScroll>
        )}
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[20rem]">
            <Empty
              icon={searchQuery && <NoMemberFound size={50} />}
              iconRequired={searchQuery}
              label={
                searchQuery ? "Search Result Not Found!" : "No team member"
              }
              subLabel={
                searchQuery
                  ? ""
                  : " No team members assigned yet to this project."
              }
            />
          </div>
        )}
      </div>
    </>
  );
};

export default TeamMemberList;
