"use client";
import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "nextjs-toploader/app";
import useSWRInfinite from "swr/infinite";
import {
  getAllExplorePost,
  updatePostLike,
  updatePostWishList,
  followUser,
  unFollowUser,
  getCommentList,
  pinPost,
  editPost,
  blockUserAPI,
} from "@/app/action";
import { CustomGrid } from "../Common/Custom-Display";
import PostCard from "./PostCard";
import Empty from "../Common/Empty";
import InfiniteScroll from "../Common/InfiniteScroll";
import ExplorePostSkeleton from "../Loader/ExplorePostSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { RESPONSE_STATUS } from "@/utils/function";
import toast from "react-hot-toast";
import { valContext } from "../context/ValContext";
import { safeToast } from "@/utils/safeToast";
import { NoMemberFound } from "@/utils/icons";

const SCROLL_KEY = "explore_scroll_position";
const PAGE_SIZE = 20;

const ExplorePostList = ({
  reload = false,
  origin,
  globalSearch,
  showSearchEmptyData = false,
  setStoryRefresh,
}) => {
  const router = useRouter();
  const [search, setSearch] = useState(globalSearch || null);
  const [userData, setUserData] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [isPageLoading, setIsPageLoading] = useState(true);

  const { latestCreatedPost, setTotalSearchRecords } = useContext(valContext);

  const scrollRestored = useRef(false);
  // const previousDataRef = useRef(null); // ✅ Cache for fallbackData
  const previousDataRef = useRef([{ data: [], totalRecords: 0 }]);

  // ✅ Restore scroll position
  // useEffect(() => {
  //   if (!scrollRestored.current) {
  //     console.log("inside here");
  //     const savedY = sessionStorage.getItem(SCROLL_KEY);
  //     if (savedY) {
  //       window.scrollTo(0, parseInt(savedY, 10));
  //       sessionStorage.removeItem(SCROLL_KEY);
  //     }
  //     scrollRestored.current = true;
  //   }
  // }, []);

  const handlePostClick = (id) => {
    // sessionStorage.setItem(SCROLL_KEY, window.scrollY.toString());
    // router.push(`/post/${id}`);
  };

  useEffect(() => {
    const data = authStorage.getProfileDetails();
    if (data) {
      setUserData(data);
    }
  }, []);

  useEffect(() => {
    if (globalSearch !== search) {
      setSearch(globalSearch);
      setRefreshKey((prev) => prev + 1);
    }
  }, [globalSearch]);

  // ✅ Memoize search and key base
  const stableSearch = useMemo(() => search?.trim() || "", [search]);
  const baseKey = useMemo(
    () => ["explore-posts", stableSearch, refreshKey],
    [stableSearch, refreshKey]
  );

  // ✅ Key generator
  const getKey = (pageIndex, previousPageData) => {
    if (previousPageData && !previousPageData.data.length) return null;
    return [...baseKey, pageIndex];
  };

  // ✅ Fetcher with response caching
  const fetcher = async ([, , , pageIndex]) => {
    const res = await getAllExplorePost({
      page: pageIndex + 1,
      limit: PAGE_SIZE,
      sortOrder: "DESC",
      searchQuery: stableSearch,
    });

    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      throw new Error(res?.message || "Failed to fetch posts");
    }

    if (search) setTotalSearchRecords(res?.data?.totalRecords);
    return res.data;
  };

  const { data, size, isLoading, setSize, isValidating, mutate, error } =
    useSWRInfinite(getKey, fetcher, {
      revalidateOnFocus: false,
      // fallbackData: previousDataRef.current, // ✅ fallback
      dedupingInterval: 2000, // ✅ avoid double fetches
      // initialSize: 1,
    });

  // ✅ Update cache
  // useEffect(() => {
  //   if (data && !data?.[0]?.data?.length > 0) {
  //     previousDataRef.current = data;
  //   }
  // }, [data]);

  const isFirstLoading = isLoading && !data?.[0]?.data?.length > 0; // ✅ precise loader

  const allPosts = data ? data.flatMap((d) => d.data) : [];
  const totalRecords = data?.[0]?.totalRecords || 0;
  const isReachingEnd = allPosts.length >= totalRecords;

  const postData = useMemo(() => {
    if (latestCreatedPost && allPosts.length === 0) {
      return [latestCreatedPost];
    }

    if (
      latestCreatedPost &&
      !allPosts.some((p) => p.id === latestCreatedPost.id)
    ) {
      return [latestCreatedPost, ...allPosts];
    }

    return allPosts;
  }, [latestCreatedPost, allPosts]);

  // console.log(latestCreatedPost, "latestCreatedPost", postData);

  // === ACTIONS ===

  const updateLike = async (id) => {
    const res = await updatePostLike(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) safeToast.error(res?.message);
    // mutate();
  };

  const updateWishList = async (id) => {
    const res = await updatePostWishList(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) safeToast.error(res?.message);
    // mutate();
  };

  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    try {
      const apiCall = isFollow ? followUser : unFollowUser;
      const response = await apiCall(userId);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
        // mutate();
      } else throw response;
    } catch (error) {
      safeToast.error(error?.message);
    }
  };

  const onMoveHandler = async (project, postData) => {
    try {
      const response = await editPost({
        id: postData?.id,
        ProjectId: project?.id,
      });
      if (response?.status !== RESPONSE_STATUS.SUCCESS) throw response;
      safeToast.success(response?.message ?? "Post moved successfully.");
      // mutate();
    } catch (error) {
      safeToast.error(error?.message);
    }
  };

  const pinPostHandler = async (id) => {
    try {
      const response = await pinPost(id);
      if (response?.status !== RESPONSE_STATUS.SUCCESS) throw response;
      // safeToast.success(response?.message);
      // mutate();
    } catch (error) {
      safeToast.error(error?.message);
    }
  };

  const getCommentData = async (id, setCommentList, setIsCommentLoading) => {
    try {
      const response = await getCommentList(id);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        setCommentList(response?.data?.data);
      } else throw response;
    } catch (error) {
      safeToast.error(error?.message);
    } finally {
      setIsCommentLoading(false);
    }
  };

  const blockerUser = async (userId, refetchDataCallback) => {
    try {
      const response = await blockUserAPI({
        id: userId,
      });
      // console.log(response);
      if (
        response?.status !== RESPONSE_STATUS.SUCCESS &&
        response?.status !== 200
      )
        throw response;
      safeToast.success(response?.message);
      setStoryRefresh((prev) => !prev);
      refetchDataCallback();
      // mutate();
    } catch (error) {
      safeToast.error(error?.message);
    }
  };

  useEffect(() => {
    if (data?.data?.length > 0 || error) {
      setIsPageLoading(false);
    }
  }, [data, error]);

  return (
    <div>
      {isFirstLoading ? ( // ✅ only true for the first load
        <ExplorePostSkeleton count={2} />
      ) : !isValidating && postData.length === 0 ? (
        showSearchEmptyData ? (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[70vh]">
            <Empty
              icon={<NoMemberFound size={50} />}
              label={"Search Result Not Found!"}
            />
          </div>
        ) : (
          <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center">
            <Empty
              iconRequired={false}
              label="Follow People or Project to show Feed!"
            />
          </div>
        )
      ) : (
        <div>
          <CustomGrid
            data={postData}
            className="tw-gap-4 sm:tw-gap-8 tw-my-6"
            Component={({ data: post }) => (
              <PostCard
                data={post}
                origin={origin}
                isUserLinkActive={true}
                userData={userData}
                getLikeData={updateLike}
                getWishlistData={updateWishList}
                onMoveHandler={onMoveHandler}
                refetchData={mutate}
                setRefresh={() => setRefreshKey((k) => k + 1)}
                getCommentData={getCommentData}
                followAndUnfollowHandler={followAndUnfollowHandler}
                pinPostHandler={() => pinPostHandler(post.id)}
                handlePostClick={() => handlePostClick(post.id)}
                isBlockUser={true}
                blockerUser={blockerUser}
              />
            )}
            xs={1}
            sm={1}
            md={1}
            lg={1}
            xl={1}
          />

          <InfiniteScroll
            threshold={90}
            loadMoreFunction={() => {
              if (!isReachingEnd && !isValidating) {
                setSize((s) => s + 1);
              }
            }}
            isLoading={isValidating}
            loadingComponent={<ExplorePostSkeleton count={3} />}
            timeout={10}
            loadOff={isReachingEnd}
          />
        </div>
      )}
    </div>
  );
};

export default ExplorePostList;
