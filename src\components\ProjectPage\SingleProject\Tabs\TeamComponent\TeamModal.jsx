import { useState, useEffect, useRef } from "react";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { LeftArrowBackIcon, NoMemberFound, SeeEyeIcon } from "@/utils/icons";
import { addTeam, getAllUser } from "@/app/action";
import Empty from "@/components/Common/Empty";
import SelectProjectSkeleton from "@/components/Loader/SelectProjectSkeleton";
import Image from "next/image";
import { blurDataURL } from "@/utils/function";
import InfiniteScroll from "react-infinite-scroll-component";
import { Eye, PenLine, SearchIcon } from "lucide-react";
import toast from "react-hot-toast";
import CustomButton from "@/components/Common/Custom-Button";
import { safeToast } from "@/utils/safeToast";
import { useDebouncedSearch } from "@/components/helper/hook/useDebouncedSearch";
import SearchBar from "@/components/Common/SearchBar";

const TeamModal = ({
  isOpen,
  setIsOpen,
  isSubProject = false,
  data = {},
  setRefresh = () => {},
  refetchData = () => {},
  loginUser,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [search, setSearch] = useState("");
  const [access, setAccess] = useState("read");
  const [isAccessModalOpen, setIsAccessModalOpen] = useState(false);
  const [isDataAvailable, setIsDataAvailable] = useState(false);
  const debounceRef = useRef(null);
  const api = useApiRequest();
  const addApi = useApiRequest(false);

  const accessList = [
    {
      label: "View Only",
      icon: <Eye />,
      value: "read",
    },
    {
      label: "Can Edit",
      icon: <PenLine />,
      value: "write",
    },
  ];

  // Reset DataList
  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };
  // Reset Modal
  const resetModal = () => {
    setIsAccessModalOpen(false);
    setIsOpen(false);
    resetData();
    setSelectedUsers([]);
    setAccess("read");
    setSearchQuery(null);
    setIsDataAvailable(false);
    setSearch("");
    setSearchQuery("");
  };

  const addTeamHandler = () => {
    const payload = {
      UserIds: selectedUsers?.map((ele) => ele?.id),
      access,
      ProjectId: data?.id,
    };
    // console.log(payload);
    addApi.sendRequest(
      addTeam,
      (res) => {
        // console.log(res);
        safeToast.success(res?.message);
        setRefresh((prev) => !prev);
        refetchData();
        resetModal();
      },
      payload
    );
  };

  // Select User Handler
  const handleSelect = (value) => {
    setSelectedUsers((prev) => {
      if (prev?.find((ele) => ele?.id === value?.id)) {
        return prev?.filter((item) => item?.id !== value?.id);
      } else {
        return [...prev, value];
      }
    });
  };

  // Search Handler
  // const onSearch = (value) => {
  //   if (debounceRef.current) {
  //     clearTimeout(debounceRef.current);
  //   }
  //   debounceRef.current = setTimeout(() => {
  //     resetData();
  //     setSearchQuery(value);
  //   }, 500);
  // };
  const onSearch = useDebouncedSearch({
    setSearchQuery, // required
    resetData, // optional
    setSearch, // optional
  });

  useEffect(() => {
    if (isOpen) {
      let queryParams = {
        page: pagination.page,
        limit: pagination.limit,
      };
      if (searchQuery) {
        queryParams = {
          ...queryParams,
          searchQuery,
        };
      }
      if (isSubProject) {
        queryParams = {
          ...queryParams,
          ParentId: data?.ParentProject?.id,
        };
      }
      queryParams = {
        ...queryParams,
        ProjectId: data?.id,
      };

      api.sendRequest(
        getAllUser,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [
              ...prev,
              ...res?.data?.data?.filter((ele) => ele?.id !== loginUser?.id),
            ]);
            // setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setIsDataAvailable(true);
            setDataList(() => [
              ...res?.data?.data?.filter((ele) => ele?.id !== loginUser?.id),
            ]);
          }
        },
        queryParams
      );
    }
  }, [pagination.page, isOpen, searchQuery]);
  // useEffect(() => {
  //   setSearch("");
  //   setSearchQuery("");
  // }, [isOpen]);
  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[40rem] !tw-rounded-[1.25rem] !tw-m-0 md:!tw-m-2",
          closeButton: `${addApi.isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
          },
        }}
        center
        focusTrapped={false}
        open={isOpen}
        closeIcon={<></>}
        onClose={() => {
          if (!addApi.isLoading) {
            resetModal();
          }
        }}
      >
        {/* Access Modal */}
        <Modal
          classNames={{
            modal:
              "!tw-max-w-[100%] !tw-w-[30rem] !tw-rounded-[1.25rem] !tw-m-0 md:!tw-m-2",
            closeButton: `${addApi.isLoading && "!tw-cursor-not-allowed"}`,
            overlay: "!tw-bg-[#000000CC]",
          }}
          styles={{
            modal: {
              position: "relative",
              overflow: "visible",
            },
          }}
          center
          focusTrapped={false}
          open={isAccessModalOpen}
          closeIcon={<></>}
          onClose={() => {
            if (!addApi.isLoading) {
              setIsAccessModalOpen(false);
            }
          }}
        >
          <div className="tw-py-3 tw-mx-8">
            <div className="tw-flex tw-justify-between tw-items-center">
              <button
                type="button"
                onClick={() => {
                  setIsAccessModalOpen(false);
                  setAccess("read");
                }}
                className=""
              >
                <LeftArrowBackIcon />
              </button>
              <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
                What can they do
              </p>
              <div />
            </div>
            <div className="tw-my-6 ">
              {accessList.map((ele) => (
                <button
                  className="tw-flex tw-justify-between tw-items-center tw-w-full tw-mb-5"
                  key={ele.value}
                  onClick={() => {
                    setAccess(ele?.value);
                  }}
                >
                  <div
                    className={`tw-flex tw-gap-3 tw-items-center ${
                      access === ele?.value
                        ? "tw-text-primary-purple"
                        : "tw-text-primary-black"
                    } `}
                  >
                    <div>{ele.icon}</div>
                    <p className="tw-text-lg">{ele.label}</p>
                  </div>

                  {ele.value === access && (
                    <div>
                      <svg
                        className=" tw-text-primary-purple !tw-rounded-full"
                        width={22}
                        height={22}
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="3"
                        strokeLinecap="round"
                        viewBox="0 0 24 24"
                      >
                        <path d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  )}
                </button>
              ))}
            </div>
            <div className="tw-flex tw-justify-center tw-items-center">
              {/* <button
                type="button"
                onClick={addTeamHandler}
                className=" tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-10 !tw-py-4 tw-font-semibold"
              >
                Invite
              </button> */}
              <CustomButton
                loading={addApi.isLoading}
                onClick={addTeamHandler}
                className={"!tw-px-10 !tw-py-[14px] "}
                type="button"
                count={8}
              >
                Invite
              </CustomButton>
            </div>
          </div>
        </Modal>

        {/* Users list */}
        <div className="tw-py-5 tw-mx-10">
          <div className="tw-flex tw-justify-between tw-items-center">
            <button
              type="button"
              onClick={() => {
                resetModal();
              }}
              className=""
            >
              <LeftArrowBackIcon />
            </button>
            <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
              Add Team Member
            </p>
            {isDataAvailable ? (
              <button
                type="button"
                className="tw-text-primary-purple tw-font-bold"
                onClick={() => {
                  // resetModal();
                  if (selectedUsers.length === 0) {
                    safeToast.error("Please Select Team Member");
                    return;
                  }
                  setIsAccessModalOpen((prev) => !prev);
                }}
              >
                Next
              </button>
            ) : (
              <div />
            )}
          </div>
          <div className="tw-my-2">
            {isDataAvailable && (
              <SearchBar
                search={search}
                setSearch={setSearch}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                onSearch={onSearch}
                resetData={resetData}
              />
            )}
            {/* <div className="tw-flex tw-mt-4 tw-gap-2 tw-items-center tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8]">
              <SearchIcon color="#787E89" />
              <input
                type="text"
                id="simple-search"
                className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500  `}
                autoComplete="off"
                placeholder="Search"
                onChange={(e) => {
                  onSearch(e.target.value);
                }}
              />
            </div> */}
            {api.isLoading && pagination.page === 1 && (
              <SelectProjectSkeleton count={7} />
            )}

            {dataList?.length > 0 && (
              <InfiniteScroll
                height={405}
                dataLength={dataList?.length ?? 0}
                // show-scrollbar css class to show the scroll bar
                className="infinite-scrollbar tw-px-1"
                hasMore={
                  pagination.page <
                  Math.ceil(pagination.total / pagination.limit)
                }
                next={() =>
                  setPagination((prev) => ({
                    ...prev,
                    page: prev.page + 1,
                  }))
                }
                loader={<SelectProjectSkeleton count={3} />}
              >
                {dataList.map((ele) => (
                  <button
                    type="button"
                    className="tw-my-3 tw-w-full tw-flex tw-justify-between tw-items-center"
                    key={ele.id}
                    onClick={() => {
                      handleSelect(ele);
                    }}
                  >
                    <div className="tw-flex tw-items-center tw-gap-3 ">
                      <div
                        className={`tw-relative tw-rounded-full tw-w-[3.25rem] tw-h-[3.25rem] ${
                          !ele?.image && "tw-bg-primary-purple tw-text-white "
                        }`}
                      >
                        {ele?.image ? (
                          <Image
                            src={ele?.image}
                            alt="user"
                            fill
                            className="!tw-rounded-full !tw-object-cover"
                            placeholder="blur"
                            blurDataURL={blurDataURL(300, 300)}
                          />
                        ) : (
                          <span className="tw-text-primary-1100  tw-font-merriweather tw-font-bold md:tw-text-2xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                            {ele?.firstName?.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div className="tw-flex tw-flex-col tw-items-start">
                        <p className="tw-font-bold tw-text-lg tw-not-italic !tw-text-primary-black">
                          {`${ele?.firstName ?? ""} ${ele?.lastName ?? ""}`}
                        </p>
                        {/* <p className="tw-text-sm  tw-max-w-[15.5rem] tw-break-words tw-not-italic !tw-text-[#787E89]">
                          {ele?.email ?? ""}
                        </p> */}
                      </div>
                    </div>
                    <div className="tw-border tw-border-black tw-p-0.5 tw-rounded tw-w-8 tw-h-8">
                      {!!selectedUsers?.find(
                        (data) => data?.id === ele?.id
                      ) && (
                        <div>
                          <svg
                            className=" tw-text-primary-purple !tw-rounded-full"
                            width={22}
                            height={22}
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            strokeLinecap="round"
                            viewBox="0 0 24 24"
                          >
                            <path d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </InfiniteScroll>
            )}
            {!api.isLoading && dataList?.length === 0 && (
              <div className="tw-flex tw-justify-center tw-items-center tw-h-[25rem]">
                <Empty
                  icon={searchQuery && <NoMemberFound size={50} />}
                  iconRequired={searchQuery}
                  label={
                    searchQuery
                      ? "Search Result Not Found!"
                      : isSubProject
                      ? "No team member"
                      : ""
                  }
                  subLabel={
                    isSubProject && !searchQuery ? "No team member yet!" : ""
                  }
                />
              </div>
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default TeamModal;
