"use client";
import {
  editPost,
  followUser,
  getAllPost,
  getCommentList,
  pinPost,
  unFollowUser,
  updatePostLike,
  updatePostWishList,
} from "@/app/action";
import { CustomGrid } from "@/components/Common/Custom-Display";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import { useProject } from "@/components/context/ProjectContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import PostModal from "@/components/HomePage/Form/PostModal";
import PostCard from "@/components/HomePage/PostCard";
import ExplorePostSkeleton from "@/components/Loader/ExplorePostSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { RESPONSE_STATUS } from "@/utils/function";
import { Edit2Icon } from "@/utils/icons";
import { safeToast } from "@/utils/safeToast";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

const Post = ({ origin }) => {
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  const [dataList, setDataList] = useState([]);
  const [userData, setUserData] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [loadOff, setLoadOff] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const api = useApiRequest();
  const { project } = useProject();

  // console.log(project?.ParentProject);

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const onMoveHandler = async (project, postData) => {
    let payload = {
      ProjectId: project?.id,
    };

    try {
      const response = await editPost({
        ...payload,
        id: postData?.id,
      });

      if (
        response?.status !== 200 &&
        response?.status !== RESPONSE_STATUS.SUCCESS
      ) {
        throw response;
      }
      safeToast.success("Post Move Successfully to this Project");
      setRefresh((prev) => !prev);
      resetDataList();
    } catch (error) {
      safeToast.error(error?.message);
      return;
    }
  };

  // Reset Data

  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };

  const updateLike = async (id) => {
    const res = await updatePostLike(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  const updateWishList = async (id) => {
    const res = await updatePostWishList(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
      // safeToast.error("Something went wrong");
    }
  };

  // Get Data
  const delayedSearch = async (projectId) => {
    const user = authStorage.getProfileDetails();
    setUserData(user);
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      ProjectId: +projectId,
      // UserId: +project?.UserId,
    };

    if (Object.keys(query)?.length > 0) {
      query.sortOrder = "DESC";
    }

    try {
      api.sendRequest(
        getAllPost,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [...prev, ...res?.data?.data]);
            setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setDataList(() => [...res?.data?.data]);
            setLoadOff(false);
          } else {
            setLoadOff(true);
          }
        },

        { ...query }
      );
    } catch (error) {
      console.error("Error fetching blog data:", error);
      // setScrollLoader(false);
    } finally {
      // setScrollLoader(false);
    }
    // setScrollLoader(false);
  };

  useEffect(() => {
    const fetchData = async (projectId) => {
      await delayedSearch(projectId);
    };

    if (project?.id) {
      fetchData(project?.id);
    }
  }, [pagination.page, refresh, project]);
  return (
    <div>
      <PostModal
        reFetchData={resetDataList}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        postForProject={!project?.ParentProject}
        postForSubProject={
          project?.ParentProject &&
          Object.keys(project?.ParentProject).length > 0
        }
        formData={{
          ...(project ?? {}),
        }}
        // formData={
        //   project?.ParentProject
        //     ? {
        //         ...(project?.ParentProject ?? {}),
        //       // id: project?.id,
        //       }
        //     : {
        //         ...(project ?? {}),
        //       }
        // }
        isRemoveAble={false}
      />
      {/* Add Post */}
      {(project?.UserId === userData?.id ||
        project?.ProjectMembers?.[0]?.access === "write") && (
        <button
          type="button"
          onClick={() => {
            setOpenModal({
              isOpen: true,
              type: "post",
            });
          }}
          className="tw-fixed lg:tw-absolute tw-z-50 tw-bottom-16 tw-right-6 lg:tw-bottom-auto lg:-tw-top-[6rem]  tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
        >
          <Edit2Icon size={22} />
          <span className="tw-hidden lg:tw-inline">Create</span>
        </button>
      )}
      <>
        <CustomGrid
          data={dataList}
          className="tw-gap-4 sm:tw-gap-8 tw-pb-16 lg:tw-pb-0"
          Component={({ data: dataProps }) => {
            return (
              <PostCard
                data={{ ...dataProps }}
                userData={userData}
                isUserLinkActive={true}
                origin={origin}
                allSettings={project?.UserId === userData?.id}
                hasEditAccessOnly={
                  project?.ProjectMembers?.[0]?.access === "write"
                }
                getLikeData={async (id) => {
                  updateLike(id);
                }}
                getWishlistData={async (id) => {
                  updateWishList(id);
                }}
                refetchData={resetDataList}
                setRefresh={setRefresh}
                getCommentData={async (
                  id,
                  setCommentList,
                  setIsCommentLoading
                ) => {
                  try {
                    const response = await getCommentList(id);
                    if (response?.status === RESPONSE_STATUS.SUCCESS) {
                      setCommentList(response?.data?.data);
                    } else {
                      throw response;
                    }
                  } catch (error) {
                    console.log(error, "error");
                    safeToast.error(error?.message);
                  } finally {
                    setIsCommentLoading(false);
                  }
                }}
                followAndUnfollowHandler={(userId, isFollow) => {
                  followAndUnfollowHandler(userId, isFollow);
                }}
                isProjectRemoveAble={false}
                onMoveHandler={onMoveHandler}
                pinPostHandler={async (id, setState) => {
                  try {
                    const response = await pinPost(id);
                    if (
                      response?.status !== 200 &&
                      response?.status !== RESPONSE_STATUS.SUCCESS
                    ) {
                      throw response;
                    }
                    resetDataList();
                    setRefresh((prev) => !prev);
                  } catch (error) {
                    safeToast.error(error?.message);
                    return;
                  }
                }}
                postForProject={!project?.ParentProject}
                postForSubProject={
                  project?.ParentProject &&
                  Object.keys(project?.ParentProject).length > 0
                }
              />
            );
          }}
          xs={1}
          sm={1}
          md={1}
          lg={1}
          xl={1}
        />
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            // if (pagination.total > dataList?.length) {
            if (
              pagination.page < Math.ceil(pagination.total / pagination.limit)
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<ExplorePostSkeleton count={2} />}
          timeout={10}
          loadOff={loadOff}
        />
      </>
      {!api.isLoading && dataList?.length === 0 && (
        <div className="tw-flex tw-h-[25rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Post available!" />
        </div>
      )}
    </div>
  );
};

export default Post;
