import { useRef, useState } from "react";
import Image from "next/image";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import { Modal } from "react-responsive-modal";
import ReactInstaStories from "react-insta-stories";
import "react-responsive-modal/styles.css"; // Required CSS
import { CloseModalIcon } from "@/utils/icons";
import {
  createProxyImageUrl,
  fonts,
  generateAvatarCanvas,
  getTimePassedFromNow,
  parseColor,
} from "@/utils/function";
import { fontMap } from "../HomePage/Font";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover"; // Adjust import path accordingly
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import HLSPlayer from "../HomePage/VideoPlayer/HLSPlayer";
import VideoPlayer from "../HomePage/VideoPlayer/VideoPlayer";
import StoriesPreview from "../HomePage/StoriesPreview";

const HighlightsList = ({
  data = [],
  setReload = () => {},
  isSettingVisible,
}) => {
  const [open, setOpen] = useState(false);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  // console.log(flatStories, currentStoryIndex);

  const handleStoryClick = (highlightIndex) => {
    // Calculate correct flat index from nested data
    // const indexInFlat = data
    //   .slice(0, highlightIndex)
    //   .reduce((acc, curr) => acc + (curr.HighlightedStories?.length || 0), 0);
    setCurrentStoryIndex(highlightIndex);
    // setStoryItems(flatStories);
    setOpen(true);
  };

  return (
    <>
      <StoriesPreview
        isOpen={open}
        setIsOpen={setOpen}
        currentUserIndex={currentStoryIndex}
        setCurrentUserIndex={setCurrentStoryIndex}
        storyData={data}
        setReload={setReload}
        dataKey="HighlightedStories"
        isSettingVisible={isSettingVisible}
        isHighlight={true}
      />

      <div className="tw-w-full tw-overflow-hidden tw-block">
        <Carousel
          opts={{
            align: "start",
            draggable: true,
            speed: 8,
            loop: false,
            startIndex: 0,
            containScroll: "trimSnaps",
            skipSnaps: true,
          }}
        >
          <CarouselContent>
            {data?.map((ele, i) => (
              <CarouselItem
                className="!tw-flex !tw-flex-col !tw-gap-1.5 !tw-items-center"
                key={`${ele?.id}`}
              >
                <button type="button" onClick={() => handleStoryClick(i)}>
                  <div className="tw-relative  tw-p-1 tw-flex-shrink-0 tw-rounded-full tw-overflow-hidden">
                    <div className="tw-relative tw-w-20 tw-h-20">
                      <Image
                        src={
                          ele?.image ||
                          generateAvatarCanvas(ele?.title || "Story", 80)
                        }
                        alt="story"
                        fill
                        className="tw-rounded-full !tw-object-cover"
                      />
                    </div>
                  </div>
                  <p
                    title={ele?.title}
                    className="tw-text-primary-black tw-font-medium tw-break-all tw-w-[6rem] tw-line-clamp-1"
                  >
                    {ele?.title}
                  </p>
                </button>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </>
  );
};

export default HighlightsList;
