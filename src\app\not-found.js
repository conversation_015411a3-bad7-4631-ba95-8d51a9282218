import Image from "next/image";
import Link from "next/link";
import errorImg from "../../public/images/auth/text-404.png";

export default function NotFound() {
  return (
    <div className="">

      <div className="tw-flex tw-justify-center tw-items-center tw-flex-col tw-gap-4 tw-h-screen">

        <div className="tw-relative tw-w-[40rem] tw-h-[22rem]">
          <Image
            src={errorImg}
            alt="Not Found"
            fill
          />
        </div>
        <div className="tw-flex tw-justify-center tw-items-center tw-flex-col tw-gap-3">
          <p className="tw-text-primary-black tw-font-bold tw-text-4xl">
            Oops.! Page Not Found!
          </p>
          <p className="tw-text-secondary-text">
            The page you are looking for does not exist
          </p>
          <Link
            hrefLang="en"
            className="tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-p-4 tw-font-semibold tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110"
            href="/"
          >
            <span> Back To Home</span>
            <b></b>
          </Link>
        </div>

      </div>


    </div>
  );
}
