"use client";
import { DotMenuIcon, EditPencilIcon, TodoFilterIcon } from "@/utils/icons";
import { CustomContainer } from "../Common/Custom-Display";
import CustomTitle from "../Common/CustomTitle";
import TodoCard from "./TodoCard";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import { deleteTodos, getTodos, updateTodoStatus } from "@/app/action";
import TodoSkeleton from "../Loader/TodoSkeleton";
import InfiniteScroll from "../Common/InfiniteScroll";
import { isObjEmpty, RESPONSE_STATUS } from "@/utils/function";
import toast from "react-hot-toast";
import Empty from "../Common/Empty";
import TodoFilter from "./TodoFilter";
import TodoModal from "./TodoModal";
import PopUpModal from "../Common/PopUpModal";
import { safeToast } from "@/utils/safeToast";
import authStorage from "@/utils/API/AuthStorage";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useCollapse } from "react-collapsed";

const Todos = ({ data = [] }) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [isAddData, setIsAddData] = useState(false);
  const [editData, setEditData] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState({});
  const [deleteData, setDeleteData] = useState(null);
  const [loginUserData, setLoginUserData] = useState(null);
  const api = useApiRequest();
  const deleteApi = useApiRequest(false);
  const todoRefs = useRef({});
  const { getCollapseProps, getToggleProps, isExpanded } = useCollapse({
    duration: 500,
  });

  const completedTodo = useMemo(() => {
    return dataList?.filter((todo) => Boolean(todo?.status));
  }, [dataList]);
  const inCompletedTodo = useMemo(() => {
    return dataList?.filter((todo) => !Boolean(todo?.status));
  }, [dataList]);

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Edit Handler
  const editHandler = (ele) => {
    setEditData(ele);
  };

  // Delete handler
  const deleteHandler = (ele) => {
    // console.log("delete", ele);
    setDeleteData(ele);
  };

  const updateTodoHandler = async (id) => {
    try {
      const response = await updateTodoStatus({
        id,
      });
      if (
        response?.status !== 200 &&
        response?.status !== RESPONSE_STATUS.SUCCESS
      ) {
        throw response;
      }

      // resetDataList();
      // setDataList((prevList) => {
      //   const updatedItem = prevList.find(
      //     (item) => item.id === id
      //   );
      //   if (!updatedItem) return prevList;

      //   const newList = prevList.filter(
      //     (item) => item.id !== id
      //   );
      //   if (!updatedItem?.status) {
      //     return [
      //       ...newList,
      //       {
      //         ...updatedItem,
      //         status: !updatedItem?.status,
      //       },
      //     ];
      //   } else {
      //     return [
      //       {
      //         ...updatedItem,
      //         status: !updatedItem?.status,
      //       },
      //       ...newList,
      //     ];
      //   }
      // });
      // setTimeout(() => {
      //   const target = todoRefs.current[id];
      //   if (
      //     target &&
      //     typeof target.scrollIntoView === "function"
      //   ) {
      //     target.scrollIntoView({
      //       behavior: "smooth",
      //       block: "center",
      //     });
      //   }
      // }, 100); // Allow rendering to complete
      safeToast.success(response?.message);
    } catch (error) {
      safeToast.error(error?.message);
      // safeToast.error("Something went wrong");
    }
  };

  const updateTodoState = (id) => {
    setDataList((prevList) => {
      const updatedItem = prevList.find((item) => item.id === id);
      if (!updatedItem) return prevList;

      const newList = prevList.filter((item) => item.id !== id);
      if (updatedItem?.status) {
        return [
          ...newList,
          {
            ...updatedItem,
            status: !updatedItem?.status,
          },
        ];
      } else {
        return [
          {
            ...updatedItem,
            status: !updatedItem?.status,
          },
          ...newList,
        ];
      }
    });
  };

  // Data fetch function
  const fetchTodos = useCallback(async () => {
    // setIsLoading(true); // ✅ Set loading before API call

    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
    };

    if (selectedFilter) {
      queryParams = {
        ...queryParams,
        ...selectedFilter,
      };
    }

    const user = authStorage.getProfileDetails();
    setLoginUserData(user);

    api.sendRequest(
      getTodos,
      (res) => {
        // console.log(res);
        if (pagination.page === 1) {
          setDataList(res?.data?.data);
        } else {
          setDataList((prev) => [...prev, ...res?.data?.data]);
        }
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
      },
      queryParams,
      null
    );
  }, [pagination.page, refresh, selectedFilter]);
  // console.log(dataList);
  useEffect(() => {
    fetchTodos();
  }, [pagination.page, refresh, selectedFilter]);
  return (
    <>
      {/* Delete Modal */}
      <PopUpModal
        isLoading={deleteApi.isLoading}
        isOpen={deleteData}
        setIsOpen={setDeleteData}
        mainMessage="Delete Todo"
        subMessage="Are you sure you want to Delete todo Permanently?"
        onConfirm={() => {
          deleteApi.sendRequest(
            deleteTodos,
            () => {
              resetDataList();
              setRefresh((prev) => !prev);
              setDeleteData(null);
            },
            deleteData?.id,

            "Todo Deleted Successfully"
          );
        }}
      />
      {/* Filter Modal */}
      <TodoFilter
        resetDataList={resetDataList}
        setFilterValues={setSelectedFilter}
        isOpen={isFilterOpen}
        setIsOpen={setIsFilterOpen}
      />
      {/* Create Todo Modal */}
      <TodoModal
        isOpen={isAddData}
        setIsOpen={setIsAddData}
        editData={editData}
        setEditData={setEditData}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
      />
      <CustomContainer className="tw-py-3 sm:tw-py-4">
        <div className="tw-w-full tw-flex tw-justify-between sm:tw-items-center">
          <CustomTitle className="tw-w-full sm:tw-w-auto" name="To Do" />

          <div className="tw-flex lg:tw-gap-6 tw-items-center tw-w-full sm:tw-w-auto tw-justify-end">
            <div className="tw-relative">
              <button
                type="button"
                onClick={() => {
                  setIsFilterOpen((prev) => !prev);
                }}
                className="tw-p-2 tw-rounded-full tw-transition-colors hover:tw-bg-gray-100"
              >
                <TodoFilterIcon className="tw-w-5 tw-h-5 sm:tw-w-6 sm:tw-h-6" />
              </button>
              {selectedFilter && Object.keys(selectedFilter).length > 0 && (
                <div className="tw-absolute tw-top-1 tw-right-1 tw-z-10 tw-h-2 tw-w-2 sm:tw-h-2.5 sm:tw-w-2.5 tw-bg-[#EF3B41] tw-rounded-full tw-border tw-border-white" />
              )}
            </div>

            <div className="">
              <button
                type="button"
                onClick={() => setIsAddData((prev) => !prev)}
                className="tw-fixed tw-bottom-[8.5rem] tw-right-4 tw-z-50 lg:tw-static tw-bg-primary-purple tw-border-primary-purple tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white tw-p-[1.1rem] lg:tw-px-5  lg:tw-py-[1.1rem] tw-flex tw-items-center tw-gap-1.5 tw-font-semibold tw-group tw-transition-transform tw-duration-300 hover:tw-scale-105 tw-text-sm sm:tw-text-base"
              >
                <DotMenuIcon className="tw-h-[18px] tw-w-[18px] sm:tw-h-[20px] sm:tw-w-[20px] tw-font-semibold" />
                <span className="tw-hidden lg:tw-inline-block">Create</span>
              </button>
            </div>
          </div>
        </div>

        {api.isLoading && pagination.page === 1 ? (
          <TodoSkeleton />
        ) : (
          <div className=" tw-mb-20 lg:tw-mt-6  ">
            <div className=" ">
              {inCompletedTodo?.map((ele) => (
                <div
                  // ref={(el) => {
                  //   if (el) todoRefs.current[ele.id] = el;
                  // }}
                  key={ele?.id}
                  className="tw-mb-3 sm:tw-mb-4"
                >
                  <TodoCard
                    ele={ele}
                    updateTodoHandler={updateTodoHandler}
                    deleteHandler={deleteHandler}
                    editHandler={editHandler}
                    isShowAllSettings={loginUserData?.id === ele?.UserId}
                    updateSetState={updateTodoState}
                  />
                </div>
              ))}
            </div>
            <InfiniteScroll
              threshold={90}
              loadMoreFunction={() => {
                // if (pagination.total > dataList?.length) {
                if (
                  pagination.page <
                  Math.ceil(pagination.total / pagination.limit)
                ) {
                  setPagination((prev) => ({
                    ...prev,
                    page: prev?.page + 1,
                  }));
                }
              }}
              isLoading={api.isLoading}
              loadingComponent={<TodoSkeleton count={2} />}
              timeout={10}
            />
            {completedTodo?.length > 0 && (
              <div
                {...getToggleProps()}
                className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center tw-py-2"
              >
                <p className="tw-text-primary-black tw-text-lg tw-font-semibold">
                  Completed ({completedTodo?.length})
                </p>
                <div className="tw-flex-shrink-0">
                  {isExpanded ? (
                    <ChevronUp size={20} />
                  ) : (
                    <ChevronDown size={20} />
                  )}
                </div>
              </div>
            )}
            <div
              {...getCollapseProps()}
              className={`tw-mt-3 tw-mb-20 lg:tw-mb-3 sm:tw-my-4  tw-transition-all tw-duration-300 `}
            >
              {completedTodo?.map((ele) => (
                <div
                  // ref={(el) => {
                  //   if (el) todoRefs.current[ele.id] = el;
                  // }}
                  key={ele?.id}
                  className="tw-mb-3 sm:tw-mb-4"
                >
                  <TodoCard
                    ele={ele}
                    updateTodoHandler={updateTodoHandler}
                    deleteHandler={deleteHandler}
                    editHandler={editHandler}
                    isShowAllSettings={loginUserData?.id === ele?.UserId}
                    updateSetState={updateTodoState}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </CustomContainer>
      {dataList?.length === 0 && !api.isLoading && (
        <div className="tw-flex tw-justify-center tw-items-center tw-h-[50dvh]">
          <Empty
            className="tw-text-2xl tw-font-semibold "
            label={
              !isObjEmpty(selectedFilter)
                ? "No matching To Do"
                : "You're all caught up!"
            }
            subLabel={
              !isObjEmpty(selectedFilter)
                ? "Try adjusting or clearing your filters to see more To Do."
                : "You have no To Do at the moment."
            }
            iconRequired={false}
          />
        </div>
      )}
    </>
  );
};

export default Todos;
