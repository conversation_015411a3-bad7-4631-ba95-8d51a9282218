import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { blurDataURL, privateProjectToggle } from "@/utils/function";
import {
  EditProfileIcon,
  PrivateProjectIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import { Flag, Lock, Trash2 } from "lucide-react";
import PopUpModal from "../Common/PopUpModal";
import { useState } from "react";
import SubProjectModal from "../HomePage/Form/SubProjectModal";
import { deleteProject, getOneProject, updateProject } from "@/app/action";
import useApiRequest from "../helper/hook/useApiRequest";
import tempProjectImg from "../../../public/images/assets/default_image.png";
import ShowMenuList from "../Common/ShowMenuList";
import { safeToast } from "@/utils/safeToast";

const SubProjectsCard = ({
  ele,
  countType = "post",
  countKey = "postsCount",
  onClick = () => {},
  settingMenu = true,
  imageClass = "",
  setRefresh = () => {},
  resetState = () => {},
  shareHandler = () => {},
  reportHandler = () => {},
  loginUserData,
  projectData,
}) => {
  const [editData, setEditData] = useState(null);
  // const [deleteProject, setDeleteProject] = useState(null);
  const [deleteProjectData, setDeleteProjectData] = useState(null);
  const [hideProject, setHideProject] = useState(null);
  const api = useApiRequest(false);
  const subProjectAPI = useApiRequest(false);

  // console.log(ele, "Project Ele");
  const myProjectSetting = [
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        api.sendRequest(
          getOneProject,
          (res) => {
            setEditData(res?.data);
          },
          {
            id: ele?.id,
          }
        );
      },
    },
    {
      label: `${!ele?.hide ? "Hide Private" : "Undo Hide"} Project`,
      className: "",
      icon: <Lock size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        setHideProject(ele);
      },
    },
    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        setDeleteProjectData(ele);
      },
    },
  ];

  const otherProjectSetting = [
    {
      label: "Share",
      className: "",
      icon: <ShareIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        // setIsShareOpen(true);
        shareHandler(ele);
      },
    },
    {
      label: "Report this Project",
      className: "",
      icon: <Flag size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        reportHandler(ele);
      },
    },
  ];
  return (
    <>
      {/* Project hide/unhide Confirm Popup */}
      <PopUpModal
        isLoading={subProjectAPI.isLoading}
        isOpen={hideProject}
        setIsOpen={setHideProject}
        icon={null}
        mainMessage={privateProjectToggle(ele?.name, !ele?.hide).title}
        // mainMessage={`${!hideProject?.hide ? "Hide" : "UnHide"} Project`}
        subMessage={privateProjectToggle(ele?.name, !ele?.hide).message}
        // subMessage={`Are you sure you want to ${
        //   !hideProject?.hide ? "hide" : "Unhide"
        // } your Project?`}
        onConfirm={() => {
          const payload = {
            id: hideProject?.id,
            hide: !hideProject?.hide,
          };
          subProjectAPI.sendRequest(
            updateProject,
            (res) => {
              safeToast.success(
                privateProjectToggle(ele?.name, !ele?.hide)?.successMessage
              );
              resetState();
              setRefresh((prev) => !prev);
              setHideProject(null);
            },
            payload
          );
        }}
      />
      {/* Edit Project Modal */}
      <SubProjectModal
        isOnlyEditSubProject
        editData={editData}
        setEditData={setEditData}
        editProjectData={editData}
        reFetchData={resetState}
        setRefresh={setRefresh}
        modalTitle={"Edit sub-project"}
        modalSubmitButton={"Update"}
      />
      {/* Delete Confirm Popup */}
      <PopUpModal
        isLoading={api.isLoading}
        isOpen={deleteProjectData}
        setIsOpen={setDeleteProjectData}
        mainMessage="Delete Sub-Project"
        subMessage="Are you sure you want to Delete Sub-Project Permanently?"
        onConfirm={() => {
          api.sendRequest(
            deleteProject,
            () => {
              setRefresh((prev) => !prev);
              resetState();
              setDeleteProjectData(null);
            },
            {
              id: deleteProjectData?.id,
            },
            "Delete Sub-Project Successfully"
          );
        }}
      />
      <div
        onClick={() => {
          onClick();
        }}
        key={ele?.id}
      >
        {/* <Link key={ele?.id} href={`/projects/${ele?.id}`}> */}
        <div className="tw-cursor-pointer" key={ele?.id}>
          <div className="tw-relative tw-w-full tw-h-[12rem] tw-rounded-3xl">
            <Image
              fill
              className={`!tw-rounded-3xl !tw-object-cover ${imageClass}`}
              src={ele?.image ?? tempProjectImg}
              alt={ele?.name ?? ele?.title ?? "sub-project"}
              placeholder="blur"
              blurDataURL={blurDataURL(290, 180)}
            />
            <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />

            {settingMenu && (
              <ShowMenuList
                data={ele}
                menuList={
                  ele?.UserId === loginUserData?.id ||
                  loginUserData?.id === projectData?.UserId
                    ? myProjectSetting?.filter((data) =>
                        ele?.isPrivate
                          ? data
                          : data?.label === "Edit" || data?.label === "Delete"
                      )
                    : otherProjectSetting
                }
              >
                <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-4">
                  <ThreeDotMenuIcon fill="#fff" size={24} />
                </div>
              </ShowMenuList>
            )}
          </div>

          {/* {(countType || countKey) && (
            <p className="tw-text-primary-black tw-my-1">
              {+ele[countKey] ?? 0} {countType}
            </p>
          )} */}
          <div className="tw-flex tw-gap-2 tw-my-1">
            {ele?.isPrivate && (
              <div className="tw-bottom-2 tw-right-3 tw-bg-primary-purple tw-p-2 tw-rounded-full tw-h-full">
                <PrivateProjectIcon size={20} />
              </div>
            )}
            <p className="tw-text-2xl tw-font-bold  tw-break-all tw-line-clamp-2">
              {ele?.name ?? ""}
            </p>
          </div>
        </div>
        {/* </Link> */}
      </div>
    </>
  );
};

export default SubProjectsCard;
