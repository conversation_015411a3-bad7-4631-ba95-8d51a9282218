import clsx from "clsx";

const priorities = [
  {
    label: "Low",
    value: 1,
  },
  {
    label: "Medium",
    value: 2,
  },
  {
    label: "High",
    value: 3,
  },
];

const PrioritySelector = ({ value, setFieldValue }) => {
  return (
    <div className="tw-space-y-2 sm:tw-space-y-3 tw-mb-4">
      <label className="tw-text-primary-black tw-text-sm sm:tw-text-base tw-font-medium">
        Select Priority (Optional)
      </label>
      <div className="tw-flex tw-justify-between lg:tw-justify-center tw-gap-4 lg:tw-gap-8">
        {priorities.map((priority) => (
          <button
            type="button"
            key={priority.label}
            onClick={() => setFieldValue("priority", priority.value)}
            className={clsx(
              "tw-px-7 lg:tw-px-9  tw-py-3  tw-rounded-full tw-border tw-border-secondary-text tw-font-medium tw-text-sm sm:tw-text-base tw-transition-all tw-duration-200 ",
              {
                "tw-bg-green-500 tw-text-white tw-border-green-500 !tw-border-none tw-shadow-sm":
                  priority.value === value && priority.label === "Low",
                "tw-bg-yellow-500 tw-text-white tw-border-yellow-500 !tw-border-none tw-shadow-sm":
                  priority.value === value && priority.label === "Medium",
                "tw-bg-red-500 tw-text-white tw-border-red-500 !tw-border-none tw-shadow-sm":
                  priority.value === value && priority.label === "High",
                "tw-bg-white tw-text-gray-800 tw-border-gray-300 hover:tw-border-gray-400 hover:tw-bg-gray-50":
                  priority.value !== value,
              }
            )}
          >
            {priority.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default PrioritySelector;
