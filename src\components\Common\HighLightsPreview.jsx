import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import Stories from "react-insta-stories";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { CloseModalIcon } from "@/utils/icons";
import { getTimePassedFromNow } from "@/utils/function";

const HighLightsPreview = ({
  isOpen,
  setIsOpen,
  currentUserIndex,
  setCurrentUserIndex,
  users,
}) => {
  const navigate = (direction) => {
    if (direction === "next" && currentUserIndex < users.length - 1) {
      setCurrentUserIndex((prev) => prev + 1);
    } else if (direction === "prev" && currentUserIndex > 0) {
      setCurrentUserIndex((prev) => prev - 1);
    }
  };

  const currentUser = users[currentUserIndex];
  const stories = currentUser?.Stories?.map((story) => {
    const {
      mediaLink,
      mediaType,
      duration = 5,
      overlayImage,
      thumbNailImage,
    } = story;
    const header = {
      heading: `${currentUser.firstName} ${currentUser.lastName}`,
      profileImage: currentUser.image,
      subheading: getTimePassedFromNow(story?.createdAt),
    };

    if (mediaType === "video") {
      if (mediaLink.endsWith(".m3u8")) {
        return {
          content: ({ action }) => (
            <HLSPlayer
              overlayImage={overlayImage}
              url={mediaLink}
              action={action}
              header={header}
            />
          ),
          duration: duration * 1000,
          header,
        };
      }

      // For mp4/webm
      return {
        content: ({ action }) => (
          <VideoPlayer
            overlayImage={overlayImage}
            url={mediaLink}
            action={action}
            header={header}
          />
        ),
        duration: duration * 1000,
        header,
      };
    }

    // Fallback for images
    return {
      url: overlayImage || mediaLink,
      type: "image",
      header,
    };
  });

  return (
    <div>
      <Modal
        open={isOpen}
        onClose={() => {
          setIsOpen(false);
        }}
        center
        classNames={{
          modal:
            "!tw-p-0 !tw-m-0 !tw-bg-black !tw-text-white !tw-w-full !tw-h-screen",
        }}
        //   closeIcon={<X className="tw-text-white" />}
        closeIcon={
          <>
            <CloseModalIcon size={30} />
          </>
        }
        styles={{ modal: { maxWidth: "100vw", width: "100%", padding: 0 } }}
        showCloseIcon={true}
        focusTrapped={false}
      >
        <div className="tw-relative tw-w-full tw-h-screen tw-flex tw-items-center tw-justify-center">
          <Stories
            stories={stories}
            defaultInterval={6000}
            storyContainerStyles={{
              overflow: "hidden",
            }}
            keyboardNavigation={true}
            height="95vh"
            onAllStoriesEnd={() => {
              if (currentUserIndex < users.length - 1) {
                navigate("next");
              } else {
                // closeModal();
                setIsOpen(false);
              }
            }}
          />

          {/* Navigation buttons */}
          {/* {currentUserIndex > 0 && (
            <button
              onClick={() => navigate("prev")}
              className="tw-z-[9999] tw-absolute tw-left-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-white tw-text-center"
            >
              <ChevronLeft size={25} />
            </button>
          )}
          {currentUserIndex < users.length - 1 && (
            <button
              onClick={() => navigate("next")}
              className="tw-z-[9999] tw-absolute tw-right-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-center tw-text-white"
            >
              <ChevronRight size={25} />
            </button>
          )} */}
        </div>
      </Modal>
    </div>
  );
};

export default HighLightsPreview;
