"use client";

import { useEffect, useState, useMemo } from "react";
import {
  blurDataURL,
  checkImageLoads,
  createProxyImageUrl,
  generateAvatarCanvas,
} from "@/utils/function";
import Image from "next/image";

const UserAvatar = ({
  imageUrl,
  imageClassName = "",
  imageParentClassName = "",
  userName,
  userNameClassName = "",
  size = 100,
}) => {
  const [error, setError] = useState(false);

  // Memoize the generated avatar to prevent recreation on every render
  const fallbackAvatar = useMemo(() => {
    return generateAvatarCanvas(userName ?? "", size, 0.55);
  }, [userName]);

  useEffect(() => {
    setError(false);
  }, [imageUrl]);

  return (
    <div
      className={`tw-relative tw-rounded-full tw-w-12 tw-h-12 md:tw-w-14 md:tw-h-14 lg:tw-w-[3.125rem] lg:tw-h-[3.125rem]  ${imageParentClassName}`}
    >
      <Image
        src={
          !error && imageUrl ? createProxyImageUrl(imageUrl) : fallbackAvatar
        }
        alt={userName ?? "User"}
        onError={() => {
          setError(true);
        }}
        fill
        placeholder="blur"
        blurDataURL={blurDataURL(45, 45)}
        className={`tw-object-cover tw-rounded-full ${imageClassName}`}
      />
    </div>
  );
};

export default UserAvatar;
