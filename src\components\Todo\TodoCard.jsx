"use client";
const TodoCard = ({
  ele,
  updateTodoHandler = () => {},
  editHandler = () => {},
  deleteHandler = () => {},
  isShowAllSettings = true,
  isOnlyEditSetting = false,
  updateSetState = () => {},
}) => {
  const [todoStatus, setTodoStatus] = useState(Boolean(ele?.status));
  const [isCollapse, setIsCollapse] = useState(false);

  const todoSetting = [
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        editHandler(ele);
      },
    },

    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        deleteHandler(ele);
      },
    },
  ];
  return (
    <>
      <div
        onClick={() => {
          setIsCollapse((prev) => !prev);
        }}
        className="tw-flex tw-gap-2 tw-items-center tw-cursor-pointer"
      >
        <div>
          <CustomCheckbox
            id={ele?.id}
            todoStatus={todoStatus}
            setTodoStatus={setTodoStatus}
            updateSetState={updateSetState}
            updateTodoHandler={(id, setState) => {
              setTodoStatus((prev) => !prev);
              updateTodoHandler(id, setState);
            }}
            status={todoStatus}
          />
        </div>
        <div className="tw-relative tw-overflow-hidden tw-py-4 tw-pl-8 tw-pr-5 tw-rounded-[1.75rem] tw-bg-[#F5F7F8]  tw-w-full">
          <div
            className="tw-absolute tw-left-0 tw-top-0 tw-w-[7px] tw-h-full tw-z-0 tw-blur-[3px] tw-rounded-tl-[1rem] tw-rounded-bl-[1rem] "
            style={{
              background: `linear-gradient(to bottom, ${
                ele?.priority && !todoStatus
                  ? todoPriorityDiv[+ele?.priority]?.bgClass
                  : "#2D394A"
              })`,

              boxShadow: `0 0 8px 3px ${
                ele?.priority && !todoStatus
                  ? todoPriorityDiv[+ele?.priority]?.bgClass
                  : "#2D394A"
              }`,

              // background: ele?.priority
              //   ? todoPriorityDiv[+ele?.priority]?.bgClass
              //   : "#111",
            }}
          />
          <div className="tw-flex tw-justify-end">
            <div className="tw-flex tw-gap-3 tw-items-center tw-text-xs lg:tw-text-sm  tw-text-secondary-text">
              {ele?.dueDate && (
                <p>
                  Due Date: {convertUTCtoLocal(ele?.dueDate, "MMM DD, YYYY")}{" "}
                </p>
              )}
              <p>
                Created on: {convertUTCtoLocal(ele?.createdAt, "MMM DD, YYYY")}
              </p>
              {(isShowAllSettings || isOnlyEditSetting) && (
                <ShowMenuList
                  data={ele}
                  menuList={
                    isShowAllSettings
                      ? todoSetting
                      : isOnlyEditSetting
                      ? todoSetting?.filter((ele) => ele.label === "Edit")
                      : []
                  }
                >
                  <button
                    type="button"
                    className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center  tw-right-7 tw-top-[1rem]"
                  >
                    <div className="">
                      <ThreeDotMenuIcon fill="#111" size={24} />
                    </div>
                  </button>
                </ShowMenuList>
              )}
            </div>
          </div>

          <p
            className={`${
              todoStatus ? "tw-text-secondary-text" : "tw-text-primary-black"
            } tw-font-semibold tw-my-1 tw-text-2xl`}
          >
            {ele?.name}
          </p>
          {isCollapse && (
            <div className="tw-transition-all tw-duration-100 tw-ease-in-out ">
              {ele?.description && (
                <p
                  className={`${
                    todoStatus
                      ? "tw-text-secondary-text"
                      : "tw-text-primary-black"
                  } tw-my-1`}
                >
                  {ele?.description}
                </p>
              )}
              {/* Files Download */}
              {ele?.Files?.map((ele) => (
                <div
                  key={ele?.link}
                  className="tw-grid tw-grid-cols-3 lg:tw-flex tw-gap-3 lg:tw-gap-6 tw-items-center -tw-mb-4"
                >
                  <div className="tw-flex tw-gap-3 tw-col-span-2 tw-items-center tw-text-sm lg:tw-text-base">
                    {ele?.name?.includes("pdf") ? (
                      <PdfIcon width={25} />
                    ) : (
                      <DocIcon width={25} />
                    )}
                    <p
                      className={`${
                        todoStatus
                          ? "tw-text-secondary-text"
                          : "tw-text-primary-black"
                      } tw-w-[85%] tw-break-all lg:tw-w-full`}
                    >
                      {ele?.name}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      downloadFileFromUrl(ele?.link, ele?.name);
                    }}
                    className="tw-flex tw-gap-3 tw-items-center tw-text-primary-purple tw-font-medium tw-text-sm"
                  >
                    <div>
                      <DownloadIcon />
                    </div>
                    <p>Download</p>
                  </button>
                </div>
              ))}
            </div>
          )}
          <div className="tw-flex tw-gap-3 tw-items-center tw-text-sm tw-mb-2 tw-mt-3 tw-text-secondary-text">
            <p>{todoPriorityDiv[+ele?.priority]?.label}</p>
            {ele?.Project && (
              <div className="tw-flex tw-gap-3 tw-items-center">
                <p className="tw-w-1 tw-h-1 tw-rounded-full tw-bg-secondary-text" />

                <p>
                  Project:&nbsp;
                  {ele?.Project?.ParentProject?.name ??
                    ele?.Project?.name ??
                    ""}
                </p>
              </div>
            )}
          </div>
          <div className="tw-mt-2 tw-mb-3 tw-flex tw-flex-col lg:tw-flex-row lg:tw-items-center tw-gap-2 lg:tw-gap-5">
            <div className="tw-flex tw-gap-2 tw-items-center tw-text-xs lg:tw-text-sm  tw-text-secondary-text">
              <p>{ele?.AssignedUser ? "Assigned By:" : "Created by me"} </p>
              {ele?.AssignedUser && (
                <div
                  className={`tw-flex tw-gap-2 tw-items-center ${
                    todoStatus
                      ? "tw-text-secondary-text"
                      : "tw-text-primary-black"
                  } tw-font-medium`}
                >
                  <UserAvatar
                    imageParentClassName="!tw-w-[1.5rem] lg:!tw-w-[1.75rem] !tw-h-[1.5rem] lg:!tw-h-[1.75rem]"
                    imageUrl={ele?.User?.image}
                    userName={ele?.User?.firstName}
                    userNameClassName="!tw-text-base"
                  />
                  {ele?.User && (
                    <div>{`${ele?.User?.firstName} ${ele?.User?.lastName}`}</div>
                  )}
                </div>
              )}
            </div>
            {ele?.AssignedUser && (
              <div className="tw-flex tw-gap-2 tw-items-center tw-text-xs lg:tw-text-sm  tw-text-secondary-text">
                <p>Assigned To:</p>
                <div
                  className={`tw-flex tw-gap-2 tw-items-center ${
                    todoStatus
                      ? "tw-text-secondary-text"
                      : "tw-text-primary-black"
                  } tw-font-medium`}
                >
                  <UserAvatar
                    imageParentClassName="!tw-w-[1.5rem] lg:!tw-w-[1.75rem] !tw-h-[1.5rem] lg:!tw-h-[1.75rem]"
                    imageUrl={ele?.AssignedUser?.image}
                    userName={ele?.AssignedUser?.firstName}
                    userNameClassName="!tw-text-base"
                  />
                  {ele?.AssignedUser && (
                    <div className="">{`${ele?.AssignedUser?.firstName} ${ele?.AssignedUser?.lastName}`}</div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default TodoCard;

import {
  blurDataURL,
  convertUTCtoLocal,
  downloadFileFromUrl,
  todoPriorityDiv,
} from "@/utils/function";
import {
  DocIcon,
  DownloadIcon,
  EditProfileIcon,
  PdfIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import Image from "next/image";
import { useState } from "react";
import ShowMenuList from "../Common/ShowMenuList";
import { Trash2 } from "lucide-react";
import UserAvatar from "../Common/UserAvatar";

function CustomCheckbox({
  updateTodoHandler,
  id,
  todoStatus,
  setTodoStatus,
  updateSetState,
}) {
  return (
    <label className="tw-relative tw-inline-block tw-w-8 tw-h-8 tw-select-none">
      <input
        type="checkbox"
        className="tw-absolute tw-opacity-0 tw-w-0 tw-h-0"
        checked={todoStatus}
        onChange={() => {
          // setTodoStatus((prev) => !prev);
          updateSetState(id);
          updateTodoHandler(id, setTodoStatus);
        }}
      />
      <span
        className={`tw-absolute tw-cursor-pointer tw-top-0 tw-left-0 tw-w-8 tw-h-8 tw-rounded-full tw-flex tw-items-center tw-justify-center ${
          todoStatus
            ? "tw-bg-[#10BE5B]"
            : "tw-border-[1.5px] tw-border-secondary-text"
        }`}
      >
        {todoStatus && (
          <svg
            className=" tw-text-white !tw-rounded-full"
            width={22}
            height={22}
            fill="none"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
            viewBox="0 0 24 24"
          >
            <path d="M5 13l4 4L19 7" />
          </svg>
        )}
      </span>
    </label>
  );
}
