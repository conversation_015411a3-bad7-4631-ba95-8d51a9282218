// app/api/linkedin-auth/route.js
import { LINKEDIN_REDIRECT_URL } from "@/utils/function";
import axios from "axios";
import { NextResponse } from "next/server";

export async function POST(req) {
  try {
    const { code } = await req.json();

    const clientId = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;
    const clientSecret = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_SECRET;
    const redirectUri = LINKEDIN_REDIRECT_URL;

    const params = new URLSearchParams();
    params.append("grant_type", "authorization_code");
    params.append("code", code);
    params.append("redirect_uri", redirectUri);
    params.append("client_id", clientId);
    params.append("client_secret", clientSecret);

    const response = await axios.post(
      "https://www.linkedin.com/oauth/v2/accessToken",
      params,
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    return NextResponse.json(response.data, { status: 200 });
  } catch (error) {
    console.error(
      "LinkedIn Token Exchange Error:",
      error?.response?.data || error
    );
    return NextResponse.json(
      { error: error?.response?.data || "Internal Server Error" },
      { status: 500 }
    );
  }
}
