import {
  DotMenuIcon,
  FolderIcon,
  HomeIcon,
  ProfileIcon,
  UserGroupIcon,
} from "@/utils/icons";
import { useFormik } from "formik";
import Modal from "react-responsive-modal";
import CustomButton from "../Common/Custom-Button";
import { useEffect } from "react";
import toast from "react-hot-toast";
import { updateMyProfile } from "@/app/action";
import useApiRequest from "../helper/hook/useApiRequest";
import "react-responsive-modal/styles.css";
import { safeToast } from "@/utils/safeToast";

const CustomizeNavigation = ({ isOpen, setIsOpen, data, setProfileData }) => {
  const api = useApiRequest(false);
  const navigationList = [
    {
      id: 1,
      label: "Explore",
      value: "explore",
      icon: <HomeIcon stroke="#2D394A" />,
    },
    {
      id: 2,
      label: "Projects",
      value: "projects",
      icon: <FolderIcon stroke="#2D394A" />,
    },
    {
      id: 3,
      label: "Communities",
      value: "communities",
      icon: <UserGroupIcon stroke="#2D394A" />,
    },
    {
      id: 4,
      label: "To Do",
      value: "to dos",
      icon: <DotMenuIcon stroke="#2D394A" />,
    },
    {
      id: 5,
      label: "Profile",
      value: "profile",
      icon: <ProfileIcon stroke="#2D394A" />,
    },
  ];

  const formik = useFormik({
    enableReinitialize: true, // Allows form values to update when `open` changes
    initialValues: {
      homeSection: "explore",
    },
    onSubmit: async (values) => {
      //   console.log("Updated Profile:", values);
      const payload = {
        customSetting: values,
      };

      api.sendRequest(
        updateMyProfile,
        (res) => {
          setProfileData((prev) => ({
            ...prev,
            ...payload,
          }));
          resetModal();
          safeToast.success(res?.message);
        },
        payload,
        ""
      );
    },
  });

  // Reset State
  const resetModal = () => {
    setIsOpen(false);
  };

  useEffect(() => {
    if (data) {
      formik.setFieldValue("homeSection", data);
    }
  }, [data]);

  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[30rem] !tw-rounded-[1.25rem] !tw-mt-1",
          closeButton: `${api.isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        focusTrapped={false}
        open={isOpen}
        onClose={() => {
          if (!api.isLoading) {
            resetModal();
          }
        }} // Fix: Changed `onClose` to `onCancel` for Ant Design modals
      >
        <div className="tw-py-5 tw-mx-14">
          <h2 className="tw-text-2xl tw-text-center tw-font-semibold">
            Customize Navigation
          </h2>
          <p className="tw-text-center tw-text-primary-black tw-font-light tw-text-base">
            Select your first home option by priority.
          </p>
          <form onSubmit={formik.handleSubmit}>
            <div className="tw-flex tw-flex-col tw-my-5 tw-gap-4 tw-items-start tw-text-lg ">
              {/* Public */}
              {navigationList?.map((ele) => (
                <div
                  key={ele?.id}
                  onClick={() => {
                    formik.setFieldValue("homeSection", ele?.value);
                  }}
                  className="tw-flex tw-cursor-pointer tw-items-center tw-rounded-xl tw-p-3 tw-w-full tw-bg-[#F5F7F8] tw-gap-3"
                >
                  <input
                    type="radio"
                    id={ele.value}
                    name="homeSection"
                    value={ele.value}
                    className="tw-cursor-pointer"
                    checked={formik.values.homeSection === ele.value}
                    onChange={formik.handleChange}
                  />
                  {ele?.icon}
                  <label htmlFor={ele.value} className="tw-cursor-pointer">
                    {ele.label}
                  </label>
                </div>
              ))}
            </div>
            <div className="tw-flex tw-justify-center  ">
              <CustomButton
                loading={api.isLoading}
                className={"!tw-px-9 !tw-py-[14px] "}
                type="submit"
                count={8}
              >
                <span className={"!tw-text-base"}>Update</span>
              </CustomButton>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
};

export default CustomizeNavigation;
