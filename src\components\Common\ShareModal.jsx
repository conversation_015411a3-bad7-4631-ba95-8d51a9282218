import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";

import {
  FacebookShareButton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  FacebookIcon,
  TwitterIcon,
  LinkedinIcon,
  WhatsappIcon,
} from "next-share";

import { useState } from "react";
import toast from "react-hot-toast";
import { safeToast } from "@/utils/safeToast";
import { handleCopy } from "@/utils/function";

const ShareModal = ({ open, onClose, shareUrl, title }) => {
  const [copied, setCopied] = useState(false);

  // const handleCopy = async (url) => {
  //   if (url) {
  //     await window.navigator.clipboard.writeText(url);
  //     setCopied(true);
  //     safeToast.success("Copied to clipboard!");
  //   }
  // };

  return (
    <Modal
      classNames={{
        modal:
          "!tw-max-w-[100%] !tw-rounded-[1.25rem] !tw-m-0 lg:!tw-m-2 !tw-max-h-[100vh]",
        overlay: "!tw-bg-[#000000CC]",
      }}
      open={open}
      onClose={() => {
        onClose();
        setTimeout(() => setCopied(false), 400);
      }}
      center
    >
      <div className="tw-text-center tw-p-4">
        <h2 className="tw-text-xl tw-font-semibold tw-mb-4">Share this link</h2>

        {/* Share Buttons */}
        <div className="tw-flex tw-justify-center tw-gap-8 tw-mb-6">
          <FacebookShareButton url={shareUrl} blankTarget>
            <FacebookIcon size={60} round />
          </FacebookShareButton>

          <TwitterShareButton url={shareUrl} blankTarget title={title}>
            <TwitterIcon size={60} round />
          </TwitterShareButton>

          <LinkedinShareButton url={shareUrl} blankTarget title={title}>
            <LinkedinIcon size={60} round />
          </LinkedinShareButton>

          <WhatsappShareButton url={shareUrl} blankTarget title={title}>
            <WhatsappIcon size={60} round />
          </WhatsappShareButton>
        </div>

        {/* Copy URL Box */}
        <div className="tw-flex tw-items-center tw-justify-between tw-bg-[#f4f7fb] tw-w-[25rem]  tw-rounded-full tw-px-4 tw-py-2 tw-overflow-hidden">
          <span className=" tw-text-primary-black tw-truncate tw-font-mediums tw-line-clamp-1">
            {shareUrl}
          </span>
          <button
            onClick={() => {
              handleCopy(shareUrl, setCopied);
            }}
            className="tw-bg-primary-purple tw-text-white  tw-font-medium tw-rounded-full tw-px-4 tw-py-1.5 tw-ml-2"
          >
            {copied ? "Copied!" : "Copy"}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default ShareModal;
