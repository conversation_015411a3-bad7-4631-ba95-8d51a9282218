/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: ["class"],
	prefix: "tw-",
	content: [
		"./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	theme: {
		extend: {
			colors: {
				'primary-purple': '#6D11D2',
				'primary-black': '#2D394A',
				'secondary-text': '#787E89',
				'incenti-purple': '#6D11D2',
				'success-green-50': ' #ECFDF5',
				'success-green-100': '#D1FAE5',
				'success-green-200': '#A7F3D0',
				'success-green-300': '#6EE7B7',
				'success-green-400': '#34D399',
				'success-green-500': '#10B981',
				'success-green-600': '#059669',
				'success-green-700': '#047857',
				'success-green-800': '#065F46',
				'success-green-900': '#064E3B',
				'error-red-50': '#FEF2F2',
				'error-red-100': '#FEE2E2',
				'error-red-200': ' #FECACA',
				'error-red-300': '#FCA5A5',
				'error-red-400': '#F87171',
				'error-red-500': '#EF4444',
				'error-red-600': '#DC2626',
				'error-red-700': '#B91C1C',
				'error-red-800': '#991B1B',
				'error-red-900': '#7F1D1D',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				chart: {
					'1': 'hsl(var(--chart-1))',
					'2': 'hsl(var(--chart-2))',
					'3': 'hsl(var(--chart-3))',
					'4': 'hsl(var(--chart-4))',
					'5': 'hsl(var(--chart-5))'
				}
			},
			backgroundImage: {
				"projectCoverImgBg": "linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 30%)",
				"projectModal": "linear-gradient(177.96deg, rgba(255, 255, 255, 0) 1.56%, #FFFFFF 49.84%)"
			},
			maxWidth: {
				'425': '420px',
				exs: '319px',
				xs: '320px',
				xl: '1280px',
				'2xl': '1536px',
				'3xl': '1792px',
				'4xl': '2048px'
			},
			screens: {
				'425': '424px',
				'423': '423px',
				exs: {
					max: '423px'
				},
				xs: {
					min: '280px',
					max: ' 576px'
				},
				'xxl': {
					min: '1440px'
				},
				'3xl': {
					min: '1536px'
				},
				'4xl': {
					min: '1900px'
				}
			},
			lineHeight: {
				'full': '120%'
			},
			fontSize: {
				'incenti-xs': '0.5rem',
				'incenti-sm': '0.9rem',
				'incenti-10': '0.6rem',
				'incenti-12': '0.75rem',
				'incenti-14': '0.9rem',
				'incenti-18': '1.125rem',
				'incenti-20': '1.25rem',
				'incenti-24': '1.5rem',
				'incenti-32': '2rem',
				'incenti-48': '3rem',
				'incenti-base': '1rem',
				'incenti-xl': '1.25rem',
				'incenti-2xl': '1.563rem',
				'incenti-3xl': '1.953rem',
				'incenti-4xl': '2.441rem',
				'incenti-5xl': '3.052rem',
				'incenti-6xl': '3.8rem',
				'incenti-7xl': '4.8rem',
				'incenti-8xl': '5.5rem'
			},
			fontFamily: {
				inter: 'var(--font-inter)'
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			boxShadow: {
				'chatbot': "0px 6px 17px 0px #0000001A;",

			}
		}
	},
	plugins: [require("tailwindcss-animate")],
};
