"use client";
import React, { useState } from "react";

const SeeMoreText = ({
  text = "",
  // textAlign = "center",
  maxLines = 2,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const shouldTruncate = text?.length > 70;
  const visibleText =
    isExpanded || !shouldTruncate ? text : text?.slice(0, 120);

  const toggleExpanded = () => setIsExpanded((prev) => !prev);

  return (
    <div
      className={className}
      // style={{
      //   whiteSpace: "pre-line",
      //   overflow: "hidden",
      //   display: "-webkit-box",
      //   WebkitLineClamp: !isExpanded ? maxLines : 0,
      //   WebkitBoxOrient: "vertical",
      // }}
    >
      <span>{visibleText}</span>
      {shouldTruncate && (
        <span
          onClick={toggleExpanded}
          className="tw-text-primary-purple tw-font-semibold tw-cursor-pointer"
          style={{
            marginLeft: 4,
          }}
        >
          {isExpanded ? "..see less" : "..see more"}
        </span>
      )}
    </div>
  );
};

export default SeeMoreText;
