import { Skeleton } from "../ui/skeleton";

const CommunitySkeleton = ({ followerCount = 4, count = 12 }) => {
  return (
    <>
      {Array.from({ length: count }, (_, i) => i + 1)?.map((ele) => (
        <div key={ele}>
          <div className="tw-flex tw-gap-2 tw-items-center lg:tw-hidden">
            <Skeleton className={"exs:tw-p-16 tw-p-20 !tw-rounded-full"} />
            <div>
              <Skeleton className="tw-h-6 tw-w-[15rem]" />
              <div className="tw-flex  tw-items-center tw-gap-3 tw-my-2">
                <div className="tw-flex tw-items-center  tw-space-x-[-10px] ">
                  {Array.from({ length: followerCount }, (_, i) => i + 1)?.map(
                    (data, index) => (
                      <Skeleton
                        key={index}
                        className="!tw-rounded-full tw-border-2 tw-border-white tw-overflow-hidden tw-w-6 tw-h-6 tw-shadow-lg tw-relative"
                      />
                    )
                  )}
                </div>
                <Skeleton className="tw-h-3 tw-w-[10rem]" />
              </div>
              <Skeleton className={"tw-w-[45%] !tw-py-6 !tw-rounded-[50px]"} />
            </div>
          </div>
          <div className="tw-bg-[#F5F7F8] tw-rounded-3xl tw-px-5 tw-py-7  tw-hidden lg:tw-flex tw-flex-col tw-items-center tw-gap-4">
            <Skeleton className="tw-relative tw-h-[7.5rem]  tw-w-[7.5rem] !tw-rounded-full" />

            <Skeleton className="tw-text-primary-black tw-h-5 tw-w-[80%]  tw-font-bold tw-text-2xl tw-text-center" />
            <div className="tw-flex tw-justify-between tw-items-center tw-gap-3">
              <div className="tw-flex tw-items-center tw-justify-center tw-space-x-[-10px] ">
                {Array.from({ length: followerCount }, (_, i) => i + 1)?.map(
                  (data, index) => (
                    <Skeleton
                      key={index}
                      className="!tw-rounded-full tw-border-2 tw-border-white tw-overflow-hidden tw-w-6 tw-h-6 tw-shadow-lg tw-relative"
                    />
                  )
                )}
              </div>
              <Skeleton className="tw-h-3 tw-w-[10rem]" />
            </div>
            <div>
              <Skeleton className={"!tw-px-14 !tw-py-7 !tw-rounded-[50px]"} />
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default CommunitySkeleton;
