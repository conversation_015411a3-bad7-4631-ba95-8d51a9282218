import { Skeleton } from "../ui/skeleton";

const ProjectSkeleton = ({ count = 12 }) => {
  return (
    <>
      {Array.from({ length: count }, (_, i) => i + 1).map((ele) => (
        <div key={ele}>
          <Skeleton className={"tw-h-[12rem] tw-w-[100%] !tw-rounded-3xl"} />
          <div className="tw-space-y-2">
            {/* <Skeleton className="tw-h-4 tw-w-[130px] tw-my-2" /> */}
            <Skeleton className="tw-h-6 tw-w-[100%] !tw-rounded-xl tw-my-2" />
          </div>
        </div>
      ))}
    </>
  );
};

export default ProjectSkeleton;
