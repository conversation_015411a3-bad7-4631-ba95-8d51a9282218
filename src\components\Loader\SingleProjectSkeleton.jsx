import { CustomContainer } from "../Common/Custom-Display";
import { Skeleton } from "../ui/skeleton";
import ExplorePostSkeleton from "./ExplorePostSkeleton";

const SingleProjectSkeleton = ({ highlightsCount = 12, tabCount = 5 }) => {
  return (
    <div>
      <CustomContainer className="tw-py-4 tw-mt-4 tw-h-full">
        <Skeleton className="tw-relative tw-w-full tw-h-[21rem] !tw-rounded-3xl" />

        <Skeleton className="tw-mt-5 tw-mb-3 !tw-rounded-xl tw-h-7 tw-w-[28rem]" />

        <Skeleton className="tw-h-4 tw-w-[18rem] tw-my-2" />
        <Skeleton className="tw-h-4 tw-w-[12rem]" />
        {/* Highlights */}
        <div className="tw-w-full tw-overflow-hidden tw-block tw-my-8">
          <Skeleton className="tw-h-4 !tw-rounded-lg tw-w-[7rem] tw-mb-3" />
          <div className="tw-flex tw-gap-4 tw-items-center">
            {Array.from({ length: highlightsCount }, (_, i) => i + 10)?.map(
              (ele) => (
                <div key={ele}>
                  <Skeleton className={"tw-h-20 tw-w-20 !tw-rounded-full "} />
                  <Skeleton className={"tw-h-3 tw-w-[5rem] tw-mt-2"} />
                </div>
              )
            )}
          </div>
        </div>
        {/* Tabs */}
        <div className="tw-my-10 tw-flex tw-justify-between tw-items-center">
          <div className="tw-flex  tw-items-center tw-gap-4 ">
            {Array.from({ length: tabCount }, (_, i) => i + 1)?.map((ele) => (
              <Skeleton
                type="button"
                key={ele}
                className={`tw-py-6 tw-cursor-pointer !tw-rounded-full tw-px-12`}
              />
            ))}
          </div>
          <Skeleton
            className={`tw-py-7 tw-cursor-pointer !tw-rounded-full tw-px-14`}
          />
        </div>
        {/* <ExplorePostSkeleton count={3} /> */}
      </CustomContainer>
    </div>
  );
};

export default SingleProjectSkeleton;
