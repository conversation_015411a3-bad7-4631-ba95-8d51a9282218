import { EmptyDataIcon } from "@/utils/icons";

const Empty = ({
  width = 261,
  height = 248,
  label = "No Record Found",
  icon = null,
  className = "",
  iconRequired = true,
  subLabel = null,
  subClassName = "",
}) => {
  return (
    <div className="tw-flex tw-flex-col tw-items-center tw-gap-5">
      {icon ? (
        icon
      ) : iconRequired ? (
        <EmptyDataIcon width={width} height={height} />
      ) : null}
      <div className="tw-flex tw-flex-col tw-items-center tw-gap-1">
        <p
          className={`${className} tw-text-primary-black tw-text-xl tw-font-semibold`}
        >
          {label}
        </p>
        {subLabel && (
          <p className={`${subClassName} tw-text-primary-black tw-text-sm`}>
            {subLabel}
          </p>
        )}
      </div>
    </div>
  );
};

export default Empty;
