"use client";
import Loader from "../Loader/Loader";
import {
  BookmarkFillOutIcon,
  BookmarkIcon,
  CloseModalIcon,
  HeartFillOutIcon,
  HeartIcon,
  MessageIcon,
  ReportPostIcon,
  ShareIcon,
} from "@/utils/icons";
import moment from "moment";
import Image from "next/image";
import CustomTitle from "../Common/CustomTitle";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import { blurDataURL } from "@/utils/function";
import authStorage from "@/utils/API/AuthStorage";
import { bool } from "yup";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import PostImageCarousel from "./PostImageCarousel";
import PostPreviewImgCarousel from "./PostPreviewImgCarousel";
import Empty from "../Common/Empty";

const PostPreview = ({
  isOpen,
  setIsOpen,
  isLoading,
  data,
  setData,
  getLikeData,
  getWishlistData,
  commentHandler,
  postState,
  setPostState,
  isDraft = false,
  isShowFollowButton = true,
  isCurrentUser,
  followAndUnfollowHandler = () => {},
  router,
  path,
  setIsShareOpen,
  reportPostHandler,
}) => {
  const icons = [
    {
      id: 1,
      icon: postState?.isLiked ? (
        <HeartFillOutIcon size={22} />
      ) : (
        <HeartIcon stroke="#2D394A" size={22} />
      ),
      onClick: () => {
        setPostState((prev) => ({
          ...prev,
          likedCount: prev.isLiked ? prev.likedCount - 1 : prev.likedCount + 1,
          isLiked: !prev.isLiked,
        }));
        // console.log(id, "Like");
        getLikeData(data?.id);
      },
    },
    {
      id: 2,
      icon: <MessageIcon stroke="#2D394A" size={22} />,
      onClick: () => {
        commentHandler(data?.id);
      },
    },
    {
      id: 3,
      icon: postState?.isWishListed ? (
        <BookmarkFillOutIcon />
      ) : (
        <BookmarkIcon stroke="#2D394A" size={22} />
      ),
      onClick: () => {
        setPostState((prev) => ({
          ...prev,
          isWishListed: !prev.isWishListed,
        }));
        getWishlistData(data?.id);
      },
    },
    {
      id: 4,
      icon: <ShareIcon stroke="#2D394A" size={22} />,
      onClick: () => {
        setIsShareOpen(true);
      },
    },
    {
      id: 5,
      icon: <ReportPostIcon size={22} />,
      onClick: (ele) => {
        reportPostHandler(ele);
      },
    },
  ];
  // console.log(userData?.id, data?.User?.id);
  // console.log(data, isLoading);

  return (
    <>
      <Modal
        open={isOpen}
        classNames={{
          modal: "!tw-mt-[2.25rem] !tw-mx-[1.2rem] !tw-mb-[1.2rem]",
          overlay: "!tw-bg-[#000000CC]",
        }}
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
            width: "85rem",
            borderRadius: "1rem",
            // zIndex: 2,
          },
        }}
        onClose={() => {
          setIsOpen(false);
          // router.push(path, { scroll: false });
          window.setTimeout(() => {
            setData(null);
          }, 500);
        }}
        closeIcon={<></>}
      >
        <div className="tw-sticky tw-top-8">
          {!isLoading && (
            <div className="tw-absolute tw-top-0 -tw-right-24 tw-flex tw-flex-col tw-items-center tw-gap-5">
              <button
                className="tw-outline-none "
                type="button"
                onClick={() => {
                  setIsOpen(false);
                  // router.push(path, { scroll: false });
                }}
              >
                <CloseModalIcon size={35} />
              </button>
              {!isDraft &&
                icons?.map((ele) => (
                  <button
                    onClick={() => {
                      ele?.onClick(ele);
                    }}
                    key={ele?.id}
                    type="button"
                    className="tw-bg-white tw-outline-none tw-rounded-full tw-p-3 tw-flex tw-justify-center tw-items-center"
                  >
                    {ele?.icon}
                  </button>
                ))}
            </div>
          )}
        </div>
        {isLoading ? (
          <div className="tw-flex tw-justify-center tw-items-center">
            <Loader />
          </div>
        ) : data ? (
          <div className="xl:tw-text-base tw-m-6 ">
            {/* tw-h-[30rem] */}
            <div className="tw-w-full 2xl:tw-col-span-8 xl:tw-col-span-7 lg:tw-col-span-8 md:tw-col-span-7 tw-col-span-12">
              <div className="tw-flex tw-flex-col 2xl:tw-gap-y-1 xl:tw-gap-y-2 lg:tw-gap-y-2.5 tw-gap-y-2">
                <div className="tw-flex tw-gap-10 tw-items-center">
                  <Link
                    href={
                      !isCurrentUser ? "/profile" : `/user/${data?.User?.slug}`
                    }
                  >
                    <div className="tw-flex tw-flex-row tw-gap-2">
                      <div
                        className={`tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[42px] tw-h-[42px] ${
                          !data?.User?.image && "tw-bg-primary-purple"
                        } tw-text-white tw-rounded-full tw-overflow-hidden tw-cursor-pointer`}
                      >
                        {data?.User?.image ? (
                          <Image
                            src={
                              data?.User?.image ??
                              "https://i.pravatar.cc/100?img=5"
                            }
                            alt="User"
                            fill
                            className="tw-object-cover tw-rounded-full"
                          />
                        ) : (
                          <span className="tw-text-primary-1100 tw-font-merriweather tw-font-bold md:tw-text-3xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                            {data?.User?.firstName?.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div className="">
                        <h4 className="tw-text-base tw-font-semibold">{`${
                          data?.User?.firstName
                        } ${
                          data?.User?.lastName ? data?.User?.lastName : ""
                        }`}</h4>

                        <p className="tw-text-primary-black tw-text-opacity-70 tw-text-[12px]">
                          {moment(data?.createdAt)?.format("MMM DD, YYYY")}
                        </p>
                      </div>
                    </div>
                  </Link>

                  <div>
                    {isCurrentUser && isShowFollowButton && (
                      <button
                        type="button"
                        onClick={() => {
                          setPostState((prev) => ({
                            ...prev,
                            isFollowed: prev?.isFollowed === 1 ? 0 : 1,
                          }));
                          followAndUnfollowHandler(
                            data?.User?.id,
                            +!postState?.isFollowed
                          );
                        }}
                        className={`${
                          !postState?.isFollowed
                            ? "tw-bg-primary-purple tw-text-white"
                            : "tw-bg-transparent tw-border tw-border-[#787E89] tw-text-[#787E89]"
                        } tw-text-sm tw-py-2 tw-px-5  tw-rounded-full tw-font-medium`}
                      >
                        {+postState?.isFollowed ? "Following" : "Follow"}
                      </button>
                    )}
                  </div>
                </div>
                {data?.Project && (
                  <Link
                    href={`/projects/${data?.Project?.id}`}
                    className="tw-mt-3 tw-flex tw-flex-row tw-gap-2 tw-items-center"
                  >
                    <div className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px]  tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center">
                      {
                        <Image
                          src={
                            data?.Project?.image ??
                            "https://s3-alpha-sig.figma.com/img/ae01/6fc3/0c7f4914bd4bb5256ed17debd1ce787e?Expires=1743379200&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=nvTjkpvoxXInLuCUmRrOwqUdcm6atp8~3zlX-26r9nE-MnVVf6jUmkO0ktk47idwAuniuf69lgfFvMlmYDVpeTyLTDE7PoiWhitp9lrKYXHiHV1Ndc3QeOwl1gnUzjbgDXeA8V7MNmojXd8SGiDod4lF6mQ7C1NV6YuPX4XL35K0TUCo8HLvyIJVqINm1n-Dc6sHtfyUY3pYhTX1Ox8DGZ-B-U2~b-eNL-MXMHKl0cLTcZhgCkRtWqpjhzzQx9k9bW2ga-wcBzd4OmYM26-Xe1Is829wdbJU5g7ch~sZngMo2-x6aEJxjLD6LHPdb5jJrH8oQKxmPMjkjN4wnlEI2Q__"
                          }
                          alt="Project"
                          fill
                          placeholder="blur"
                          blurDataURL={blurDataURL(20, 20)}
                          className="tw-object-cover tw-rounded-full"
                        />
                      }
                    </div>
                    <div className="tw-flex tw-gap-2 tw-items-center">
                      <p className="tw-font-semibold tw-text-[13px]">
                        {data?.Project?.name}
                      </p>
                      <ChevronRight size={18} />
                    </div>
                  </Link>
                )}
                <div className="tw-flex tw-justify-between tw-mt-5 tw-mb-3">
                  <div>
                    <CustomTitle htag={2} name={`${data?.title}`} />
                  </div>
                </div>
                {/* {data?.media && data?.media?.length > 0 && (
               <div className="tw-my-3 tw-flex tw-justify-center">
                 <PostPreviewImgCarousel
                   imageClass=""
                   imagArr={data?.media}
                 />
               </div>
             )} */}
              </div>
            </div>
            <div
              className="xl:tw-text-base !tw-relative project-description "
              dangerouslySetInnerHTML={{ __html: data?.description }}
            />
          </div>
        ) : (
          <>
            <div className="tw-flex tw-justify-center tw-items-center tw-h-[25rem]">
              <Empty />
            </div>
          </>
        )}
      </Modal>
      {/* <Dialog
        modal
        className="show-scrollbar"
        open={false}
   
      >
        <DialogContent className="!tw-max-w-[40rem] overflow-y-scroll max-h-screen ">
          
          <DialogHeader className="">
            <DialogTitle className="tw-hidden">
              <p className="tw-hidden"></p>
            </DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog> */}
    </>
  );
};

export default PostPreview;
