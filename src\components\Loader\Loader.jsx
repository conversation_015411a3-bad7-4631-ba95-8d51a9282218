import React from "react";

const Loader = ({ color = "#6D11D2", size = "24px", zIndex = 999 }) => {
  return (
    // <div className="flex justify-center items-center">
    //     <div className="loader"></div>
    // </div>
    <div
      className="dot-spinner"
      style={{
        "--uib-color": color,
        "--uib-size": size,
        zIndex: zIndex,
      }}
    >
      <div className="dot-spinner__dot "></div>
      <div className="dot-spinner__dot"></div>
      <div className="dot-spinner__dot"></div>
      <div className="dot-spinner__dot"></div>
      <div className="dot-spinner__dot"></div>
      <div className="dot-spinner__dot"></div>
      <div className="dot-spinner__dot"></div>
      <div className="dot-spinner__dot"></div>
    </div>
  );
};

export default Loader;
