import { Skeleton } from "../ui/skeleton";

const NotificationSkeleton = ({ count = 5 }) => {
  return (
    <>
      <div className="tw-my-5">
        {Array.from({ length: count }, (_, i) => i + 1).map((ele) => (
          <div className="tw-mb-5" key={ele}>
            <Skeleton className={"tw-py-14 !tw-rounded-3xl"} />
          </div>
        ))}
      </div>
    </>
  );
};

export default NotificationSkeleton;
