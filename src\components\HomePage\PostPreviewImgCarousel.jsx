"use client";
import Image from "next/image";
// import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import { blurDataURL, moveCoverImageToFront } from "@/utils/function";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";

const PostPreviewImgCarousel = ({ imagArr, imageClass = null }) => {
  return (
    <div className="tw-relative tw-w-full">
      {/* React-responsive carousel */}
      <div id="post-preview" className={`tw-w-full`}>
        <Carousel
          showThumbs={false}
          swipeable
          emulateTouch
          showArrows={false}
          showStatus={false}
          showIndicators={moveCoverImageToFront(imagArr)?.length > 1}
        >
          {moveCoverImageToFront(imagArr)?.map((ele, i) => (
            <div
              key={`${ele?.id}-${i}`}
              className={`tw-relative ${
                moveCoverImageToFront(imagArr).length > 1 && "tw-cursor-pointer"
              } tw-gap-4 tw-h-[30rem] ${imageClass}`}
            >
              <Image
                src={ele?.link}
                fill
                alt={"Toolplate Blog Image"}
                className="tw-rounded-[20px] tw-border-gray-color tw-overflow-hidden tw-object-cover tw-relative"
                title={"Toolplate Blog"}
                sizes="auto"
                blurDataURL={blurDataURL(400, 250)}
                placeholder="blur"
              />
            </div>
          ))}
        </Carousel>
      </div>
    </div>
  );
};

export default PostPreviewImgCarousel;
