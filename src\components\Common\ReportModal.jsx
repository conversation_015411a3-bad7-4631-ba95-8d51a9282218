import { ChevronRight } from "lucide-react";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import Loader from "../Loader/Loader";
import { useEffect, useState } from "react";

const ReportModal = ({
  isOpen,
  setIsOpen,
  title = "Why are you reporting this post?",
  description = "Your report is anonymous. If someone is in immediate danger, call the local emergency services - don't wait.",
  reportReasons = [
    {
      id: 1,
      reason: "I just don't Like it",
    },
    {
      id: 2,
      reason: "Scam, fraud or spam",
    },
    {
      id: 3,
      reason: "False information",
    },
  ],
  isLoading,
  onConfirm = () => {},
}) => {
  const [selectedReason, setSelectedReason] = useState(null);
  useEffect(() => {
    setSelectedReason(null);
  }, [isOpen]);
  return (
    <>
      <Modal
        open={isOpen}
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[30rem] !tw-rounded-[1.25rem] !tw-m-0 md:tw-m-3",
          closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        onClose={() => {
          setIsOpen(false);
          setSelectedReason(null);
        }}
        center
      >
        {" "}
        <div className="tw-pb-5">
          <p className="tw-text-2xl tw-text-center tw-font-semibold tw-border-b tw-pb-2">
            Report
          </p>
          <div className="tw-flex  tw-flex-col tw-items-center tw-py-5 tw-gap-3">
            <p className="tw-text-primary-black  tw-font-semibold tw-text-xl tw-text-center">
              {title}
            </p>
            <p className="tw-mx-auto tw-text-[#787E89] tw-text-sm tw-max-w-[20rem] tw-text-center">
              {description}
            </p>
          </div>
          <div className="tw-w-full">
            {reportReasons?.map((ele, i) => (
              <button
                disabled={isLoading}
                className={`tw-flex tw-justify-between tw-items-center tw-py-3 tw-px-4 tw-w-full ${
                  reportReasons.length === i + 1 ? "tw-border-y" : "tw-border-t"
                }`}
                key={ele?.id}
                onClick={() => {
                  setSelectedReason(ele.reason);
                  onConfirm(ele?.reason);
                }}
              >
                <p>{ele?.reason}</p>
                <div>
                  {isLoading && ele.reason === selectedReason ? (
                    <Loader />
                  ) : (
                    <ChevronRight size={20} />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ReportModal;
