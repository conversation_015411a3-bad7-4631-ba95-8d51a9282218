import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc'; // For handling UTC times
import timezone from 'dayjs/plugin/timezone'; // For handling UTC times
import { DefaultFileIcon, DocumentIcon, ExcelIcon, ImgPreviewIcon, PdfIcon, PPTIcon, ShareIcon, TextFileIcon, VideoIcon, ZipIcon } from "./icons";
import { Flag } from "lucide-react";
import { safeToast } from "./safeToast";
import { marked } from "marked";
// Extend Day.js with the plugins
dayjs.extend(utc);
dayjs.extend(timezone);

export const convertUTCtoLocal = (utcTime, format = "YYYY-MM-DDTHH:mm:ss[Z]") => {
    return dayjs.utc(utcTime).local().format(format);
}


export const API_BASE_URL =
    process.env.NEXT_PUBLIC_ENV == "production"
        ? process.env.NEXT_PUBLIC_BASE_URL
        : process.env.NEXT_PUBLIC_DEV_BASE_URL;
export const WEBSITE_URL =
    process.env.NEXT_PUBLIC_ENV == "production"
        ? process.env.NEXT_PUBLIC_WEB_URL
        : process.env.NEXT_PUBLIC_DEV_WEB_URL;
export const getToken = () => {
    if (typeof window !== "undefined") {
        // const ProfileData = localStorage.getItem("profile");
        const Token = localStorage.getItem("token");
        if (
            // localStorage.getItem("ItemToken") &&
            Token
            // &&
            // ProfileData
        ) {
            return {
                token: Token,
                // profile: ProfileData 
            };
        }
    }
    return null;
};

export const LINKEDIN_REDIRECT_URL =
    process.env.NEXT_PUBLIC_ENV == "staging"
        ? process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URL
        : process.env.NEXT_PUBLIC_LINKEDIN_DEV_REDIRECT_URL;


// export const getToken = () => {
//     if (typeof window !== "undefined") {
//       //   const ProfileData = localStorage.getItem("profile");
//       const Token = localStorage.getItem("token");
//       if (Token) {
//         return { token: Token };
//       }
//     }
//     return null;
//   };
export const getProfile = () => {
    if (typeof window !== "undefined") {
        //   const ProfileData = localStorage.getItem("profile");
        const profile = JSON.parse(localStorage.getItem("registerValues"));
        if (profile) {
            return profile;
        }
    }
    return null;
};

export function convertSeconds(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    return { hours, minutes, remainingSeconds };
}
export const isFunctionEmpty = (fn) => {
    // const fnString = fn.toString();
    // const body = fnString.match(/{([\s\S]*)}/); // Extract the function body between `{` and `}`
    // return body && body[1].trim().length === 0;
    return typeof (fn) === "function";

};
export function removeHtmlTags(htmlString) {
    if (!htmlString) return ""; // Handle null or undefined input
    return htmlString?.replace(/<\/?[^>]+(>|$)/g, "")?.trim();
}

const shimmer = (w, h) => `
    <svg width="${w}" height="${h}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <defs>
        <linearGradient id="g">
          <stop stop-color="#CFCFCF" offset="20%" />
          <stop stop-color="#CFCFCF" offset="50%" />
          <stop stop-color="#CFCFCF" offset="70%" />
        </linearGradient>
      </defs>
      <rect width="${w}" height="${h}" fill="#CFCFCF" />
      <rect id="r" width="${w}" height="${h}" fill="url(#g)" />
      <animate xlink:href="#r" attributeName="x" from="-${w}" to="${w}" dur="1s" repeatCount="indefinite"  />
    </svg>
`;
const toBase64 = (str) =>
    typeof window === "undefined"
        ? Buffer.from(str).toString("base64")
        : window.btoa(str);

export const blurDataURL = (width, height) =>
    `data:image/svg+xml;base64,${toBase64(shimmer(width, height))}`;

export function isJsonString(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}

export const validValue = (val) =>
    val !== null &&
    val !== undefined &&
    val !== "undefined" &&
    val !== "" &&
    !!val;

export const apiGenerator = (apiObject, exchangePair = {}, join = null) => {
    const apiObj = { ...apiObject };
    if (Object.keys(exchangePair).length) {
        Object.keys(exchangePair).forEach((el) => {
            apiObj.endpoint = apiObj.endpoint.replace(`:${el}`, exchangePair[el]);
        });
    }

    if (join) {
        apiObj.endpoint = `${apiObj.endpoint}${join}`;
    }
    return apiObj;
};

export const buildQueryString = (params) => {
    const queryParts = [];

    for (const key in params) {
        if (params.hasOwnProperty(key)) {
            const value = params[key];

            if (key.startsWith("autogenerate-mul-array-") && Array.isArray(value)) {
                // Handle autogenerate-mul-array- with array values
                const arrayKey = key.slice("autogenerate-mul-array-".length);
                value.forEach((item) => {
                    queryParts.push(
                        `${encodeURIComponent(arrayKey)}=${encodeURIComponent(item)}`
                    );
                });
            } else {
                // Handle other cases
                queryParts.push(
                    `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
                );
            }
        }
    }

    return queryParts.length > 0 ? `?${queryParts.join("&")}` : "";
};

export function moveCoverImageToFront(images = []) {
    const index = images?.findIndex(img => img?.isCoverImage);
    if (index > 0) {
        images?.unshift(images?.splice(index, 1)[0]); // Remove and insert at index 0
    }
    return images;
}


export const stories = [
    {
        id: 1,
        img: "https://picsum.photos/200/300?random=1",
        profile: "https://i.pravatar.cc/100?img=1",
        label: "Photography",
        viewed: false,
        key: "john doe",
    },
    {
        id: 2,
        img: "https://picsum.photos/200/300?random=2",
        profile: "https://i.pravatar.cc/100?img=2",
        label: "Captures",
        viewed: false,
        key: "Jerry",
    },

];

export const convertImage = (htmlString) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, "text/html");
    const imgTags = doc.querySelectorAll("img");

    const srcValues = [...imgTags].map(img => img.src);

    return srcValues
}

export function isBase64Image(str) {
    const base64Regex = /^data:image\/(png|jpeg|jpg|gif|webp|bmp|svg\+xml);base64,[A-Za-z0-9+/=]+$/;
    return typeof str === 'string' && base64Regex.test(str);
}

export function base64ToFile(base64String, fileName) {
    // Extract the mime type from Base64
    const arr = base64String.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];

    // Decode Base64 to raw data
    const byteCharacters = atob(arr[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    // Convert raw data to typed array
    const byteArray = new Uint8Array(byteNumbers);

    // Create Blob and File object
    const file = new File([byteArray], fileName, { type: mime });
    return file;
}

export function replaceBase64WithLinks(htmlString, imagesArray) {
    // console.log(imagesArray, "imgs")
    return htmlString.replace(/<img\s+[^>]*src=["'](data:image\/[a-zA-Z]+;base64,[^"']+)["'][^>]*>/g, (match, base64Src) => {
        const imageObj = imagesArray.find(({ src }) => src === base64Src);
        return imageObj ? match.replace(base64Src, imageObj.link) : match;
    });
}

export function removeBackgroundColor(html) {
    // return html.replace(/background-color\s*:\s*[^;"]*;?/gi, '');
    const color = [
        'black',
        '#000000',
        '#000',
        '#000000ff',
        'rgb(0, 0, 0)',
        'rgba(0, 0, 0, 1)',
        'rgba(0, 0, 0, 1.0)',
        'white',
        '#ffffff',
        '#fff',
        '#ffffffff',
        'rgb(255, 255, 255)',
        'rgba(255, 255, 255, 1)',
        'rgba(255, 255, 255, 1.0)'
    ];

    return html.replace(/style\s*=\s*"([^"]*)"/gi, (match, styleContent) => {
        const styles = styleContent.split(';').map(s => s.trim()).filter(Boolean);

        const filtered = styles.filter(style => {
            const lower = style.toLowerCase();

            // Remove background-color always
            if (lower.startsWith('background-color')) return false;

            // Remove color only if black or white
            if (lower.startsWith('color')) {
                const value = lower.split(':').pop().trim();
                return !color.includes(value);
            }

            return true; // keep other styles
        });

        if (filtered.length === 0) {
            return ''; // no style left, remove attribute
        }

        return `style="${filtered.join('; ')}"`;
    });
}

export const otherProjectSetting = [
    {
        label: "Share",
        className: "",
        icon: <ShareIcon size={17} stroke="#2D394A" />,
        onClick: () => { },
    },
    {
        label: "Report this",
        className: "",
        icon: <Flag size={17} stroke="#2D394A" />,
        onClick: () => { },
    },
];

export function isValidURL(value) {
    try {
        new URL(value);
        return true;
    } catch (err) {
        return false;
    }
}

export const RESPONSE_STATUS = {
    SUCCESS: "success"
}

export const OS_TYPE = {
    WINDOWS: "Windows",
    MACOS: "macOS"
}

export function getOS() {
    const userAgent = window.navigator.userAgent;

    if (/Win/i.test(userAgent)) {
        return OS_TYPE.WINDOWS;
    }

    if (/Macintosh/i.test(userAgent)) {
        return OS_TYPE.MACOS;
    }

    return "Other OS";
}


export const getImageHeight = (src, maxWidth = 400) => {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
            const aspectRatio = img.width / img.height;
            let displayHeight = maxWidth / aspectRatio;


            // if (displayHeight > maxHeight) {
            //     displayHeight = maxHeight;
            // }

            resolve(displayHeight);
        };
        img.onerror = () => resolve(0); // On error, return 0 height
        img.src = src;
    });
};

export const getMaxImageHeight = async (imageList, maxHeight = 520) => {
    const maxWidth = 400;
    // const maxWidth = window.innerWidth;



    const heights = await Promise.all(
        imageList.map((item) => getImageHeight(item.src, maxWidth))
    );
    return heights.length === 0 ? maxHeight : Math.max(...heights);
};


export const todoPriorityDiv = {
    1: {
        label: "Low",
        bgClass: "#10BE5B",

    }, 2: {
        label: "Medium",
        bgClass: "#E8AB2E",

    }, 3: {
        label: "High",
        bgClass: "#EF3B41",

    }

}

export const downloadFileFromUrl = async (url, filename) => {
    try {
        const response = await fetch(url);
        const blob = await response.blob();

        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = filename || "download";
        document.body.appendChild(link);
        link.click();
        link.remove();
    } catch (error) {
        console.error("File download failed:", error);
    }
};

export function getFileExtension(filename) {
    if (typeof filename !== 'string') return '';
    const lastDotIndex = filename.lastIndexOf('.');
    const lastSlashIndex = Math.max(filename.lastIndexOf('/'), filename.lastIndexOf('\\'));

    // Ensure the dot is after the last path separator
    if (lastDotIndex > lastSlashIndex) {
        return filename.slice(lastDotIndex + 1).toLowerCase();
    }
    return '';
}

export function getFileTypeIcon(fileName) {
    const imageExtensions = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tif', 'tiff',
        'webp', 'heic', 'heif', 'ico'
    ];
    const videoExtensions = ['mp4', 'mov', 'avi', 'wmv', 'flv', 'mkv'];
    const documentExtensions = ['doc', 'docx'];
    const presentationExtensions = ['pptx', 'ppt', 'ppsx', 'pps', 'odp'];
    const spreadsheetExtensions = ['xlsx', 'xls', 'ods'];
    const textExtensions = ['txt', 'rtf', 'csv', 'tsv'];
    const zipExtensions = [
        'zip', 'rar', '7z', 'tar.gz', 'tar.bz2', 'gz', 'bz2',
        'tgz', 'tbz2', 'zipx', 'z', 'tar.xz', 'xz'
    ];
    const pdfExtensions = ["pdf"]

    const ext = getFileExtension(fileName)?.toLowerCase();

    if (documentExtensions.includes(ext)) return <DocumentIcon height={80} width={60} />;
    if (pdfExtensions.includes(ext)) return <PdfIcon height={80} width={60} />;
    if (imageExtensions.includes(ext)) return <ImgPreviewIcon height={80} width={60} />;
    if (videoExtensions.includes(ext)) return <VideoIcon height={80} width={80} />;
    if (presentationExtensions.includes(ext)) return <PPTIcon height={80} width={60} />;
    if (spreadsheetExtensions.includes(ext)) return <ExcelIcon height={80} width={60} />;
    if (textExtensions.includes(ext)) return <TextFileIcon height={80} width={60} />;
    if (zipExtensions.includes(ext)) return <ZipIcon height={80} width={60} />;

    return <DefaultFileIcon height={80} width={60} />;


}

function redirectToPage(value, router, progress) {
    progress.start()
    router.push(value)
}

export function parseNotification(message, actionStatus, router, progress, ele, loginUser) {
    const joinRegex = /^(.*?) has joined your (.*?) community\.?$/;
    const followRegex = /^(.*?) started following you\.?$/;
    const postRegex = /^(.*?) wants to add a post in your (.*?) community\.?$/;
    const projectRegex = /^(.*?) invited you to (.*?) project\.?$/;
    const invitationRegex = /^(.*?) has (.*?) the invitation to join your project (.*?)\.?$/;
    const subProjectInvitationRegex = /^(.*?) invited you to (.*?) which belongs to (.*?)\.?$/;
    const acceptedInvitationRegex = /^You have(.*?) (.*?)'s invitation for (.*?) project\.?$/;
    const postCommentRegex = /^(.*?) commented on your post: (.*?)\.?$/;
    const likePostRegex = /^(.*?) liked your post: (.*?)\.?$/;
    const memberLeftRegex = /^(.*?) exited from your Project: (.*?)\.?$/;
    const todoRegex = /^You have this To Do due today: (.*?)\.?$/;
    const projectMentionRegex = /^Your Project:(.*?) is mentioned in a Post:(.*?)\.?$/;
    const postMentionRegex = /^You were mentioned on this Post:(.*?)\.?$/;

    if (joinRegex.test(message)) {
        const [, name, community] = message.match(joinRegex);
        return {
            type: "joinCommunity",
            name,
            community: <>
                <span className="tw-font-bold tw-cursor-pointer"> {community}</span> community
            </>,
            middleLabel: " has joined your "
        };
    } else if (followRegex.test(message)) {
        const [, name] = message.match(followRegex);
        return {
            type: "follow",
            name,
            middleLabel: " started following you"
        };
    } else if (postRegex.test(message) && actionStatus === null) {
        const [, name, community] = message.match(postRegex);
        return {
            type: "post_request",
            name,
            community: <>
                <span onClick={(e) => {
                    e.stopPropagation()

                    redirectToPage(`/communities/${JSON.parse(ele?.clickAction)?.communitySlug}`, router, progress)

                }} className="tw-font-bold tw-cursor-pointer"> {community}</span> community
            </>,
            middleLabel: " wants to add a post in your "
        };
    }
    else if (postRegex.test(message) && actionStatus !== null) {
        const [, name, community] = message.match(postRegex);
        return {
            type: "post_requestStatus",
            name: "",
            userName: name,
            community: <>
                <span onClick={(e) => {
                    e.stopPropagation()

                    redirectToPage(`/communities/${JSON.parse(ele?.clickAction)?.communitySlug}`, router, progress)

                }} className="tw-font-bold tw-cursor-pointer"> {community}</span> community
            </>,
            middleLabel: <>
                You have {actionStatus ? "accepted" : "rejected"} <span className="tw-font-bold">{name}</span>'s post in your
            </>
        };
    }
    else if (projectRegex.test(message) && actionStatus === null) {
        const [, name, project] = message.match(projectRegex);
        return {
            type: "project",
            name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation()

                    // if (JSON.parse(ele?.clickAction)?.isPrivateProject) {
                    //     safeToast.error("This Project is Private. Please accept the invitation to view this project");
                    //     return;
                    // }

                    // redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold tw-cursor-pointer"> {project}</span> project
            </>,
            middleLabel: " invited you to "
        };
    }
    else if (projectRegex.test(message) && actionStatus !== null) {
        const [, name, project] = message.match(projectRegex);
        return {
            type: "invitationStatus",
            name: ``,
            userName: name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation()
                    // if (JSON.parse(ele?.clickAction)?.isPrivateProject && !actionStatus) {
                    //     safeToast.error("This Project is Private. Please accept the invitation to view this project");
                    //     return;
                    // }
                    // redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold tw-cursor-pointer"> {project}</span> project
            </>,
            middleLabel: <>
                You have {actionStatus ? "accepted" : "rejected"} <span className="tw-font-bold">{name}</span>'s invitation for
            </>
        };
    }
    else if (invitationRegex.test(message)) {
        const [, name, reason, project] = message.match(invitationRegex);

        return {
            type: "invitation",
            name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation()

                    // redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold"> {project}</span> project
            </>,
            middleLabel: ` has ${reason} the invitation to join your project `
        };
    }
    else if (subProjectInvitationRegex.test(message) && actionStatus === null) {
        const [, name, subProject, project] = message.match(subProjectInvitationRegex);

        return {
            type: "subProjectInvitation",
            name,
            community: <>
                <span onClick={(e) => {
                    e.stopPropagation()
                    // if (JSON.parse(ele?.clickAction)?.isPrivateProject) {
                    //     safeToast.success("This Project is Private");
                    //     return;
                    // }
                    redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.parentSlug}`, router, progress)

                }} className="tw-font-bold"> {project}</span>&nbsp;project
            </>,
            middleLabel: <>
                &nbsp;invited you to   <span onClick={(e) => {
                    // e.stopPropagation()
                    // if (JSON.parse(ele?.clickAction)?.isPrivateSubProject) {
                    //     safeToast.error("This Project is Private. Please accept the invitation to view this project");
                    //     return;
                    // }
                    // redirectToPage(`/sub-project/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold"> {subProject}</span> which belongs to
            </>
        };
    }
    else if (subProjectInvitationRegex.test(message) && actionStatus !== null) {
        const [, name, subProject, project] = message.match(subProjectInvitationRegex);

        return {
            type: "subProjectInvitationStatus",
            name: ``,
            userName: name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation()
                    // if ((JSON.parse(ele?.clickAction)?.isPrivateProject || JSON.parse(ele?.clickAction)?.isPrivateSubProject) && !actionStatus) {
                    //     safeToast.error("This Project is Private. Please accept the invitation to view this project");
                    //     return;
                    // }
                    // const baseRoute = JSON.parse(ele?.clickAction)?.ParentId ? "/sub-project" : "/projects";
                    // redirectToPage(`${baseRoute}/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold tw-cursor-pointer"> {subProject}</span> project
            </>,
            middleLabel: <>
                You have {actionStatus ? "accepted" : "rejected"} <span className="tw-font-bold">{name}</span>'s invitation for
            </>
        };
    }
    else if (acceptedInvitationRegex.test(message)) {
        const [, reason, name, project] = message.match(acceptedInvitationRegex);

        return {
            type: "invitationAccept",
            // name: `You have ${reason} ${name}'s`,]
            userName: name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation()
                    // if (JSON.parse(ele?.clickAction)?.isPrivateProject && actionStatus === "rejected") {
                    //     safeToast.error("This Project is Private. Please accept the invitation to view this project");
                    //     return;
                    // }
                    // redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold tw-cursor-pointer"> {project}</span> project
            </>,
            middleLabel: <>
                You have {actionStatus ? "accepted" : "rejected"} <span className="tw-font-bold">{name}</span>'s invitation for
            </>
        };
    }
    else if (postCommentRegex.test(message)) {
        const [, name, post] = message.match(postCommentRegex);

        return {
            type: "postComment",
            name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation();

                    // redirectToPage(`/posts/${JSON.parse(ele?.clickAction)?.postSlug}`, router, progress)

                }} className="tw-font-bold">{post}</span>
            </>,
            middleLabel: ` commented on your post: `
        };
    }
    else if (likePostRegex.test(message)) {
        const [, name, post] = message.match(likePostRegex);

        return {
            type: "likePost",
            name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation();

                    // redirectToPage(`/posts/${JSON.parse(ele?.clickAction)?.postSlug}`, router, progress)

                }} className="tw-font-bold">{post}</span>
            </>,
            middleLabel: ` liked your post: `
        };
    }
    else if (memberLeftRegex.test(message)) {
        const [, name, project] = message.match(memberLeftRegex);

        return {
            type: "memberLeft",
            name,
            community: <>
                <span onClick={(e) => {
                    e.stopPropagation();

                    redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                }} className="tw-font-bold">{project}</span>
            </>,
            middleLabel: ` exited from your Project: `
        };
    }
    else if (todoRegex.test(message)) {
        const [, todoName] = message.match(todoRegex);

        return {
            type: "todoDue",
            userName: loginUser?.name,
            community: <>
                <span onClick={(e) => {
                    // e.stopPropagation();

                    // redirectToPage(`/to-dos`, router, progress)

                }} className="tw-font-bold">{todoName}</span>
            </>,
            middleLabel: `You have this To Do due today: `
        };
    }
    else if (projectMentionRegex.test(message)) {
        const [, projectName, post] = message.match(projectMentionRegex);

        return {
            type: "projectMention",
            userName: loginUser?.name,
            middleLabel: <>
                <span>
                    Your Project:&nbsp;<span onClick={(e) => {
                        e.stopPropagation();

                        redirectToPage(`/projects/${JSON.parse(ele?.clickAction)?.projectSlug}`, router, progress)

                    }} className="tw-font-bold">{projectName} project</span> is mentioned in a Post:&nbsp;<span onClick={(e) => {
                        // e.stopPropagation();

                        // redirectToPage(`/posts/${JSON.parse(ele?.clickAction)?.postSlug}`, router, progress)

                    }} className="tw-font-bold">{post}</span>
                </span>
            </>
        };
    }
    else if (postMentionRegex.test(message)) {
        const [, post] = message.match(postMentionRegex);

        return {
            type: "postMention",
            userName: loginUser?.name,
            middleLabel: <>
                <span>
                    You were mentioned on this Post:&nbsp;<span className="tw-font-bold">{post}</span>
                </span>
            </>
        };
    }
    else {
        return {
            type: "unknown",
            message,
        };
    }
}


export const TEAM_ACCESS = {
    "read": "View Only",
    "write": "Can Edit"
}


export function getTimePassedFromNow(pastDateString) {
    const pastDate = new Date(pastDateString);
    const now = new Date();
    const msDiff = now - pastDate;

    const minutes = Math.floor(msDiff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (minutes < 1) {
        return "just now";
    } else if (minutes < 60) {
        return `${minutes} ${minutes === 1 ? 'min' : 'mins'} ago`;
    } else if (hours < 24) {
        return `${hours} ${hours === 1 ? 'hr' : 'hrs'} ago`;
    } else if (days < 7) {
        return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else if (days < 30) {
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
    } else if (months < 12) {
        return `${months} ${months === 1 ? 'month' : 'months'} ago`;
    } else {
        return `${years} ${years === 1 ? 'year' : 'years'} ago`;
    }
}


export const parseColor = (argb) => {
    const r = (argb >> 16) & 255;
    const g = (argb >> 8) & 255;
    const b = argb & 255;
    const a = ((argb >> 24) & 255) / 255;
    return `rgba(${r},${g},${b},${a})`;
};

export const fonts = [
    "Roboto",
    "Lato",
    "Open Sans",
    "Montserrat",
    "Poppins",
    "Raleway",
    "Ubuntu",
    "Playfair Display",
    "Quicksand",
    "Dancing Script",
    "Alegreya",
    "B612",
    "Varela",
    "Vollkorn",
    "Rakkas",
    "Neonderthaw",
    "Sacramento",
    "UnifrakturMaguntia",
];


export const isExistInData = (arr = [], key, value) => {
    return arr.filter(ele => ele[key] === value)?.length > 0
}

export const checkImageLoads = (url) => {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.crossOrigin = "anonymous";
        img.src = url;
        setTimeout(() => resolve(false), 5000); // Optional timeout
    });
};

export const createProxyImageUrl = (originalUrl) => {
    if (!originalUrl) return null;

    // For development, you can use a CORS proxy service
    // Replace this with your own proxy endpoint in production
    if (process.env.NODE_ENV === "development") {
        return `/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
    }

    return `https://stag-webapp.incenti.ai/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
};

export const generateAvatarCanvas = (name, size = 100, fontSize = 0.65) => {
    // Check if we're in a browser environment
    if (typeof window === "undefined" || typeof document === "undefined") {
        // Return a placeholder data URL for server-side rendering
        // This creates a simple SVG avatar as fallback
        const firstLetter = name?.charAt(0)?.toUpperCase() || "U";
        const svgAvatar = `
            <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
                <circle cx="${size / 2}" cy="${size / 2}" r="${size / 2}" fill="#6D11D2"/>
                <text x="${size / 2}" y="${size / 2}" text-anchor="middle" dy="0.35em"
                      fill="white" font-family="Arial, sans-serif"
                      font-size="${size * fontSize}" font-weight="bold">${firstLetter}</text>
            </svg>
        `;
        return `data:image/svg+xml;base64,${typeof window === "undefined"
            ? Buffer.from(svgAvatar).toString("base64")
            : window.btoa(svgAvatar)}`;
    }

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    canvas.width = size;
    canvas.height = size;

    // Generate a consistent color based on the name
    let hash = 0;
    for (let i = 0; i < name?.length; i++) {
        hash = name?.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Draw background circle
    ctx.fillStyle = "#6D11D2";
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 2, 0, 2 * Math.PI);
    ctx.fill();

    // Draw text
    ctx.fillStyle = "#FFFFFF";
    ctx.font = `bold ${size * fontSize}px Arial, sans-serif`;
    ctx.textAlign = "center";
    ctx.textBaseline = "alphabetic";

    const firstLetter = name?.charAt(0)?.toUpperCase();

    // Measure text to get precise centering
    const textMetrics = ctx.measureText(firstLetter);
    const textHeight =
        textMetrics.actualBoundingBoxAscent + textMetrics.actualBoundingBoxDescent;

    // Calculate precise center position
    const centerX = size / 2;
    const centerY =
        size / 2 + (textMetrics.actualBoundingBoxAscent - textHeight / 2);

    ctx.fillText(firstLetter, centerX, centerY);

    return canvas.toDataURL("image/png");
};

export function removeTargetAttributeFromLink(html) {
    return html?.replace(/<a([^>]*?)\s+target="_blank"([^>]*)>/g, '<a$1$2>');
}

export function addDataListToOrderedListItems(htmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, "text/html");

    const orderedLists = doc.querySelectorAll("ol");
    orderedLists.forEach((ol) => {
        const listItems = ol.querySelectorAll("li");

        let hasLiWithoutDataList = false;

        listItems.forEach((li) => {
            if (!li.hasAttribute("data-list")) {
                li.setAttribute("data-list", "custom-ordered");
                hasLiWithoutDataList = true;
            }
        });

        if (hasLiWithoutDataList) {
            ol.classList.add("custom-ol");
        }
    });

    return doc.body.innerHTML;
}

export function cleanHtmlString(input) {
    if (!input) return "";

    // Step 1: Remove all <br> tags (both self-closing and normal)
    let cleaned = input.replace(/<br\s*\/?>/gi, "");

    // Step 2: Remove all <img> tags
    cleaned = cleaned.replace(/<img[^>]*>/gi, "");

    // Step 3: Parse string into DOM to check inner text
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = cleaned;

    // Step 4: Check if elements have any text content, remove empty tags
    const children = Array.from(tempDiv.childNodes);
    children.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
            const text = node.textContent.trim();
            if (!text) {
                node.remove(); // remove empty tag
            }
        }
    });

    const finalOutput = tempDiv.innerHTML.trim();

    // Step 5: Return final string or empty if no content
    return finalOutput || "";
}

export const finalDescription = (description) => {
    return removeTargetAttributeFromLink(
        addDataListToOrderedListItems(
            cleanHtmlString(description ?? "")
        )
    )
}

export const privateProjectToggle = (projectName, isHide = false) => {
    const projectMessage = {
        Hide: {
            title: "Hide Private Project",
            message: `Currently others are able to see your Private Project ${projectName}. Do you want to hide that?`,
            successMessage: "Other users won't be able to view this Private Project Title and Cover Image"
        },
        UnHide: {
            title: "Undo Hide Project",
            message: `Currently others can’t see your Private Project ${projectName}. Do you want to undo that?`,
            successMessage: "Other Users are now able to view your Project Title and Cover Image"
        },
    };

    return projectMessage[isHide ? "Hide" : "UnHide"];
}



export function hasAProjectAccess(arrayA, arrayB) {
    // console.log(arrayA, arrayB, "arrayA, arrayB");
    const idSet = new Set(arrayA.map(item => item.id));
    return arrayB.every(item => idSet.has(item.id));
}

// export const ProjectHideSuccessMessage = (isHide = false) => {
//     const projectSuccessMessage = {
//         Hide: `Other users won't be able to view this Private Project Title and Cover Image`,
//         UnHide: `Other Users are now able to view your Project Title and Cover Image`,
//     };

//     return projectSuccessMessage[isHide ? "Hide" : "UnHide"];
// }


export function getDifference(original, updated) {
    return updated?.filter(e => !original?.includes(e));
}


export const routes = {
    HOME: "/",
    SINGLE_POST: "/posts/:slug",
    PROJECTS: "projects",
    CREATED_PROJECTS: "/projects/created",
    FOLLOWED_PROJECTS: "/projects/followed",
    EXPLORE_PROJECTS: "/projects/explore",
    SINGLE_PROJECTS: "/projects/:slug",
    SUB_PROJECT: "sub-project",
    SINGLE_SUB_PROJECT: "/sub-project/:slug",
    COMMUNITIES: "communities",
    CREATED_COMMUNITIES: "/communities/created",
    JOINED_COMMUNITIES: "/communities/joined",
    EXPLORE_COMMUNITIES: "/communities/explore",
    SINGLE_COMMUNITIES: "communities/:slug",
    TO_DOS: "/to-dos",
    SEARCH: "/search",
    PROFILE: "/profile",
    USER: "/user/:slug",
    GENIE: "/genie",

};


export const HandlerIframeURL = (html) => {
    // Replace youtu.be URLs with embed URLs
    return html.replace(
        /https:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,
        "https://www.youtube.com/embed/$1"
    );
};


export function formatNumber(amount) {
    if (amount === null || amount === undefined) return "0";
    const absAmount = Math.abs(amount);

    if (absAmount >= 1.0e9) {
        return (amount / 1.0e9).toFixed(2).replace(/\.00$/, "") + "B";
    } else if (absAmount >= 1.0e6) {
        return (amount / 1.0e6).toFixed(2).replace(/\.00$/, "") + "M";
    } else if (absAmount >= 1.0e3) {
        return (amount / 1.0e3).toFixed(2).replace(/\.00$/, "") + "K";
    } else {
        return amount.toString();
    }
}

export const handleCopy = async (url, setCopied = () => {

}) => {
    if (url) {
        await window.navigator.clipboard.writeText(url);
        setCopied(true);
        safeToast.success("Copied to clipboard!");
    }
};

export const isObjEmpty = (obj) => Object.keys(obj).length === 0;

export const ReplaceBackslashN = (str) => {
    // const newStr = str?.replace(/\n/g, "<br/>")
    return str?.replace(/\n/g, "<br/>")
    // return marked.parse(str)

}

export const role = {
    assistant: "assistant",
    user: "user",
};


const extractId = (slug) => {
    return slug.match(/\d+$/)?.[0]
}

export function extractMentions(htmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, "text/html");

    const anchors = doc.querySelectorAll("a[name]");
    const result = [];

    anchors.forEach(anchor => {
        const name = anchor.getAttribute("name");
        const href = anchor.getAttribute("href");
        const id = anchor.getAttribute("id");
        const userSlugId = anchor.getAttribute("data-user-slug");

        let obj = {
            name
        }

        let type = "unknown";
        if (href.includes("/user/")) {
            let slug = href.split("/user/")[1];
            obj = {
                ...obj,
                type: "user",
                projectSlug: slug,
                UserId: +id,
            }
        } else if (href.includes("/projects/")) {
            let slug = href.split("/projects/")[1];
            obj = {
                ...obj,
                type: "project",
                projectSlug: slug,
                ProjectId: +id,
                UserId: +userSlugId,
            }
        }

        result.push({ ...obj });
    });

    return result;
}
