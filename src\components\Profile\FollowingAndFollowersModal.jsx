import { useEffect, useRef, useState } from "react";
import Modal from "react-responsive-modal";
import { SearchIcon } from "lucide-react";
import { apiGenerator, blurDataURL, RESPONSE_STATUS } from "@/utils/function";
import InfiniteScroll from "react-infinite-scroll-component";
import FollowerList from "../Loader/FollowerListSkeleton";
import FollowerListSkeleton from "../Loader/FollowerListSkeleton";
import Empty from "../Common/Empty";
import {
  communityMemberList,
  followersList,
  followingList,
  followUser,
  likePostUserList,
  removeFollowUser,
  unFollowUser,
} from "@/app/action";
import toast from "react-hot-toast";
import ListComponent from "./ListComponent";
import useApiRequest from "../helper/hook/useApiRequest";
import "react-responsive-modal/styles.css";
import { safeToast } from "@/utils/safeToast";
import { CloseModalIcon, NoMemberFound } from "@/utils/icons";
import { useDebouncedSearch } from "../helper/hook/useDebouncedSearch";
import SearchBar from "../Common/SearchBar";
import UserAvatar from "../Common/UserAvatar";
import PopUpModal from "../Common/PopUpModal";
import CustomButton from "../Common/Custom-Button";

const FollowingAndFollowersModal = ({
  modalType,
  setModalType = () => {},
  setRefresh = () => {},
  communityId = null,
  loginUserData = null,
  UserId = null,
  postId = null,
  apiKey = "community",
  isSearchRequired = true,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [isDataFetchedFirstTime, setIsDataFetchedFirstTime] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [search, setSearch] = useState("");
  const [isConfirm, setIsConfirm] = useState({
    isOpen: false,
    data: null,
  });
  const [isLoading, setIsLoading] = useState(false);
  // const debounceRef = useRef(null);
  const api = useApiRequest();
  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // console.log(modalType?.type);
  // Render Modal Details
  const modalDetails = {
    api: {
      followerUser: followersList,
      followingUser: followingList,
      User: {
        community: communityMemberList,
        likeUser: likePostUserList,
      },
      // likeUser: ,
    },
    title: {
      followerUser: "Followers",
      followingUser: "Following",
      User: {
        community: "Members",
        likeUser: "Likes",
      },
    },
    buttonText: {
      followerUser: {
        0: "Following",
        1: "Follow",
      },
      // followingUser: "Unfollow",
      followingUser: {
        0: "Unfollow",
        1: "Follow",
      },
      User: {
        0: "Following",
        1: "Follow",
      },
    },
  };

  const errorMessage = {
    followingUser: {
      label: "No Following",
      subLabel: "No following yet!",
    },
    followerUser: {
      label: "No Followers",
      subLabel: "No followers yet!",
    },
    community: {
      label: "No Members",
      subLabel: "",
    },
    likeUser: {
      label: "No Likes yet",
    },
  };

  const followAndUnfollowHandler = async (userId, isFollow = 1) => {
    // console.log(userId, isFollow);
    setIsLoading(true);
    let apiCall;
    try {
      if (Boolean(isFollow)) {
        apiCall = followUser;
      } else if (
        !UserId &&
        !communityId &&
        modalType?.type === "followerUser"
      ) {
        apiCall = removeFollowUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      // console.log(response);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
        setIsConfirm({
          isOpen: false,
          data: null,
        });
        if (
          !UserId &&
          !communityId &&
          modalType?.type === "followerUser" &&
          !Boolean(isFollow)
        ) {
          setDataList((prev) =>
            prev?.filter((ele) => ele?.["followerUser"]?.id !== userId)
          );
        }
        setIsLoading(false);
      } else {
        throw response;
      }
    } catch (error) {
      // console.log(error);
      safeToast.error(error?.message);
    }
  };

  const confirmModalDetails = {
    followingUser: {
      title: "",
      message: `If you change your mind, you'll have to request to follow ${`${
        isConfirm?.data?.firstName ?? ""
      } ${isConfirm?.data?.lastName ?? ""}`} again!`,
      buttonText: "Unfollow",
    },
    followerUser: {
      title: "Remove follower?",
      message: `We won't tell ${`${isConfirm?.data?.firstName ?? ""} ${
        isConfirm?.data?.lastName ?? ""
      }`} that they were removed from your followers`,
      buttonText: "Remove",
    },
    User: {
      message: `If you change your mind, you'll have to request to follow ${`${
        isConfirm?.data?.firstName ?? ""
      } ${isConfirm?.data?.lastName ?? ""}`} again!`,
      buttonText: "Unfollow",
    },
  };

  // Search Handler
  // const onSearch = (value) => {
  //   setSearch(value);
  //   if (debounceRef.current) {
  //     clearTimeout(debounceRef.current);
  //   }
  //   debounceRef.current = setTimeout(() => {
  //     resetData();
  //     setSearchQuery(value);
  //   }, 500);
  // };
  const onSearch = useDebouncedSearch({
    setSearchQuery, // required
    resetData, // optional
    setSearch, // optional
  });
  useEffect(() => {
    if (modalType?.isOpen) {
      let queryParams = {
        page: pagination.page,
        limit: pagination.limit,
      };
      if (searchQuery) {
        queryParams = {
          ...queryParams,
          searchQuery,
        };
      }
      if (UserId) {
        queryParams = {
          ...queryParams,
          UserId,
        };
      }
      if (communityId) {
        queryParams = {
          ...queryParams,
          id: communityId,
        };
      }
      if (postId) {
        queryParams = {
          ...queryParams,
          id: postId,
        };
      }
      api.sendRequest(
        modalType?.type === "User"
          ? modalDetails.api[modalType?.type][apiKey]
          : modalDetails.api[modalType?.type],
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (
            res?.data?.data?.length > 0 ||
            res?.data?.members?.data?.length > 0
          ) {
            setIsDataFetchedFirstTime(true);
          }
          if (modalType?.type === "User" && apiKey === "community") {
            // Community Member list
            setDataList((prev) => [
              ...prev,
              {
                ...res?.data?.admin,
                User: {
                  ...res?.data?.admin?.User,
                  isAdmin: true,
                  isLoginUser: res?.data?.admin?.User?.id === loginUserData?.id,
                },
              },
              ...res?.data?.members?.data,
            ]);
          } else {
            setDataList((prev) => [...prev, ...res?.data?.data]);
          }
        },
        queryParams
      );
    }
  }, [modalType, searchQuery]);

  useEffect(() => {
    setSearch("");
    setSearchQuery("");
  }, [modalType]);
  return (
    <>
      {/* Confirm Modal - Moved outside parent modal */}
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%]  sm:!tw-max-w-[90vw] md:!tw-max-w-[80vw] lg:!tw-w-[30rem] !tw-rounded-[1.25rem] !tw-mt-1 !tw-outline-none",
          overlay: "!tw-bg-[#000000CC] ",
        }}
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
            // zIndex: 9999,
          },
        }}
        center
        focusTrapped={false}
        open={isConfirm?.isOpen}
        closeIcon={<></>}
        onClose={() => {}}
      >
        <div className="tw-flex tw-gap-3 tw-flex-col tw-items-center tw-text-primary-black tw-max-w-[22rem] tw-mx-auto">
          <UserAvatar
            size={150}
            imageUrl={isConfirm?.data?.image}
            userName={isConfirm?.data?.firstName}
            imageParentClassName="lg:!tw-w-[5.5rem] lg:!tw-h-[5.5rem]"
          />
          {confirmModalDetails[UserId ? "followingUser" : modalType?.type]
            ?.title && (
            <p className="tw-text-xl tw-font-medium">
              {
                confirmModalDetails[UserId ? "followingUser" : modalType?.type]
                  ?.title
              }
            </p>
          )}
          <p className="tw-text-center ">
            {
              confirmModalDetails[UserId ? "followingUser" : modalType?.type]
                ?.message
            }
          </p>

          <div className=" tw-w-full tw-flex  tw-gap-5 tw-justify-center tw-items-center">
            <button
              disabled={isLoading}
              onClick={() => {
                setIsConfirm((prev) => ({
                  ...prev,
                  isOpen: false,
                }));
              }}
              className={`tw-outline-none tw-text-incenti-purple tw-px-6 tw-py-[10px] tw-border-incenti-purple tw-border tw-rounded-full tw-transition hover:tw-transform hover:tw-scale-105 ${
                isLoading && "tw-cursor-not-allowed"
              }`}
            >
              Cancel
            </button>
            <CustomButton
              loading={isLoading}
              disabled={isLoading}
              count={8}
              onClick={() => {
                isConfirm.callBack();
                followAndUnfollowHandler(isConfirm?.data?.id, false);
                setRefresh((prev) =>
                  typeof prev === "undefined" ? true : !prev
                );
              }}
              type="button"
              className={`tw-outline-none tw-rounded-full !tw-text-base tw-border-none hover:!tw-bg-[#EF3B41] active:!tw-bg-[#EF3B41] !tw-bg-[#EF3B41] tw-px-6 !tw-py-3 tw-text-white tw-transition hover:tw-transform hover:tw-scale-105 ${
                isLoading && "tw-cursor-not-allowed"
              }`}
            >
              {
                confirmModalDetails[UserId ? "followingUser" : modalType?.type]
                  ?.buttonText
              }
            </CustomButton>
          </div>
        </div>
      </Modal>

      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-m-0 md:!tw-m-[1.2rem] sm:!tw-w-[95vw] md:!tw-w-[80vw] lg:!tw-w-[30rem] !tw-rounded-[1.25rem] !tw-top-[8%]",
          overlay: "!tw-bg-[#000000CC]",
        }}
        // center
        focusTrapped={false}
        open={modalType?.isOpen}
        onClose={() => {
          setModalType((prev) => ({
            ...prev,
            isOpen: false,
          }));
          setSearchQuery("");

          setTimeout(() => {
            resetData();
            setIsDataFetchedFirstTime(false);
          }, 300);
          setIsConfirm({
            isOpen: false,
            data: null,
          });
        }}
      >
        <div className="tw-py-3 tw-px-5 tw-relative">
          <p className="tw-text-primary-black tw-font-semibold tw-text-2xl tw-text-center">
            {`${
              communityId || apiKey === "likeUser" ? "" : pagination.total || ""
            } ${
              modalType?.type === "User"
                ? modalDetails.title[modalType?.type][apiKey]
                : modalDetails.title[modalType?.type]
            }`}
          </p>

          {isDataFetchedFirstTime && isSearchRequired && (
            <SearchBar
              search={search}
              setSearch={setSearch}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              onSearch={onSearch}
              resetData={resetData}
              parentClassName="!tw-mb-4"
            />
          )}

          {api.isLoading && pagination.page === 1 && (
            <div className="tw-mt-5">
              <FollowerListSkeleton />
            </div>
          )}
          {dataList?.length > 0 && (
            <InfiniteScroll
              height={300}
              dataLength={dataList?.length}
              className={`${!isSearchRequired && "tw-mt-5"}`}
              hasMore={
                Math.ceil(pagination.total / pagination.limit) > pagination.page
              }
              next={() =>
                setPagination((prev) => ({
                  ...prev,
                  page: prev.page + 1,
                }))
              }
              loader={<FollowerListSkeleton />}
            >
              {dataList?.map((ele) => (
                <ListComponent
                  key={ele?.id}
                  ele={ele}
                  type={modalType?.type}
                  // type={communityId ? "User" : type}
                  isFollowButtonRequired={apiKey !== "likeUser"}
                  loginUserData={loginUserData}
                  communityId={communityId}
                  followAndUnfollowHandler={followAndUnfollowHandler}
                  modalDetails={modalDetails}
                  setDataList={setDataList}
                  setPagination={setPagination}
                  setRefresh={setRefresh}
                  updateListHandler={(userData, callBack) => {
                    setIsConfirm({
                      isOpen: true,
                      data: userData,
                      callBack,
                    });
                  }}
                />
              ))}
            </InfiniteScroll>
          )}
          {!api.isLoading && dataList.length === 0 && (
            <div className="tw-h-[30dvh] tw-flex tw-justify-center tw-items-center">
              <Empty
                icon={
                  searchQuery &&
                  modalType?.type === "User" &&
                  apiKey === "community" && <NoMemberFound size={50} />
                }
                iconRequired={
                  searchQuery &&
                  modalType?.type === "User" &&
                  apiKey === "community"
                }
                label={`${
                  searchQuery
                    ? modalType?.type === "User"
                      ? "Search Result Not Found!"
                      : "No User Found"
                    : errorMessage[
                        modalType?.type === "User" ? apiKey : modalType?.type
                      ]?.label ?? ""
                }`}
                className={`${
                  searchQuery &&
                  modalType?.type === "User" &&
                  apiKey === "community"
                    ? "-tw-mt-2"
                    : ""
                } tw-font-semibold tw-text-xl`}
                subLabel={`${
                  searchQuery
                    ? modalType?.type === "User"
                      ? ""
                      : "Search Result Not Found!"
                    : errorMessage[
                        modalType?.type === "User" ? apiKey : modalType?.type
                      ]?.subLabel ?? ""
                } `}
                subClassName="-tw-mt-1"
              />
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default FollowingAndFollowersModal;
