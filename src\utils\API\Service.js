"use server"
import axios from "axios";
import { API_BASE_URL } from "../function";
import { cookies } from "next/headers";
const Services = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    Accept: "application/json",
    // Authorization: `Bearer ${authStorage?.getAuthToken() ? authStorage?.getAuthToken() : ""}`,
    // 'X-RapidAPI-Key': `${NEXT_API_KEY}`,
  },
});

Services.interceptors.request.use(
  async (config) => {
    const cookieStore = await cookies()
    const token = cookieStore.get("SAID")?.value;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export default Services;