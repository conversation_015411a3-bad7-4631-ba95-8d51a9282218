import useApiRequest from "@/components/helper/hook/useApiRequest";
import FloatingLabelInput from "@/components/HomePage/Form/FloatingLabelInput";
import { useFormik } from "formik";
import * as Yup from "yup";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import { addFolder, updateFolder } from "@/app/action";
import CustomButton from "@/components/Common/Custom-Button";
import toast from "react-hot-toast";
import { useEffect } from "react";
import { safeToast } from "@/utils/safeToast";

const FolderModal = ({
  isOpen,
  setIsOpen = () => {},
  editData,
  setEditData = () => {},
  setRefresh = () => {},
  reFetchData = () => {},
  defaultData = null,
  parentProjectData = null,
}) => {
  const api = useApiRequest(false);
  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().required("Folder Name is required"),
  });
  // Formik
  const formik = useFormik({
    enableReinitialize: true, // Allows form values to update when `open` changes
    initialValues: {
      name: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      // console.log("Updated Profile:", values);
      let payload = {
        name: values?.name?.trim(),
      };

      if (!editData) {
        if (parentProjectData) {
          payload = {
            ...payload,
            ParentId: parentProjectData?.id,
          };
        }
        if (defaultData) {
          payload = {
            ...payload,
            ProjectId: defaultData?.id,
          };
        }
      }
      if (editData) {
        payload = {
          ...payload,
          id: editData?.id,
        };
      }

      api.sendRequest(
        editData ? updateFolder : addFolder,
        (res) => {
          //   console.log(res);
          reFetchData();
          setRefresh((prev) => !prev);
          resetModal();
          safeToast.success(res?.message);
        },
        payload,
        "",
        (err) => {
          safeToast.error(err?.message);
        }
      );
    },
  });

  const resetModal = () => {
    setIsOpen(false);
    setEditData(null);
    formik.resetForm();
  };

  useEffect(() => {
    if (editData) {
      formik.setFieldValue("name", editData?.name);
    }
  }, [editData]);
  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[40rem] !tw-rounded-[1.25rem] !tw-m-0 md:tw-m-3",
          closeButton: `${api.isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        styles={{
          modal: {
            position: "relative",
            overflow: "visible",
          },
        }}
        center
        focusTrapped={false}
        open={editData ? editData !== null : isOpen}
        onClose={() => {
          if (!api.isLoading) {
            resetModal();
          }
        }}
      >
        <div className="tw-py-5 tw-mx-6 lg:tw-mx-14">
          <h2 className="tw-text-2xl tw-text-center tw-font-semibold">
            New Folder
          </h2>

          <form onSubmit={formik.handleSubmit} className="tw-mt-4">
            <FloatingLabelInput
              label="Folder Name*"
              name="name"
              formik={formik}
            />
            {formik.touched.name && formik.errors.name && (
              <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                {formik.errors.name}
              </p>
            )}
            <div className="tw-flex tw-justify-center tw-my-2 ">
              <CustomButton
                loading={api.isLoading}
                className={"!tw-px-9 !tw-py-[14px] "}
                type="submit"
                count={8}
              >
                <span className={"!tw-text-base"}>
                  {editData ? "Update" : "Create"}
                </span>
              </CustomButton>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
};

export default FolderModal;
