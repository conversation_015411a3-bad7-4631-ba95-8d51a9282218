import { useRef, useEffect } from "react";

export const useDebouncedSearch = ({
    setSearchQuery,
    resetData = () => { },
    setSearch,
    isNeedToStore = false,
    searchStoreKey = "search",
    delay = 500,
}) => {
    const debounceRef = useRef(null);

    useEffect(() => {
        return () => {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, []);

    const onSearch = (value) => {
        if (typeof setSearch === "function") {
            setSearch(value);
        }

        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }

        debounceRef.current = setTimeout(() => {
            const trimmedValue = value?.trim() || "";
            if (isNeedToStore) {
                sessionStorage.setItem(searchStoreKey, value?.trim());
            }
            setSearchQuery((prev) => {
                if (prev === trimmedValue) return prev;

                if (typeof resetData === "function") {
                    resetData();
                }

                return trimmedValue;
            });
        }, delay);
    };

    return onSearch;
};
