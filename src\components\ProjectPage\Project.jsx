"use client";
import { useCallback, useEffect, useState } from "react";
import CustomButton from "../Common/Custom-Button";
import { CustomContainer } from "../Common/Custom-Display";
import CustomTitle from "../Common/CustomTitle";
import {
  AddProjectIcon,
  EditProfileIcon,
  PrivateProjectIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import Image from "next/image";
import {
  apiGenerator,
  blurDataURL,
  otherProjectSetting,
  privateProjectToggle,
} from "@/utils/function";
import useApiRequest from "../helper/hook/useApiRequest";
import authStorage from "@/utils/API/AuthStorage";
import {
  deleteProject,
  getProject,
  reportProject,
  updateProject,
} from "@/app/action";
import InfiniteScroll from "../Common/InfiniteScroll";
import ProjectSkeleton from "../Loader/ProjectSkeleton";
import Empty from "../Common/Empty";
import { useRouter } from "nextjs-toploader/app";
import ProjectModal from "../HomePage/Form/ProjectModal";
import PopUpModal from "../Common/PopUpModal";
import TabSkeleton from "../Loader/TabSkeleton";
import MainProjectCard from "./MainProjectCard";
import ShareModal from "../Common/ShareModal";
import ReportModal from "../Common/ReportModal";
import toast from "react-hot-toast";
import { useProgress } from "@bprogress/next";
import { safeToast } from "@/utils/safeToast";

const Project = ({ category, origin }) => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  const [refresh, setRefresh] = useState(false);
  const [isDelete, setIsDelete] = useState(null);
  const [editData, setEditData] = useState(null);
  const [hideProject, setHideProject] = useState(null);
  const [userData, setUserData] = useState(null);
  const router = useRouter();
  const api = useApiRequest();
  const projectAPI = useApiRequest(false);
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareData, setShareData] = useState(null);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);
  const resetState = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };
  const progress = useProgress();

  const emptyDataMessages = {
    created: "Let's get started, create your project today!",
    followed: "No Project followed yet!",
    explore: "No Projects found",
  };

  const projectsCategory = [
    {
      label: "Created",
      value: "created",
    },
    {
      label: "Followed",
      value: "followed",
    },

    {
      label: "Explore",
      value: "explore",
    },
  ];

  useEffect(() => {
    setSelectedCategory(category);
  }, [category]);

  // Fetch Data
  const fetchProjects = useCallback(async () => {
    setIsLoading(true); // ✅ Set loading before API call

    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
    };

    const userDetails = authStorage.getProfileDetails();
    if (selectedCategory === "followed") {
      queryParams = { ...queryParams, followed: 1 };
    } else if (selectedCategory === "created") {
      if (Object.keys(userDetails)?.length > 0) {
        queryParams = { ...queryParams, UserId: userDetails?.id };
      }
    }

    try {
      await api.sendRequest(
        getProject,
        (res) => {
          if (pagination.page === 1) {
            setDataList(res?.data?.data);
          } else {
            setDataList((prev) => [...prev, ...res?.data?.data]);
          }
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
        },

        queryParams
      );
    } catch (error) {
      console.error("Error fetching projects:", error);
    } finally {
      setIsLoading(false); // ✅ Set loading false after API response (success or error)
    }
  }, [pagination.page, selectedCategory, refresh]);

  useEffect(() => {
    if (selectedCategory) {
      setUserData(authStorage.getProfileDetails());
      fetchProjects();
    }
  }, [pagination.page, selectedCategory, refresh]);
  return (
    <>
      <section>
        {/* Report Modal */}
        <ReportModal
          title="Why are you reporting this project?"
          isOpen={isReported}
          setIsOpen={setIsReported}
          isLoading={projectAPI?.isLoading}
          onConfirm={(reason) => {
            const payload = {
              reason,
              id: reportData?.id,
            };
            projectAPI.sendRequest(
              reportProject,
              (res) => {
                safeToast.success(res?.message);
                setIsReported(false);
                resetState();
                setRefresh((prev) => !prev);
                setReportData(null);
              },
              payload
            );
          }}
        />
        {/* Project hide/unhide Confirm Popup */}
        <PopUpModal
          isLoading={projectAPI.isLoading}
          isOpen={hideProject}
          setIsOpen={setHideProject}
          icon={null}
          mainMessage={
            privateProjectToggle(hideProject?.name, !hideProject?.hide)?.title
          }
          subMessage={
            privateProjectToggle(hideProject?.name, !hideProject?.hide)?.message
          }
          // subMessage={`Are you sure you want to ${
          //   !hideProject?.hide ? "hide" : "Unhide"
          // } your Project?`}
          onConfirm={() => {
            const payload = {
              id: hideProject?.id,
              hide: !hideProject?.hide,
            };
            projectAPI.sendRequest(
              updateProject,
              (res) => {
                // console.log(res);
                safeToast.success(
                  privateProjectToggle(hideProject?.name, !hideProject?.hide)
                    ?.successMessage
                );
                resetState();
                setRefresh((prev) => !prev);
                setHideProject(null);
              },
              payload
            );
          }}
        />
        {/* Delete Confirm Popup */}
        <PopUpModal
          isLoading={projectAPI.isLoading}
          isOpen={isDelete}
          setIsOpen={setIsDelete}
          mainMessage="Delete Project"
          subMessage="Are you sure you want to Delete Project Permanently?"
          onConfirm={() => {
            projectAPI.sendRequest(
              deleteProject,
              () => {
                setRefresh((prev) => !prev);
                resetState();
                setIsDelete(null);
              },
              {
                id: isDelete?.id,
              },
              "Delete Project Successfully"
            );
          }}
        />
        {/* Project Modal */}
        <ProjectModal
          reFetchData={resetState}
          setRefresh={setRefresh}
          open={openModal}
          setOpen={setOpenModal}
          modalTitle={"Create Project"}
          modalSubmitButton={"Create"}
        />
        {/* Edit Modal */}
        <ProjectModal
          reFetchData={resetState}
          setRefresh={setRefresh}
          editData={editData}
          setEditData={setEditData}
          modalTitle={"Edit Project"}
          modalSubmitButton={"Update"}
        />

        <ShareModal
          open={isShareOpen}
          onClose={() => {
            setIsShareOpen(false);
          }}
          shareUrl={`${origin}/projects/${shareData?.slug}`}
          title={`${shareData?.name} \nCheckout this project:`}
        />
        <CustomContainer className="tw-py-4">
          {/* Title, Tabs, and Add Project Button */}
          <div className="tw-w-full tw-flex tw-justify-center lg:tw-justify-between tw-items-center">
            <CustomTitle className="tw-hidden lg:tw-block" name="Projects" />
            {selectedCategory ? (
              <div className="tw-flex tw-justify-center tw-items-center tw-gap-2">
                {projectsCategory?.map((ele) => (
                  <div
                    onClick={() => {
                      setSelectedCategory(ele?.value);
                      progress.start(0, 1);
                      router.push(`/projects/${ele?.value}`);
                      resetState();
                    }}
                    key={ele?.label}
                    className={`tw-py-3 tw-cursor-pointer tw-rounded-full ${
                      ele?.value === selectedCategory
                        ? "tw-font-bold tw-bg-[#F6EFFE] tw-text-incenti-purple"
                        : "tw-bg-[#F5F7F8] tw-text-[#2D394A] tw-font-medium"
                    } tw-px-5`}
                  >
                    {ele?.label}
                  </div>
                ))}
              </div>
            ) : (
              <TabSkeleton />
            )}
            {/* Floating Create Button for mobile and fixed position, hidden on desktop */}
            <div className="tw-fixed lg:tw-static tw-bottom-[8.5rem] tw-right-4 tw-z-50 lg:tw-z-auto">
              <CustomButton
                type="button"
                onClick={() => {
                  setOpenModal({
                    isOpen: true,
                    type: "project",
                  });
                }}
                className={`tw-px-[1.1rem] lg:tw-px-6 tw-hidden lg:tw-block`}
                count={8}
              >
                <div className="tw-flex tw-items-center tw-gap-1.5 tw-font-semibold">
                  <AddProjectIcon className="" />
                  <p className="tw-hidden lg:tw-block">Create</p>
                </div>
              </CustomButton>
              {/* Mobile floating icon button */}
              <button
                type="button"
                onClick={() => {
                  setOpenModal({
                    isOpen: true,
                    type: "project",
                  });
                }}
                className="tw-bg-incenti-purple tw-text-white tw-rounded-full tw-p-5 tw-shadow-lg tw-block lg:tw-hidden"
                aria-label="Create Project"
                style={{ zIndex: 1050 }}
              >
                <AddProjectIcon className="tw-w-6 tw-h-6" />
              </button>
            </div>
          </div>
          <div className="tw-my-5 !tw-pb-6 tw-grid tw-grid-cols-2 md:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7  tw-w-full ">
            {/* {isLoading && pagination.page === 1 && <ProjectSkeleton />} */}
            {dataList?.map((ele) => (
              <div
                key={ele?.id}
                className="tw-cursor-pointer"
                onClick={() => {
                  if (
                    ele?.isPrivate &&
                    userData?.id !== ele?.UserId &&
                    selectedCategory === "explore"
                  ) {
                    safeToast.success("This Project is Private");
                    return;
                  }
                  progress.start(0, 1);
                  router.push(`/projects/${ele?.slug}`);
                  // router.push(`/projects/${selectedCategory}/${ele?.id}`);
                }}
              >
                <MainProjectCard
                  ele={ele}
                  selectedCategory={selectedCategory}
                  setIsDelete={setIsDelete}
                  setEditData={setEditData}
                  reFetchData={resetState}
                  setRefresh={setRefresh}
                  setHideProject={setHideProject}
                  loginUserData={userData}
                  shareHandler={(ele) => {
                    setIsShareOpen(true);
                    setShareData(ele);
                  }}
                  isMenuVisible={
                    selectedCategory === "explore" ? !ele?.isPrivate : true
                  }
                  reportHandler={(ele) => {
                    setIsReported(true);
                    setReportData(ele);
                  }}
                />
              </div>
            ))}
            <InfiniteScroll
              threshold={90}
              loadMoreFunction={() => {
                if (
                  Math.ceil(pagination.total / pagination.limit) >
                  pagination.page
                ) {
                  setPagination((prev) => ({
                    ...prev,
                    page: prev?.page + 1,
                  }));
                }
              }}
              isLoading={isLoading}
              loadingComponent={<ProjectSkeleton />}
              timeout={10}
              // loadOff={loadOff}
            />
          </div>
          {dataList?.length === 0 && !isLoading && (
            <div className="tw-flex tw-justify-center tw-items-center tw-h-[50dvh]">
              <Empty label={emptyDataMessages[selectedCategory]} />
            </div>
          )}
        </CustomContainer>
      </section>
    </>
  );
};

export default Project;
