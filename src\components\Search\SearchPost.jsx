"use client";
import { useContext } from "react";
import { valContext } from "../context/ValContext";
import ExplorePostList from "../HomePage/ExplorePostList";

const SearchPost = ({ origin }) => {
  const { globalSearch } = useContext(valContext);
  return (
    <div>
      <ExplorePostList
        origin={origin}
        showSearchEmptyData
        globalSearch={globalSearch}
      />
    </div>
  );
};

export default SearchPost;
