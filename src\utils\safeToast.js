// utils/toastControl.js
import { toast } from "react-hot-toast";

let allowToasts = true;

export const setAllowToasts = (value) => {
    allowToasts = value;
    if (!value) {
        toast.remove()
    }; // Remove existing and queued toasts
};


export const safeToast = {
    success: (message, options) => {
        if (allowToasts) toast.success(message, options);
    },
    error: (message, options) => {
        if (allowToasts) toast.error(message, options);
    },
    // Add more wrappers if needed
};
