import ShowMenuList from "@/components/Common/ShowMenuList";
import UserAvatar from "@/components/Common/UserAvatar";
import { blurDataURL, TEAM_ACCESS } from "@/utils/function";
import { ThreeDotMenuIcon } from "@/utils/icons";
import { Eye, LogOut, PenLine, Trash2 } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

const TeamCard = ({
  ele,
  updateAccessHandler = () => {},
  removeHandler = () => {},
  projectData,
  loginUserData,
  followAndUnfollowHandler = () => {},
}) => {
  const [access, setAccess] = useState(ele?.access);
  const [isFollow, setIsFollow] = useState(Boolean(+ele?.User?.isFollowed));

  // console.log(ele?.User);
  const teamSetting1 = [
    {
      label: "View Only",
      element: (
        <div className="tw-flex tw-justify-between tw-items-center">
          <div
            className={` tw-flex tw-gap-3 tw-items-center ${
              access === "read"
                ? "tw-text-primary-purple"
                : "tw-text-primary-black"
            } `}
          >
            <div>
              <Eye size={17} />
            </div>
            <p className={`tw-text-sm`}>View Only</p>
          </div>
        </div>
      ),
      className: "",
      onClick: (ele) => {
        setAccess("read");
        updateAccessHandler({
          id: ele?.id,
          access: "read",
        });
      },
    },
    {
      label: "Can Edit",
      element: (
        <div className="tw-flex tw-justify-between tw-items-center">
          <div
            className={` tw-flex tw-gap-3 tw-items-center ${
              access === "write"
                ? "tw-text-primary-purple"
                : "tw-text-primary-black"
            } `}
          >
            <div>
              <PenLine size={17} />
            </div>
            <p className={`tw-text-sm`}>Can Edit</p>
          </div>
        </div>
      ),
      onClick: () => {
        setAccess("write");
        updateAccessHandler({
          id: ele?.id,
          access: "write",
        });
      },
    },
    {
      label: "Remove",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        removeHandler(ele);
      },
    },
  ];
  const teamSetting2 = [
    {
      label: "Leave",
      className: "tw-text-[#EF3B41]",
      icon: <LogOut stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        removeHandler(ele);
      },
    },
  ];
  return (
    <div className="tw-relative">
      <div className="tw-py-10 tw-h-[17rem] tw-bg-[#F5F7F8] tw-rounded-3xl tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-4">
        {(projectData?.UserId === loginUserData?.id ||
          ele?.User?.id === loginUserData?.id) && (
          <ShowMenuList
            data={ele}
            menuList={
              projectData?.UserId === loginUserData?.id
                ? ele?.status !== null && !ele?.status
                  ? teamSetting1?.filter((ele) => ele.label === "Remove")
                  : teamSetting1
                : teamSetting2
            }
          >
            <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-7">
              <ThreeDotMenuIcon fill="#111" size={24} />
            </div>
          </ShowMenuList>
        )}
        <div>
          {/* <div
            className={`tw-relative tw-h-24 tw-w-24 tw-rounded-full ${
              !ele?.User?.image &&
              "tw-bg-primary-purple tw-text-white tw-flex tw-justify-center tw-items-center"
            }`}
          >
            {ele?.User?.image ? (
              <Image
                src={ele?.User?.image}
                alt="User"
                fill
                className="!tw-rounded-full !tw-object-cover"
                placeholder="blur"
                blurDataURL={blurDataURL(200, 200)}
              />
            ) : (
              <div className="tw-text-primary-1100 tw-font-merriweather tw-font-medium tw-text-3xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                {ele?.User?.firstName?.charAt(0)}
              </div>
            )}
          </div> */}
          <UserAvatar
            imageParentClassName="!tw-w-24 !tw-h-24"
            imageUrl={ele?.User?.image}
            userName={ele?.User?.firstName}
            userNameClassName="!tw-text-6xl"
          />
        </div>
        <div className="tw-text-center">
          <p className="tw-text-primary-black tw-font-bold tw-text-2xl">
            {ele?.User?.firstName ?? ""}&nbsp;{ele?.User?.lastName ?? ""}
          </p>
          {projectData?.UserId === loginUserData?.id ||
          projectData?.ProjectMembers?.length > 0 ? (
            ele?.User?.id === loginUserData?.id ? (
              <p className="tw-text-sm tw-text-primary-black">You</p>
            ) : (
              <>
                <p className="tw-text-sm tw-text-primary-black">
                  {access ? TEAM_ACCESS[access] : ""}{" "}
                  {!ele?.status && (
                    <span
                      className={`tw-text-sm ${
                        ele?.status === null
                          ? "tw-text-[#E8AB2E]"
                          : "tw-text-red-500"
                      }`}
                    >
                      (&nbsp;{ele?.status === null ? "Pending..." : "Reject"}
                      &nbsp;)
                    </span>
                  )}
                </p>
              </>
            )
          ) : (
            <>
              {/* <p className="tw-text-sm tw-text-primary-black">
                {ele?.User?.email ?? ""}
              </p> */}
              {ele?.User?.id !== loginUserData?.id ? (
                <div>
                  <button
                    type="button"
                    onClick={() => {
                      if (isFollow) {
                        //
                        followAndUnfollowHandler(
                          ele?.User?.id,
                          false,
                          setIsFollow
                        );
                      } else {
                        followAndUnfollowHandler(
                          ele?.User?.id,
                          true,
                          setIsFollow
                        );
                      }
                      setIsFollow((prev) => !prev);
                    }}
                    className={`tw-py-2 tw-px-4 tw-mt-4 tw-rounded-full ${
                      // !isFollow && (type === "User" || type === "followerUser")
                      !isFollow
                        ? "tw-border tw-border-primary-purple tw-text-primary-purple tw-bg-transparent"
                        : "tw-border tw-bg-transparent tw-border-[#D9D9D9] tw-text-[#787E89]"
                    } tw-font-semibold`}
                  >
                    {isFollow ? "Following" : "Follow"}
                  </button>
                </div>
              ) : (
                <p className="tw-text-sm tw-text-primary-black tw-mt-2">You</p>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default TeamCard;
