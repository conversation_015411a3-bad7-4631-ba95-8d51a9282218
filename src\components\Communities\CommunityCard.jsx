import { blurDataURL } from "@/utils/function";
import Image from "next/image";
import CustomButton from "../Common/Custom-Button";
import { useState } from "react";
import tempImg from "../../../public/images/assets/group-community.jpg";
import UserAvatar from "../Common/UserAvatar";

const CommunityCard = ({ ele, isCreatedByMe, joinHandler }) => {
  const [isJoined, setIsJoined] = useState(Boolean(+ele?.isJoined) ?? false);
  return (
    <>
      <div className="tw-relative tw-h-[8rem] tw-w-[8rem] exs:tw-h-[7.5rem] exs:tw-w-[7.5rem]">
        <Image
          src={ele?.image ?? tempImg}
          alt={ele?.name ?? "community"}
          fill
          className="!tw-rounded-full !tw-object-cover"
          placeholder="blur"
          blurDataURL={blurDataURL(300, 300)}
        />
      </div>
      <div className="tw-col-span-2 tw-flex tw-flex-col tw-gap-1 lg:tw-gap-4 lg:tw-items-center ">
        <div>
          <p
            title={ele?.name}
            className="tw-text-primary-black tw-line-clamp-2 lg:tw-mx-3 tw-font-bold tw-text-xl md:tw-text-2xl lg:tw-text-center"
          >
            {ele?.name}
          </p>
        </div>
        <div className="tw-flex lg:tw-justify-between tw-items-center tw-gap-3">
          {ele?.recentMembers?.length > 0 && (
            <div className="tw-flex tw-items-center tw-justify-center tw-space-x-[-10px] ">
              {ele?.recentMembers?.map((data, index) => (
                <div key={`${data?.User?.id}-${index}`}>
                  <UserAvatar
                    imageParentClassName="!tw-w-6 !tw-h-6 !tw-border-[1.5px] !tw-border-white"
                    userNameClassName="!tw-text-sm !tw-font-normal"
                    imageUrl={data?.User?.image}
                    userName={data?.User?.firstName}
                  />
                </div>
              ))}
            </div>
          )}
          <p className="tw-text-primary-black tw-text-sm">
            {+ele?.members ?? 0} members are following
          </p>
        </div>
        <div>
          {isCreatedByMe ? (
            <>
              <p className="tw-italic tw-text-[#787E89] tw-text-sm">
                Created by me
              </p>
            </>
          ) : (
            <CustomButton
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                setIsJoined((prev) => !prev);
                joinHandler(ele?.id);
              }}
              className={`lg:!tw-px-8 lg:!tw-py-4 !tw-py-2.5 !tw-px-5 !tw-bg-transparent ${
                isJoined
                  ? "!tw-border-[#787E89]  !tw-text-[#787E89]"
                  : " !tw-text-primary-purple"
              } `}
              count={8}
            >
              {isJoined ? "Joined" : "Join"}
            </CustomButton>
          )}
        </div>
      </div>
    </>
  );
};

export default CommunityCard;
