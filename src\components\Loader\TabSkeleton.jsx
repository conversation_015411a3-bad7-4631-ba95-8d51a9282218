import { Skeleton } from "../ui/skeleton";

const TabSkeleton = ({ tabCount = 3, className = "" }) => {
  return (
    <>
      <div className={`tw-flex  tw-items-center tw-gap-4 ${className} `}>
        {Array.from({ length: tabCount }, (_, i) => i + 1)?.map((ele) => (
          <Skeleton
            type="button"
            key={ele}
            className={`tw-py-6  !tw-rounded-full tw-px-12`}
          />
        ))}
      </div>
    </>
  );
};

export default TabSkeleton;
