// hooks/useScrollRestoration.js
'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useRouter } from "nextjs-toploader/app";

const scrollPositions = new Map();

export default function useScrollRestoration(key = '/') {
    const pathname = usePathname();
    const router = useRouter();
    const shouldRestore = useRef(false);

    useEffect(() => {
        const handleRouteChangeStart = () => {
            scrollPositions.set(key, window.scrollY);
        };

        const handleRouteChangeComplete = () => {
            if (shouldRestore.current) {
                const y = scrollPositions.get(key) ?? 0;
                window.scrollTo(0, y);
                shouldRestore.current = false;
            }
        };

        // We use global router events from next/navigation using the router instance.
        if (router && router.events?.on) {
            router.events.on('routeChangeStart', handleRouteChangeStart);
            router.events.on('routeChangeComplete', handleRouteChangeComplete);
        }

        return () => {
            if (router && router.events?.off) {
                router.events.off('routeChangeStart', handleRouteChangeStart);
                router.events.off('routeChangeComplete', handleRouteChangeComplete);
            }
        };
    }, [router, key]);

    useEffect(() => {
        shouldRestore.current = true;
    }, [pathname]);
}
