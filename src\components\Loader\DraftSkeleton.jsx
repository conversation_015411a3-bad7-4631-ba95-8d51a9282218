import { Skeleton } from "../ui/skeleton";

const DraftSkeleton = ({ count = 6 }) => {
  return (
    <>
      {Array.from({ length: count }, (_, i) => i + 1).map((ele) => (
        <div
          key={ele}
          className="tw-mb-6 tw-flex tw-justify-between tw-items-center tw-w-full tw-h-[18rem] tw-gap-5 tw-px-7 tw-py-10 tw-rounded-[30px] 320:tw-rounded-[20px] tw-text-inherit tw-bg-[#F5F7F8] "
        >
          <div className="tw-w-full 2xl:tw-col-span-8 xl:tw-col-span-7 lg:tw-col-span-8 md:tw-col-span-7 tw-col-span-12">
            <div className="tw-flex tw-pt-10 tw-flex-col 2xl:tw-gap-y-1 xl:tw-gap-y-2 lg:tw-gap-y-2.5 tw-gap-y-2">
              <Skeleton className="tw-h-4 tw-w-[14.5rem] " />
              <div className="tw-flex tw-justify-between tw-mt-2">
                <Skeleton className="tw-h-6 tw-w-[20rem] " />
              </div>
              <Skeleton className="tw-h-4 tw-my-0.5 tw-w-full " />
              <div className="tw-flex tw-justify-end tw-items-center"></div>
            </div>
          </div>
          <Skeleton className="tw-relative tw-flex tw-justify-end tw-items-center tw-w-[35rem]  2xl:tw-h-[179px] xl:tw-h-[180px]  2xl:tw-col-span-4 xl:tw-col-span-5 lg:tw-col-span-4 md:tw-col-span-5 tw-col-span-12 !tw-rounded-[20px]" />
        </div>
      ))}
    </>
  );
};

export default DraftSkeleton;
