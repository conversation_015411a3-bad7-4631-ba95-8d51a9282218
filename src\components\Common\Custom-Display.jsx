export const CustomCol = ({
  children,
  xs,
  sm,
  md,
  lg,
  xl,
  className = "",
  total = 12,
}) => {
  return (
    <div
      className={`   ${xs ? `sm:tw-w-${xs}/${total}` : ""} ${
        sm ? `md:tw-w-${sm}/${total}` : ""
      } ${md ? `lg:tw-w-${md}/${total}` : ""} ${
        lg ? `xl:tw-w-${lg}/${total}` : ""
      } ${xl ? `2xl:tw-w-${xl}/${total}` : ""} ${className}`}
    >
      {children}
    </div>
  );
};

export const CustomContainer = ({ children, className = "", ...args }) => {
  return (
    <div
      {...args}
      // className={`container m-auto px-2 sm:px-4 lg:px-4 xl:px-24 2xl:px-32 3xl:px-24   ${className}`}
      // className={`container m-auto sm:px-4 lg:px-10 xl:px-40 2xl:px-40 3xl:px-40  4xl:px-48 max-[391px]:max-w-[340px] sm:max-w-470px md:max-w-500px lg:max-w-725px xl:max-w-880px 2xl:max-w-1100px 3xl:max-w-1250px 4xl:max-w-1600px ${className}`}
      className={` sm:tw-container sm:tw-mx-auto tw-px-5 md:tw-px-10 lg:tw-px-24   4xl:tw-px-48 ${className}`}
    >
      {children}
    </div>
  );
};

export const CustomRow = ({ children, className = "" }) => {
  return (
    <div className={`tw-flex tw-flex-wrap tw-gap-2 ${className}`}>
      {children}
    </div>
  );
};

export const CustomGrid = ({
  xs = 0,
  sm = 0,
  md = 0,
  lg = 0,
  xl = 0,
  xxl = 0,
  xxxl = 0,
  data = [],
  Component,
  className = "gap-8 ",
}) => {
  const smGrid = {
    0: "",
    1: "sm:tw-grid-cols-1",
    2: "sm:tw-grid-cols-2",
    3: "sm:tw-grid-cols-3",
    4: "sm:tw-grid-cols-4",
    5: "sm:tw-grid-cols-5",
    6: "sm:tw-grid-cols-6",
  };
  const xsGrid = {
    0: "",
    1: "xs:tw-grid-cols-1",
    2: "xs:tw-grid-cols-2",
    3: "xs:tw-grid-cols-3",
    4: "xs:tw-grid-cols-4",
    5: "xs:tw-grid-cols-5",
    6: "xs:tw-grid-cols-6",
  };
  const mdGrid = {
    0: "",
    1: "md:tw-grid-cols-1",
    2: "md:tw-grid-cols-2",
    3: "md:tw-grid-cols-3",
    4: "md:tw-grid-cols-4",
    5: "md:tw-grid-cols-5",
    6: "md:tw-grid-cols-6",
  };
  const lgGrid = {
    0: "",
    1: "lg:tw-grid-cols-1",
    2: "lg:tw-grid-cols-2",
    3: "lg:tw-grid-cols-3",
    4: "lg:tw-grid-cols-4",
    5: "lg:tw-grid-cols-5",
    6: "lg:tw-grid-cols-6",
  };
  const xlGrid = {
    0: "",
    1: "xl:tw-grid-cols-1",
    2: "xl:tw-grid-cols-2",
    3: "xl:tw-grid-cols-3",
    4: "xl:tw-grid-cols-4",
    5: "xl:tw-grid-cols-5",
    6: "xl:tw-grid-cols-6",
  };
  const xxlGrid = {
    0: "",
    1: "2xl:tw-grid-cols-1",
    2: "2xl:tw-grid-cols-2",
    3: "2xl:tw-grid-cols-3",
    4: "2xl:tw-grid-cols-4",
    5: "2xl:tw-grid-cols-5",
    6: "2xl:tw-grid-cols-6",
  };
  const xxxlGrid = {
    0: "",
    1: "3xl:tw-grid-cols-1",
    2: "3xl:tw-grid-cols-2",
    3: "3xl:tw-grid-cols-3",
    4: "3xl:tw-grid-cols-4",
    5: "3xl:tw-grid-cols-5",
    6: "3xl:tw-grid-cols-6",
  };
  const xxxxlGrid = {
    0: "",
    1: "4xl:tw-grid-cols-1",
    2: "4xl:tw-grid-cols-2",
    3: "4xl:tw-grid-cols-3",
    4: "4xl:tw-grid-cols-4",
    5: "4xl:tw-grid-cols-5",
    6: "4xl:tw-grid-cols-6",
  };

  return (
    <div
      // className={`grid grid-cols-1 ${xsGrid[xs]} ${smGrid[sm]}  ${mdGrid[md]}  ${lgGrid[lg]}  ${xlGrid[xl]}  ${xxlGrid[xxl]}  ${xxxlGrid[xxxl]} ${className} `}
      className={`tw-grid tw-grid-cols-1 tw-mb-16 lg:tw-mb-0 ${xsGrid[xs]} ${
        smGrid[sm]
      }  ${mdGrid[md]}  ${lgGrid[lg]}  ${xlGrid[xl]}  ${xxlGrid[xxl]}  ${
        xxxlGrid[xxxl]
      } ${
        xxxxlGrid[xxxl || xxl || xl || lg || md || sm || xs || 0]
      }  ${className}`}
    >
      {/* Single loop to render grid items for all breakpoints */}
      {/* {data.slice(0, xs + sm + md + lg + xl + xxl + xxxl).map((item, index) => (
        <Component key={index} data={item} />
      ))} */}
      {data?.map((item, index) => (
        <Component key={`custom-${index}`} index={index} data={item} />
      ))}
    </div>
  );
};

export const CustomButtonGrid = ({ children, className = "" }) => {
  return (
    <div
      className={`tw-flex tw-flex-wrap tw-justify-center tw-mt-5 tw-gap-x-5 sm:tw-gap-x-16 tw-gap-y-5 md:tw-mt-10 ${className}`}
    >
      {children}
    </div>
  );
};

export const HeadingTags = ({ htag = 2, children, ...rest }) => {
  switch (htag) {
    case 1:
      return <h1 {...rest}>{children}</h1>;
    case 2:
      return <h2 {...rest}>{children}</h2>;
    case 3:
      return <h3 {...rest}>{children}</h3>;
    case 4:
      return <h4 {...rest}>{children}</h4>;
    case 5:
      return <h5 {...rest}>{children}</h5>;
    case 6:
      return <h6 {...rest}>{children}</h6>;
    default:
      return <p {...rest}>{children}</p>;
  }
};
