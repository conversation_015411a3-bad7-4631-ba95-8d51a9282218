import { useEffect, useRef, useState } from "react";
// import { Spinner } from "react-bootstrap";

const InfiniteScroll = (props) => {
  const {
    loadMoreFunction = () => {},
    threshold = 90,
    isLoading,
    loadOff,
    timeout = 500,
    loadingComponent,
  } = props;
  const debounce = useRef(null);
  const [scroll, setScroll] = useState(1);
  const handleScroll = () => {
    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const scrollPercentage = ((scrollTop + windowHeight) / scrollHeight) * 100;
    // console.log(scrollPercentage, threshold);

    if (scrollPercentage >= threshold) {
      setScroll(scrollPercentage);
    }
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  useEffect(() => {
    if (scroll < threshold) {
      return;
    }
    clearTimeout(debounce.current);
    debounce.current = setTimeout(() => {
      if (loadOff || isLoading) {
        return;
      } else {
        loadMoreFunction();
      }
    }, timeout);
  }, [scroll]);
  return <>{isLoading && !loadOff ? <>{loadingComponent}</> : ""}</>;
};

export default InfiniteScroll;
