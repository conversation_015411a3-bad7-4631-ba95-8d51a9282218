import { Skeleton } from "../ui/skeleton";

const ExplorePostSkeleton = ({ count = 6 }) => {
  return (
    <>
      {Array.from({ length: count }, (_, i) => i + 1).map((ele) => (
        <div key={ele}>
          <div className="lg:tw-hidden tw-mb-6 tw-mt-3">
            <Skeleton className="tw-text-base !tw-rounded-[25px] tw-font-semibold tw-h-[20rem] tw-my-0.5 tw-w-[100%] " />
          </div>
          {/* Post Skeleton Pc View */}
          <div className="tw-hidden  tw-mb-6 tw-mt-3 lg:tw-flex tw-justify-between tw-items-center tw-w-full tw-h-[18rem] tw-gap-5 tw-px-7 tw-py-6 tw-rounded-[30px] 320:tw-rounded-[20px] tw-text-inherit tw-bg-[#F5F7F8] ">
            <div className="tw-w-full 2xl:tw-col-span-8 xl:tw-col-span-7 lg:tw-col-span-8 md:tw-col-span-7 tw-col-span-12">
              <div className="tw-flex tw-flex-col 2xl:tw-gap-y-1 xl:tw-gap-y-2 lg:tw-gap-y-2.5 tw-gap-y-2">
                <div className="">
                  <div className="tw-flex tw-flex-row tw-gap-2">
                    <Skeleton className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[42px] tw-h-[42px] tw-bg-gray-300 !tw-rounded-full tw-overflow-hidden tw-cursor-pointer" />
                    <div className="">
                      <Skeleton className="tw-text-base tw-font-semibold tw-h-4 tw-my-0.5 tw-w-[14.5rem] " />
                      <div className="tw-flex tw-flex-row tw-gap-1 tw-items-center tw-my-1">
                        <Skeleton className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px] tw-bg-gray-300 !tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center"></Skeleton>
                        <Skeleton className="tw-font-semibold tw-h-4 tw-w-[13rem]"></Skeleton>
                      </div>
                      <Skeleton className="tw-h-4 tw-w-[14.5rem] " />
                    </div>
                  </div>
                </div>
                <div className="tw-flex tw-justify-between tw-mt-2">
                  <Skeleton className="tw-h-6 tw-w-[20rem] " />
                </div>
                <Skeleton className="tw-h-4 tw-my-0.5 tw-w-full " />
                <div className="tw-flex tw-justify-between tw-items-center">
                  <div className="tw-flex tw-gap-5 tw-text-light-gray-700 xl:tw-text-base lg:tw-text-sm tw-text-12px">
                    <Skeleton className="tw-flex tw-gap-x-2 tw-mt-1 tw-h-7 tw-w-[2rem]"></Skeleton>
                    <Skeleton className="tw-flex tw-gap-x-2 tw-mt-1 tw-h-7 tw-w-[2rem]"></Skeleton>
                    <Skeleton className="tw-flex tw-gap-x-2 tw-mt-1 tw-h-7 tw-w-[2rem]"></Skeleton>
                    <Skeleton className="tw-flex tw-gap-x-2 tw-mt-1 tw-h-7 tw-w-[2rem]"></Skeleton>
                  </div>
                </div>
              </div>
            </div>
            <Skeleton className="tw-relative tw-flex tw-justify-end tw-items-center tw-w-[35rem]  2xl:tw-h-[179px] xl:tw-h-[180px]  2xl:tw-col-span-4 xl:tw-col-span-5 lg:tw-col-span-4 md:tw-col-span-5 tw-col-span-12 !tw-rounded-[20px]" />
          </div>
        </div>
      ))}
    </>
  );
};

export default ExplorePostSkeleton;
