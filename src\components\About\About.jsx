"use client";
import Image from "next/image";
import { CustomContainer } from "../Common/Custom-Display";
import logo from "../../../public/images/logo/logo-primary.svg";
import { blurDataURL } from "@/utils/function";
import { useRouter } from "nextjs-toploader/app";
import { useProgress } from "@bprogress/next";
import { LeftArrowBackIcon } from "@/utils/icons";

const About = () => {
  const router = useRouter();
  const progress = useProgress();
  return (
    <>
      <CustomContainer className="!tw-pt-[2rem] tw-h-full">
        <button
          type="button"
          onClick={() => {
            progress.start(0, 1);
            router.back();
          }}
          className="tw-mb-4"
        >
          <LeftArrowBackIcon />
        </button>
        <div className="tw-relative tw-w-[7.5rem] tw-h-[2.8rem] ">
          <Image
            src={logo}
            alt="logo"
            placeholder="blur"
            blurDataURL={blurDataURL(120, 80)}
            fill
            className=""
          />
        </div>
        <h1 className="tw-text-primary-black tw-text-3xl tw-font-semibold tw-text-center tw-mt-5 lg:tw-mt-0 lg:tw-text-left lg:tw-absolute tw-left-[43%] tw-top-[4rem]">
          About Floment
        </h1>
        <div className="tw-flex tw-flex-col tw-gap-8 tw-items-center tw-max-w-[38rem] tw-mx-auto">
          <div className="tw-flex tw-flex-col tw-gap-3 tw-items-center tw-mt-2">
            <div className="tw-text-center tw-text-lg tw-text-primary-black  ">
              Welcome to Floment, your ultimate productivity companion. Our
              platform is designed to help you achieve your goals and maximize
              your potential.
            </div>
          </div>
          {/* Vision */}
          <div className="tw-flex tw-flex-col tw-gap-3 tw-items-center tw-mt-6">
            <h1 className="tw-text-primary-black tw-text-3xl tw-font-semibold">
              Vision
            </h1>
            <div className="tw-text-center tw-text-lg tw-text-primary-black  ">
              Built exclusively for you and your goals Tailored tools and
              features to help you achieve your unique goals.
            </div>
          </div>
          {/* Mission */}
          <div className="tw-flex tw-flex-col tw-gap-3 tw-items-center tw-mt-6">
            <h1 className="tw-text-primary-black tw-text-3xl tw-font-semibold">
              Mission
            </h1>
            <div className="tw-text-center tw-text-lg tw-text-primary-black  ">
              Strategize and set clear, actionable goals for your projects. Use
              our robust tools to outline your objectives and create a roadmap
              for success.
            </div>
          </div>
          {/* Who are we? */}
          <div className="tw-flex tw-flex-col tw-gap-3 tw-items-center tw-mt-6">
            <h1 className="tw-text-primary-black tw-text-3xl tw-font-semibold">
              Who are we?
            </h1>
            <div className="tw-text-center tw-text-lg tw-text-primary-black  ">
              Floment began in co-founder Raj Patel living room in 2002 and was
              officially launched on May 5, 2003.
            </div>
            <div className="tw-text-center tw-text-lg tw-text-primary-black  ">
              Floment is a platform that connects you with the right people,
              resources, and opportunities to help you achieve your goals. We
              are committed to providing you with the tools and support you need
              to succeed.
            </div>
          </div>
        </div>
      </CustomContainer>
    </>
  );
};

export default About;
