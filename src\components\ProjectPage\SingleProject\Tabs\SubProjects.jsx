"use client";
import { getProject, reportProject } from "@/app/action";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import ReportModal from "@/components/Common/ReportModal";
import ShareModal from "@/components/Common/ShareModal";
import { useProject } from "@/components/context/ProjectContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import SubProjectModal from "@/components/HomePage/Form/SubProjectModal";
import ProjectSkeleton from "@/components/Loader/ProjectSkeleton";
import SubProjectsCard from "@/components/SubProjects/SubProjectsCard";
import authStorage from "@/utils/API/AuthStorage";
import { hasAProjectAccess } from "@/utils/function";
import { AddProjectIcon } from "@/utils/icons";
import { safeToast } from "@/utils/safeToast";
import { useProgress } from "@bprogress/next";
import { useRouter } from "nextjs-toploader/app";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

const SubProjects = () => {
  const [dataList, setDataList] = useState([]);
  const [userData, setUserData] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [isSubProject, setIsSubProject] = useState(false);
  const [refresh, setRefresh] = useState(false);

  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareData, setShareData] = useState(null);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);
  const router = useRouter();
  const progress = useProgress();
  const { project } = useProject();
  const projectAPI = useApiRequest(false);
  const api = useApiRequest();
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // console.log(project, dataList);
  // Fetch Data
  const fetchData = (projectId) => {
    const user = authStorage.getProfileDetails();
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      ParentId: +projectId,
      UserId: +user?.id,
    };

    setUserData(user);

    api.sendRequest(
      getProject,
      (res) => {
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
        if (res?.data?.data?.length && pagination?.page > 1) {
          setDataList((prev) => [...prev, ...res?.data?.data]);
        } else if (res?.data?.data?.length && pagination.page === 1) {
          setDataList((prev) => [...res?.data?.data]);
        } else {
        }
      },
      query
    );
  };
  useEffect(() => {
    if (project?.id) {
      fetchData(project?.id);
    }
  }, [pagination.page, project, refresh]);
  return (
    <>
      {/* Report Modal */}
      <ReportModal
        title="Why are you reporting this sub-project?"
        isOpen={isReported}
        setIsOpen={setIsReported}
        isLoading={projectAPI?.isLoading}
        onConfirm={(reason) => {
          const payload = {
            reason,
            id: reportData?.id,
          };
          projectAPI.sendRequest(
            reportProject,
            (res) => {
              safeToast.success(res?.message);
              setIsReported(false);
              resetDataList();
              setRefresh((prev) => !prev);
              setReportData(null);
            },
            payload
          );
        }}
      />
      <ShareModal
        open={isShareOpen}
        onClose={() => {
          setIsShareOpen(false);
        }}
        shareUrl={`${origin}/sub-project/${shareData?.slug}`}
        title={`${shareData?.name} \nCheckout this sub-project:`}
      />
      {/* Sub-Project Modal */}
      <SubProjectModal
        isOnlyCreateSubProject
        open={isSubProject}
        setOpen={setIsSubProject}
        reFetchData={resetDataList}
        setRefresh={setRefresh}
        modalTitle={"Create sub-project"}
        modalSubmitButton={"Save"}
        editProjectData={{
          ...project,
        }}
        isProjectPrivate={project?.isPrivate}
      />
      {/* Add Sub Project button */}
      {(project?.UserId === userData?.id ||
        project?.ProjectMembers?.[0]?.access === "write") && (
        <button
          type="button"
          onClick={() => {
            setIsSubProject(true);
          }}
          className="tw-fixed tw-bottom-16 tw-right-6 lg:tw-bottom-auto lg:tw-absolute lg:-tw-top-[6rem] lg:tw-right-[0rem] tw-z-50 tw-bg-primary-purple tw-border-primary-purple  tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white !tw-px-4 !tw-py-4 tw-transition tw-ease-in-out tw-duration-300 hover:tw-scale-110 !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold"
        >
          <AddProjectIcon size={22} />
          <span className="tw-hidden lg:tw-inline">Create</span>
        </button>
      )}
      {dataList?.length === 0 && !api.isLoading && (
        <div className="tw-flex tw-h-[20rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Sub Project available!" />
        </div>
      )}
      <div className="tw-mb-20 lg:tw-my-5 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7 tw-overflow-y-auto  tw-w-full ">
        {/* {api.isLoading && <ProjectSkeleton />} */}
        {dataList?.length > 0 && !api.isLoading
          ? dataList?.map((ele) => (
              <div key={ele?.id}>
                <SubProjectsCard
                  setRefresh={setRefresh}
                  projectData={project}
                  resetState={resetDataList}
                  onClick={() => {
                    if (
                      ele?.isPrivate &&
                      userData?.id !== project?.UserId &&
                      userData?.id !== ele?.UserId &&
                      ele?.ProjectMembers?.length === 0
                    ) {
                      safeToast.success("This Project is Private");
                      return;
                    }
                    progress.start(0, 1);
                    router.push(`/sub-project/${ele?.slug}`);
                  }}
                  settingMenu={
                    !ele?.isPrivate ||
                    userData?.id === ele?.UserId ||
                    userData?.id === project?.UserId
                  }
                  loginUserData={userData}
                  shareHandler={(ele) => {
                    setIsShareOpen(true);
                    setShareData(ele);
                  }}
                  reportHandler={(ele) => {
                    setIsReported(true);
                    setReportData(ele);
                  }}
                  ele={ele}
                />
              </div>
            ))
          : ""}
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            // if (pagination.total > dataList?.length) {
            if (
              pagination.page < Math.ceil(pagination.total / pagination.limit)
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<ProjectSkeleton count={6} />}
          timeout={10}
        />
      </div>
    </>
  );
};

export default SubProjects;
