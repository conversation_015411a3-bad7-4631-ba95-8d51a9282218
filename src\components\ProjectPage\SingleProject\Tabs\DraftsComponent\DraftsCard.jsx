import CustomTitle from "@/components/Common/CustomTitle";
import PostImageCarousel from "@/components/HomePage/PostImageCarousel";
import PostPreview from "@/components/HomePage/PostPreview";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { EditProfileIcon, ThreeDotMenuIcon } from "@/utils/icons";
import moment from "moment";
import { useMemo, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Trash2 } from "lucide-react";
import PopUpModal from "@/components/Common/PopUpModal";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { deletePost, getOnePost } from "@/app/action";
import PostModal from "@/components/HomePage/Form/PostModal";
import ShowMenuList from "@/components/Common/ShowMenuList";
import { convertUTCtoLocal, finalDescription } from "@/utils/function";
import { safeToast } from "@/utils/safeToast";

const DraftsCard = ({
  data,
  userData,
  setRefresh = () => {},
  resetDataList = () => {},
  projectData = {},
}) => {
  const [previewModal, setPreviewModal] = useState(false);
  const [postData, setPostData] = useState(null);
  const [deleteData, setDeleteData] = useState(null);
  const singleOneAPI = useApiRequest(false);
  const [editData, setEditData] = useState(null);

  const processedDescription = useMemo(() => {
    return finalDescription(data?.description);
  }, [data?.description]);

  // Get One Post Details
  const getPostDetails = (id, setDataState) => {
    singleOneAPI.sendRequest(
      getOnePost,
      (res) => {
        setDataState(res?.data);
      },
      {
        id,
      }
    );
  };

  const myPostSetting = [
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: (ele) => {
        getPostDetails(ele?.id, setEditData);
      },
    },

    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        setDeleteData(ele);
      },
    },
  ];

  return (
    <>
      {/* Edit Post Modal */}
      <PostModal
        editData={editData}
        setEditData={setEditData}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
        isRemoveAble={false}
        postForProject={!projectData?.ParentProject}
        postForSubProject={
          projectData?.ParentProject &&
          Object.keys(projectData?.ParentProject).length > 0
        }
        isPublishAble={true}
        // formData={{
        //   ...(projectData ?? {}),
        // }}
      />
      {/* Delete Modal */}
      <PopUpModal
        isLoading={singleOneAPI.isLoading}
        isOpen={deleteData}
        mainMessage="Delete Draft"
        subMessage="Are you sure you want to Delete Draft Permanently?"
        setIsOpen={setDeleteData}
        onConfirm={() => {
          singleOneAPI.sendRequest(
            deletePost,
            () => {
              setRefresh((prev) => !prev);
              resetDataList();
              setDeleteData(null);
            },
            {
              id: deleteData?.id,
            },
            ""
          );
        }}
      />
      {/* Preview Modal */}
      <PostPreview
        isCurrentUser={userData?.id !== postData?.User?.id}
        isOpen={previewModal}
        setIsOpen={setPreviewModal}
        data={postData}
        setData={setPostData}
        isLoading={singleOneAPI.isLoading}
        isDraft
      />
      <div
        key={`post-${data?.id}-${data?.slug}`}
        className={` tw-justify-between tw-items-center tw-w-full  tw-gap-5 tw-py-8 ${
          data?.media?.length > 0 ? "tw-pl-7 tw-pr-5" : "tw-px-7"
        } tw-rounded-[30px] 320:tw-rounded-[20px] tw-text-inherit tw-bg-[#F5F7F8] tw-cursor-pointer`}
        onClick={() => {
          if (
            userData?.id !== data?.User?.id &&
            projectData?.User?.id !== userData?.id
          ) {
            safeToast.error("You can not edit this draft");
            return;
          }
          getPostDetails(data?.id, setEditData);
        }}
      >
        <div className="tw-w-full ">
          <div className="tw-flex tw-py-5  tw-flex-col 2xl:tw-gap-y-1 xl:tw-gap-y-2 lg:tw-gap-y-2.5 tw-gap-y-2">
            <div className="tw-flex tw-justify-between ">
              <div className="tw-flex tw-flex-row tw-gap-2">
                <div className="">
                  <p className="tw-text-primary-black tw-text-opacity-70 tw-text-[13px]">
                    {/* Edited on {moment(data?.updatedAt)?.format("MMM DD, YYYY")} */}
                    Edited on{" "}
                    {convertUTCtoLocal(data?.updatedAt, "MMM DD, YYYY")}
                  </p>
                </div>
              </div>

              {(userData?.id === projectData?.User?.id ||
                userData?.id === data?.User?.id) && (
                <div className="tw-flex tw-justify-end tw-items-center">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <ShowMenuList
                        align="center"
                        data={data}
                        menuList={myPostSetting}
                      >
                        <div className="tw-text-light-gray-700 xl:tw-text-base lg:tw-text-sm tw-text-12px tw-flex tw-justify-end tw-cursor-pointer tw-items-center">
                          <ThreeDotMenuIcon />
                        </div>
                      </ShowMenuList>
                    </TooltipTrigger>
                    <TooltipContent>Updated</TooltipContent>
                  </Tooltip>
                </div>
              )}
            </div>
          </div>
        </div>
        <div
          className={`tw-grid tw-gap-4 lg:tw-gap-0 tw-grid-cols-1 ${
            data?.media?.length > 0 ? "tw-grid-cols-2" : "tw-grid-cols-1"
          } `}
        >
          <div className="tw-order-2 lg:tw-order-1 tw-w-full">
            <div className="tw-flex tw-justify-between">
              <CustomTitle
                textClassName="tw-break-all"
                htag={2}
                name={`${data?.title}`}
              />
            </div>

            {processedDescription && (
              <div
                id="post-description"
                className={`xl:tw-text-base tw-text-sm tw-line-clamp-2 tw-w-full ${
                  data?.media?.length > 0
                    ? "lg:tw-max-w-[27rem]"
                    : "lg:tw-max-w-[100%]"
                } tw-break-all`}
                dangerouslySetInnerHTML={{
                  __html: processedDescription,
                }}
              />
            )}
          </div>
          {data?.media?.length > 0 && (
            <div className="tw-order-1 lg:tw-order-2 tw-flex tw-justify-end">
              <PostImageCarousel imagArr={data?.media} />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default DraftsCard;
