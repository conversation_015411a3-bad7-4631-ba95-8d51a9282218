import { getProject, getSubProjectsFromProjectIds } from "@/app/action";
import CustomButton from "@/components/Common/Custom-Button";
import Empty from "@/components/Common/Empty";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import ProjectSkeleton from "@/components/Loader/ProjectSkeleton";
import SubProjectsCard from "@/components/SubProjects/SubProjectsCard";
import authStorage from "@/utils/API/AuthStorage";
import { blurDataURL } from "@/utils/function";
import { CheckMarkIcon, LeftArrowBackIcon } from "@/utils/icons";
import { SearchIcon } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import SubProjectModal from ".././Form/SubProjectModal";
import SearchBar from "@/components/Common/SearchBar";
import { useDebouncedSearch } from "@/components/helper/hook/useDebouncedSearch";
import tempProjectImg from "../../../../public/images/assets/default_image.png";

const SelectProjectModal = ({
  open,
  setIsOpen,
  selectedProject,
  setSelectedProject,
  selectedSubProject,
  setSelectedSubProject,
  postForProject = false,
  postForSubProject = false,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [subProjectList, setSubProjectList] = useState([]);
  const [subProjectPagination, setSubProjectPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [tempSelection, setTempSelection] = useState(null);
  const [tempSelectedProject, setTempSelectedProject] = useState(null);
  const [tempSelectedSubProject, setTempSelectedSubProject] = useState(null);
  const [isSubProject, setIsSubProject] = useState(false);
  const [search, setSearch] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const api = useApiRequest();

  const resetModal = () => {
    setIsOpen(false);
    setDataList([]);
    setSubProjectList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
    setSubProjectPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
    setTempSelectedProject(null);
    setTempSelectedSubProject(null);
    setTempSelection(null);
    setSearchQuery("");
    setSearch("");
  };

  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // On search
  // const onSearch = (value) => {
  //   if (debounceRef.current) {
  //     clearTimeout(debounceRef.current);
  //   }
  //   debounceRef.current = setTimeout(() => {
  //     setDataList([]);
  //     setPagination({
  //       page: 1,
  //       limit: 20,
  //       total: 0,
  //     });

  //     setSearchQuery(value);
  //   }, 500);
  // };
  const onSearch = useDebouncedSearch({
    setSearchQuery, // required
    resetData, // optional
    setSearch, // optional
  });

  // Fetch Projects
  const fetchProjects = useCallback(async () => {
    // ✅ Set loading before API call
    const userDetails = authStorage.getProfileDetails();
    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
      UserId: userDetails?.id,
    };
    if (searchQuery) {
      queryParams = {
        ...queryParams,
        searchQuery,
      };
    }

    // console.log(queryParams);
    try {
      await api.sendRequest(
        getProject,
        (res) => {
          if (pagination.page === 1) {
            setDataList(res?.data?.data);
          } else {
            setDataList((prev) => [...prev, ...res?.data?.data]);
          }
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
        },

        queryParams
      );
    } catch (error) {
      console.error("Error fetching projects:", error);
    } finally {
      // ✅ Set loading false after API response (success or error)
    }
  }, [pagination.page, searchQuery]);
  // Fetch Projects
  const fetchSubProjects = useCallback(
    async (project) => {
      const userDetails = authStorage.getProfileDetails();

      // console.log(projectId);

      // ✅ Set loading before API call
      let queryParams = {
        page: subProjectPagination.page,
        limit: subProjectPagination.limit,
        ParentId: project?.id,
        UserId: userDetails?.id,
      };

      // console.log(queryParams);
      try {
        await api.sendRequest(
          getProject,
          (res) => {
            // console.log(res?.data?.data);
            let dataItems = res?.data?.data?.filter(
              (ele) =>
                project?.UserId === userDetails?.id ||
                ele?.UserId === userDetails?.id ||
                (ele?.ProjectMembers?.length > 0 &&
                  ele?.ProjectMembers?.[0]?.access === "write")
            );

            if (subProjectPagination.page === 1) {
              setSubProjectList(dataItems);
            } else {
              setSubProjectList((prev) => [...prev, ...dataItems]);
            }
            setSubProjectPagination((prev) => ({
              ...prev,
              total: res?.data?.totalRecords,
            }));
          },

          queryParams
        );
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        // ✅ Set loading false after API response (success or error)
      }
    },
    [subProjectPagination.page, tempSelectedProject]
  );

  // Fetch Projects
  useEffect(() => {
    if (open) {
      fetchProjects();
    }
  }, [pagination.page, open, searchQuery]);

  useEffect(() => {
    if (selectedProject && Object.keys(selectedProject).length > 0) {
      setTempSelectedProject(selectedProject);
      if (postForProject || postForSubProject) {
        setTempSelection(selectedProject);
        fetchSubProjects(selectedProject);
      }
    }
    if (selectedSubProject && Object.keys(selectedSubProject).length > 0) {
      setTempSelectedSubProject(selectedSubProject);
    }
  }, [selectedProject, selectedSubProject, open]);
  return (
    <>
      {/* Sub Project Modal */}
      <SubProjectModal
        isOnlyCreateSubProject
        open={isSubProject}
        setOpen={setIsSubProject}
        reFetchData={() => {
          setSubProjectList([]);
          setSubProjectPagination({
            page: 1,
            limit: 20,
            total: 0,
          });
          fetchSubProjects(tempSelectedProject);
        }}
        // setRefresh={setRefresh}
        modalTitle={"Create sub-project"}
        modalSubmitButton={"Save"}
        editProjectData={{
          ...tempSelectedProject,
        }}
      />
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-[50rem] !tw-m-0 md:!tw-m-2 !tw-rounded-[1.25rem] ",
          overlay: "!tw-bg-[#000000CC]",
        }}
        focusTrapped={false}
        open={open}
        center
        onClose={() => resetModal()}
      >
        <div className="tw-py-5 tw-mx-14 tw-relative">
          <div className="">
            {tempSelection && !postForProject && !postForSubProject && (
              <button
                type="button"
                onClick={() => {
                  setTempSelection(null);
                  // setSearchQuery("");
                  // setSearch("");
                  setTempSelectedProject(null);
                  setTempSelectedSubProject(null);
                  setSubProjectList([]);
                  setSubProjectPagination({
                    page: 1,
                    limit: 20,
                    total: 0,
                  });
                  // resetData();
                }}
                className="tw-absolute tw-top-6 tw-left-5"
              >
                <LeftArrowBackIcon />
              </button>
            )}
            <p className="tw-text-primary-black tw-font-semibold tw-text-2xl tw-text-center">
              {tempSelection ? "Project" : "Select Project"}
            </p>
            {/* <div /> */}
          </div>
          {/* Search */}
          {!tempSelection && (
            <SearchBar
              search={search}
              setSearch={setSearch}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              onSearch={onSearch}
              resetData={resetData}
            />
            // <div className="tw-flex tw-my-4 tw-gap-2 tw-items-center tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8]">
            //   <SearchIcon color="#787E89" />
            //   <input
            //     type="text"
            //     id="simple-search"
            //     className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500  `}
            //     autoComplete="off"
            //     placeholder="Search"
            //     onChange={(e) => {
            //       onSearch(e.target.value);
            //     }}
            //   />
            // </div>
          )}

          {/* data */}
          {tempSelection ? (
            <>
              <div className="tw-mt-4">
                <div
                  className="tw-cursor-pointer tw-relative"
                  onClick={() => {
                    if (!postForProject && !postForSubProject) {
                      setTempSelectedProject((prev) =>
                        prev?.id === tempSelection?.id ? null : tempSelection
                      );
                    }
                  }}
                >
                  {tempSelection?.id === tempSelectedProject?.id &&
                    postForProject &&
                    !postForSubProject && (
                      <div className="tw-absolute tw-left-[16.5rem] tw-top-[0.4rem] tw-z-20">
                        <CheckMarkIcon size={22} />
                      </div>
                    )}
                  <div className="tw-relative tw-w-[19rem] tw-h-[12rem] tw-rounded-3xl">
                    <Image
                      fill
                      className={`!tw-rounded-3xl !tw-object-cover tw-border-2 ${
                        tempSelection?.id === tempSelectedProject?.id &&
                        postForProject &&
                        !postForSubProject
                          ? "!tw-border-[#10BE5B]"
                          : "!tw-border-transparent"
                      }`}
                      src={tempSelection?.image ?? tempProjectImg}
                      onError={() =>
                        setTempSelection((prev) => ({
                          ...prev,
                          image: tempProjectImg,
                        }))
                      }
                      alt={tempSelection?.name ?? "sub-project"}
                      placeholder="blur"
                      blurDataURL={blurDataURL(290, 180)}
                    />
                    {/* <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
                  
                    {ele?.isPrivate && (
                      <div className="tw-absolute tw-bottom-2 tw-right-3 tw-bg-primary-purple tw-p-2 tw-rounded-full">
                        <PrivateProjectIcon size={20} />
                      </div>
                    )}
                  </div> */}
                  </div>

                  <p className="tw-text-2xl tw-font-bold tw-my-1 tw-break-all ">
                    {tempSelection?.name ?? ""}
                  </p>
                </div>

                {/* </Link> */}
              </div>
              <p className="tw-text-center tw-text-xl tw-font-semibold tw-my-4">
                Select Sub-Project
              </p>
              {api.isLoading && subProjectPagination.page === 1 && (
                <div className="tw-grid tw-grid-cols-2 tw-gap-5 ">
                  <ProjectSkeleton count={4} />.
                </div>
              )}
              {subProjectList?.length > 0 ? (
                <InfiniteScroll
                  height={250}
                  dataLength={subProjectList?.length ?? 0}
                  className="infinite-scrollbar"
                  hasMore={
                    subProjectPagination.page <
                    Math.ceil(
                      subProjectPagination.total / subProjectPagination.limit
                    )
                  }
                  next={() =>
                    setSubProjectList((prev) => ({
                      ...prev,
                      page: prev.page + 1,
                    }))
                  }
                  loader={
                    <div className="tw-grid tw-grid-cols-2 tw-gap-5">
                      <ProjectSkeleton count={4} />.
                    </div>
                  }
                >
                  <div className="tw-grid tw-grid-cols-2 tw-gap-5 ">
                    {subProjectList?.map((ele) => (
                      <div
                        className={`tw-relative `}
                        key={ele?.id}
                        onClick={() => {
                          // setTempSelection(ele);
                          // setTempSelection((prev) =>
                          //   prev?.id === ele?.id ? null : ele
                          // );
                          if (!postForSubProject) {
                            setTempSelectedSubProject((prev) =>
                              prev?.id === ele?.id ? null : ele
                            );
                          }
                        }}
                      >
                        {tempSelectedSubProject?.id === ele?.id && (
                          <div className="tw-absolute tw-right-4 tw-top-2 tw-z-20">
                            <CheckMarkIcon size={22} />
                          </div>
                        )}
                        <SubProjectsCard
                          ele={ele}
                          countKey={null}
                          countType={null}
                          settingMenu={false}
                          imageClass={`tw-border-2 ${
                            tempSelectedSubProject?.id === ele?.id
                              ? "!tw-border-[#10BE5B]"
                              : "!tw-border-transparent"
                          }`}
                        />
                      </div>
                    ))}
                  </div>
                </InfiniteScroll>
              ) : (
                <div className="tw-flex tw-justify-center tw-items-center tw-pb-20">
                  <CustomButton
                    className={"!tw-px-9 !tw-py-[14px]"}
                    type="button"
                    count={8}
                    onClick={() => {
                      // console.log("here");
                      setIsSubProject(true);
                    }}
                  >
                    <span className={"!tw-text-base"}>Create Sub-Project</span>
                  </CustomButton>
                </div>
              )}
            </>
          ) : (
            <>
              {api.isLoading && pagination.page === 1 && (
                <div className="tw-grid tw-grid-cols-2 tw-gap-5 tw-pt-5">
                  <ProjectSkeleton count={4} />.
                </div>
              )}
              {dataList?.length > 0 && (
                <InfiniteScroll
                  height={500}
                  dataLength={dataList?.length ?? 0}
                  className="infinite-scrollbar"
                  hasMore={
                    Math.ceil(pagination.total / pagination.limit) >
                    pagination.page
                  }
                  next={() =>
                    setPagination((prev) => ({
                      ...prev,
                      page: prev.page + 1,
                    }))
                  }
                  loader={
                    <div className="tw-grid tw-grid-cols-2 tw-gap-5 tw-pt-5">
                      <ProjectSkeleton count={4} />.
                    </div>
                  }
                >
                  <div className="tw-grid tw-grid-cols-2 tw-gap-5 tw-pt-5">
                    {dataList?.map((ele) => (
                      <div
                        className={`tw-relative `}
                        key={ele?.id}
                        onClick={() => {
                          setTempSelection(ele);
                          setTempSelectedProject(ele);
                          fetchSubProjects(ele);
                        }}
                      >
                        {tempSelectedProject?.id === ele?.id && (
                          <div className="tw-absolute tw-right-4 tw-top-2 tw-z-20">
                            <CheckMarkIcon size={22} />
                          </div>
                        )}
                        <SubProjectsCard
                          ele={ele}
                          countKey="subProjectsCount"
                          countType="Sub-projects"
                          settingMenu={false}
                          imageClass={`tw-border-2 ${
                            tempSelectedProject?.id === ele?.id
                              ? "!tw-border-[#10BE5B]"
                              : "!tw-border-transparent"
                          }`}
                        />
                      </div>
                    ))}
                  </div>
                </InfiniteScroll>
              )}
            </>
          )}
          {!api.isLoading && dataList?.length === 0 && (
            <div className="tw-flex tw-justify-center tw-items-center">
              <Empty label="No Project Found" />
            </div>
          )}
          {!api.isLoading &&
            (tempSelectedProject || tempSelectedSubProject) &&
            !postForSubProject && (
              <div className="tw-w-full tw-flex tw-justify-center tw-absolute  tw-bottom-4 tw-z-30">
                {/* tw-bg-projectModal */}
                <CustomButton
                  className={"!tw-px-9 !tw-py-[14px]"}
                  type="button"
                  count={8}
                  onClick={() => {
                    // console.log("here");
                    // setSelectedProject(tempSelection);
                    // onMoveHandler(tempSelection, data);
                    setSelectedProject(tempSelectedProject ?? tempSelection);
                    setSelectedSubProject(tempSelectedSubProject);
                    resetModal();
                  }}
                >
                  <span className={"!tw-text-base"}>Save</span>
                </CustomButton>
              </div>
            )}
        </div>
      </Modal>
    </>
  );
};

export default SelectProjectModal;
