{"name": "incenti-ai-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@bprogress/next": "^3.2.12", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "antd": "^5.24.0", "antd-input-otp": "^2.1.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "firebase": "^11.2.0", "formik": "^2.4.6", "framer-motion": "^12.19.1", "hls.js": "^1.6.2", "lucide-react": "^0.477.0", "marked": "^16.0.0", "moment": "^2.30.1", "next": "15.1.4", "next-share": "^0.27.0", "nprogress": "^0.2.0", "quill": "^2.0.3", "quill-magic-url": "^4.2.0", "quill-mention": "^6.1.1", "react": "^19.0.0", "react-avatar-editor": "^13.0.2", "react-collapsed": "^4.1.2", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-google-places-autocomplete": "^4.1.0", "react-hot-toast": "^2.5.2", "react-infinite-scroll-component": "^6.1.0", "react-insta-stories": "^2.8.0", "react-intersection-observer": "^9.16.0", "react-linkedin-login-oauth2": "^2.0.1", "react-quilljs": "^2.0.5", "react-responsive-carousel": "^3.2.23", "react-responsive-modal": "^6.4.2", "socket.io-client": "^4.8.1", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1"}}