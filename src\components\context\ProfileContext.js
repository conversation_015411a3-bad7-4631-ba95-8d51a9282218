"use client";
import React, { createContext, useContext, useState, useEffect } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import { getMyProfile, getOneUser } from "@/app/action";

export const ProfileContext = createContext();

export const useProfile = () => {
    const context = useContext(ProfileContext);
    if (!context) {
        throw new Error("useProfile must be used within a Profile Page");
    }
    return context;
};

export const ProfileProvider = ({ children, initialData, userIdSlug }) => {
    const [profile, setProfile] = useState(initialData ?? {});
    const [error, setError] = useState(null);
    const api = useApiRequest(!initialData)


    useEffect(() => {
        // only fetch if no initial profile (client-side nav fallback)
        if (!initialData) {
            // console.log("inside the wrapper")
            const fetchData = async () => {

                api.sendRequest(userIdSlug ? getOneUser : getMyProfile, (res) => {
                    setProfile(res?.data)
                }, {
                    userIdSlug
                }, "",
                    (err) => {
                        setError(err)
                    })
            };

            fetchData();
        }
    }, [initialData]);

    return (
        <ProfileContext.Provider value={{ profile, setProfile, loading: api.isLoading, error }}>
            {children}
        </ProfileContext.Provider>
    );
};
