import { Skeleton } from "../ui/skeleton";

const StoriesSkeleton = ({ tabCount = 5, className }) => {
  return (
    <>
      <div className={`tw-flex  tw-items-center tw-gap-4 ${className} `}>
        {Array.from({ length: tabCount }, (_, i) => i + 1)?.map((ele) => (
          <div key={ele}>
            <Skeleton className={`tw-p-12  !tw-rounded-full `} />
            <Skeleton className={`tw-h-4 tw-px-2 tw-my-2`} />
          </div>
        ))}
      </div>
    </>
  );
};

export default StoriesSkeleton;
