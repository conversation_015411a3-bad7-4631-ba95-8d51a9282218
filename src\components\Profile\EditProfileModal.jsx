import { useFormik } from "formik";
import Modal from "react-responsive-modal";
import CustomButton from "../Common/Custom-Button";
import { EditProfileIcon } from "@/utils/icons";
import { useContext, useEffect, useState } from "react";
import { blurDataURL, generateAvatarCanvas } from "@/utils/function";
import Image from "next/image";
import { updateImageToURL, updateMyProfile } from "@/app/action";
import toast from "react-hot-toast";
import * as Yup from "yup";
import FloatingLabelInput from "../HomePage/Form/FloatingLabelInput";
import FloatingLabelTextArea from "../HomePage/Form/FloatingLabelTextArea";
import { valContext } from "../context/ValContext";
import axios from "axios";
import useApiRequest from "../helper/hook/useApiRequest";
import "react-responsive-modal/styles.css";
import { safeToast } from "@/utils/safeToast";
import GooglePlacesAutocomplete from "react-google-places-autocomplete";
import { ChevronDown, ChevronUp } from "lucide-react";

const EditProfileModal = ({
  open,
  setIsOpen,
  setRefresh,
  resetDataList,
  formData,
}) => {
  const [previewImage, setPreviewImage] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [location, setLocation] = useState(null);
  const [isAddMore, setIsAddMore] = useState(false);
  const { setUpdatedProfile } = useContext(valContext);
  const api = useApiRequest();

  // console.log(previewImage, imageFile);

  const resetModal = () => {
    setIsOpen(false);
    setIsAddMore(false);
    setImageFile(null);
    setPreviewImage(null);
  };

  const handleProfileImage = (e) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }
    // const fileSize = e.target.files[0].size / (1024 * 1024);
    // if (fileSize > 2) {
    //   safeToast.error("The file size exceeds 2 MB. Please upload a smaller file.");
    //   return;
    // }
    setImageFile(e.target.files[0]);

    if (e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        setPreviewImage(reader.result);
      };
      reader?.readAsDataURL(e.target.files[0]);
    }
  };

  const validationSchema = Yup.object().shape({
    // firstName: Yup.string().trim().required("First Name is required"),
    // lastName: Yup.string().trim().required("Last Name is required"),
    firstName: Yup.string()
      .trim()
      .required("First Name is required")
      .matches(
        /^(?![\d\W]+$)[a-zA-Z\d\W]+$/,
        "First Name cannot contain only numbers or special characters"
      ),
    lastName: Yup.string()
      .trim()
      .required("Last Name is required")
      .matches(
        /^(?![\d\W]+$)[a-zA-Z\d\W]+$/,
        "Last Name cannot contain only numbers or special characters"
      ),
    // about: Yup.string().trim().required("About is required"),
  });
  const formik = useFormik({
    enableReinitialize: true, // Allows form values to update when `open` changes
    initialValues: {
      firstName: "",
      lastName: "",
      about: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      // console.log("Updated Profile:", values);
      setIsLoading(true);
      let payload = {
        location: location && location?.label !== "" ? location?.label : null,
      };
      // values;
      Object.keys(values)?.forEach((ele) => {
        payload = {
          ...payload,
          [ele]: values[ele]?.trim() === "" ? null : values[ele]?.trim(),
        };
      });
      if (imageFile) {
        const formData = new FormData();
        formData.append("file", imageFile);

        try {
          const res = await updateImageToURL(formData);
          // setSelectedImage(res?.data?.[0]?.link);
          payload = {
            ...payload,
            image: res?.data?.[0]?.link,
          };
        } catch (error) {
          console.dir(error);
          return;
        }

        // uploadCoverImageHandler(payload);
      }
      // console.log("response", payload);

      api.sendRequest(
        updateMyProfile,
        (res) => {
          setRefresh((prev) => (typeof prev === "undefined" ? true : !prev));
          setUpdatedProfile(
            payload?.image ?? generateAvatarCanvas(payload?.firstName, 90)
          );
          safeToast.success(
            res?.message ?? "Your Profile Updated Successfully"
          );
          setImageFile(null);
          setPreviewImage(null);
          resetDataList();
          resetModal();
        },
        payload,
        ""
      );
    },
  });

  useEffect(() => {
    if (formData) {
      formik.setFieldValue("firstName", formData?.firstName);
      formik.setFieldValue("lastName", formData?.lastName);
      formik.setFieldValue("about", formData?.about);
      if (formData?.location) {
        setLocation({
          label: formData?.location,
          value: formData?.location,
        });
      }
    }
  }, [formData]);

  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-m-2 md:tw-m-[1.2rem] !tw-w-[40rem] !tw-rounded-[1.25rem] ",
          overlay: "!tw-bg-[#000000CC]",
        }}
        focusTrapped={false}
        open={open}
        onClose={resetModal} // Fix: Changed `onClose` to `onCancel` for Ant Design modals
      >
        <div className="tw-py-5 tw-mx-14">
          <h2 className="tw-text-xl tw-text-center tw-font-semibold">
            Edit Profile
          </h2>

          <form onSubmit={formik.handleSubmit} className="tw-mt-4">
            {/* Profile Picture */}
            <div className="tw-relative tw-flex tw-justify-center tw-mb-8">
              <div
                className={`tw-relative  tw-w-[8rem]  tw-h-[8rem] tw-rounded-full  ${
                  !formData?.image &&
                  !previewImage &&
                  "tw-flex tw-justify-center tw-items-center tw-bg-primary-purple tw-text-white"
                }`}
              >
                {formData?.image || previewImage ? (
                  <Image
                    src={previewImage ?? formData?.image}
                    className="!tw-rounded-full !tw-object-cover"
                    alt="profile"
                    placeholder="blur"
                    fill
                    blurDataURL={blurDataURL(300, 300)}
                  />
                ) : (
                  <span className="tw-text-primary-1100 tw-font-merriweather tw-font-medium tw-text-6xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                    {formData?.firstName?.charAt(0)}
                  </span>
                )}
              </div>
              <div className="tw-absolute -tw-bottom-2 tw-right-[38%]">
                <div className="tw-relative tw-overflow-hidden tw-cursor-pointer">
                  <div className="tw-bg-[#C6B4A3] tw-p-2 tw-rounded-full tw-cursor-pointer">
                    <EditProfileIcon size={25} />
                  </div>
                  <input
                    type="file"
                    // accept="image/*"
                    accept=".jpg, .jpeg, .png"
                    className="tw-absolute tw-top-[0.4rem] tw-cursor-pointer tw-scale-[1.25] tw-opacity-0"
                    onChange={(e) => {
                      handleProfileImage(e);
                    }}
                  />
                </div>
              </div>
            </div>
            {/* First Name */}

            <FloatingLabelInput
              label="First Name*"
              name="firstName"
              formik={formik}
            />
            {formik.touched.firstName && formik.errors.firstName && (
              <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                {formik.errors.firstName}
              </p>
            )}
            {/* Last Name */}

            <FloatingLabelInput
              label="Last Name*"
              name="lastName"
              formik={formik}
            />
            {formik.touched.lastName && formik.errors.lastName && (
              <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                {formik.errors.lastName}
              </p>
            )}
            {/* Email */}
            <div className="tw-mb-4 tw-bg-[#F1F2F3] tw-py-3 tw-px-5 tw-rounded-2xl !tw-cursor-not-allowed">
              <label className="tw-block tw-text-primary-black tw-text-xs tw-mb-1 !tw-cursor-not-allowed">
                Email
              </label>
              <input
                type="text"
                name="email"
                disabled
                value={formData?.email}
                className="tw-w-full !tw-cursor-not-allowed tw-text-primary-black tw-opacity-50 tw-bg-[#F1F2F3] tw-font-semibold  tw-outline-none"
              />
            </div>

            {/* About (Textarea) */}

            {/* About (Textarea) */}
            <div
              onClick={() => {
                setIsAddMore((prev) => !prev);
              }}
              className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center"
            >
              <p className="tw-text-primary-black tw-text-lg">
                Add More (Optional)
              </p>
              {!isAddMore ? <ChevronDown /> : <ChevronUp />}
            </div>

            <div
              className={`tw-overflow-hidden tw-transition-all tw-duration-300 ${
                isAddMore
                  ? "tw-max-h-[900px] tw-opacity-100"
                  : "tw-max-h-0 tw-opacity-0"
              }`}
            >
              {/* Location */}
              <div className="tw-mb-4">
                <div className="tw-relative  tw-bg-[#F1F2F3] tw-pt-5 tw-pb-3 tw-px-5 tw-rounded-2xl">
                  {/* Floating Label */}
                  <label
                    htmlFor="location"
                    className={`tw-absolute tw-left-5 tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none
          ${
            isFocused || location
              ? "tw-top-2 tw-text-xs tw-text-gray-500"
              : "tw-top-6 tw-text-lg"
          }`}
                  >
                    Location
                  </label>

                  <GooglePlacesAutocomplete
                    apiKey={process.env.NEXT_PUBLIC_GOOGLE_API_KEY}
                    selectProps={{
                      value: location,
                      onChange: (value) => setLocation(value),
                      onFocus: () => setIsFocused(true),
                      onBlur: () => setIsFocused(!!location),
                      placeholder: " ", // keep it blank for floating label trick
                      isClearable: true,
                      name: "location",
                      styles: {
                        control: (base, state) => ({
                          ...base,
                          backgroundColor: "transparent",
                          border: "none",
                          boxShadow: "none",
                          padding: "0",
                          minHeight: "2.5rem",
                          color: "#2D394A",
                          fontSize: "1.125rem", // Tailwind's text-lg
                          fontWeight: 600,
                        }),
                        singleValue: (base) => ({
                          ...base,
                          color: "#2D394A",
                        }),
                        placeholder: (base) => ({
                          ...base,
                          // color: "#BFBFC7",
                        }),
                        indicatorSeparator: () => ({}),
                        dropdownIndicator: () => ({
                          display: "none",
                        }),
                        menu: (base) => ({
                          ...base,
                          zIndex: 100,
                        }),
                      },
                    }}
                  />
                </div>
                {/* <p className="tw-italic tw-text-[#787E89] tw-font-light tw-text-sm tw-text-center">
                      Add a location to find specific project easily.
                    </p> */}
              </div>

              {/* Description */}
              <FloatingLabelTextArea
                label="About"
                name="about"
                formik={formik}
              />
              <div className="tw-flex tw-justify-end tw-mb-4">
                <p className="tw-text-sm tw-text-[#787E89] ">
                  {formik.values.about?.length ?? 0}/250
                </p>
              </div>
            </div>

            {/* Submit Button */}

            <div className="tw-flex tw-justify-center">
              <CustomButton
                loading={isLoading}
                className={"!tw-px-9 !tw-py-[14px]"}
                type="submit"
                count={8}
                onClick={() => {}}
              >
                <span className={"!tw-text-base"}>Update</span>
              </CustomButton>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
};

export default EditProfileModal;
