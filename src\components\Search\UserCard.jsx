"use client";
import { blurDataURL } from "@/utils/function";
import Image from "next/image";
import { useState } from "react";
import UserAvatar from "../Common/UserAvatar";

const UserCard = ({ ele, followAndUnfollowHandler }) => {
  const [isFollow, setIsFollow] = useState(Boolean(+ele?.isFollowed));
  return (
    <>
      <div className="tw-flex tw-items-center tw-gap-3 ">
        {/* <div
          className={`tw-relative tw-rounded-full tw-w-[3.25rem] tw-h-[3.25rem] ${
            !ele?.image && "tw-bg-primary-purple tw-text-white "
          }`}
        >
          {ele?.image ? (
            <Image
              src={ele?.image}
              alt="user"
              fill
              className="!tw-rounded-full !tw-object-cover"
              placeholder="blur"
              blurDataURL={blurDataURL(300, 300)}
            />
          ) : (
            <span className="tw-text-primary-1100  tw-font-merriweather tw-font-bold md:tw-text-2xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
              {ele?.firstName?.charAt(0)}
            </span>
          )}
        </div> */}
        <UserAvatar imageUrl={ele?.image} userName={ele?.firstName} />
        <div className="tw-flex tw-flex-col tw-items-start">
          <p className="tw-font-bold tw-text-lg tw-not-italic !tw-text-primary-black">
            {`${ele?.firstName ?? ""} ${ele?.lastName ?? ""}`}
          </p>
          {/* <p className="tw-text-sm  tw-max-w-[15.5rem] tw-break-words tw-not-italic !tw-text-[#787E89]">
            {ele?.email ?? ""}
          </p> */}
        </div>
      </div>
      <div>
        <button
          type="button"
          className={`tw-py-2 tw-px-4 tw-rounded-full ${
            !isFollow
              ? "tw-border tw-border-primary-purple tw-text-primary-purple tw-bg-transparent"
              : "tw-border tw-bg-transparent tw-border-[#D9D9D9] tw-text-[#787E89]"
          } tw-font-semibold`}
          onClick={(e) => {
            e.stopPropagation();
            followAndUnfollowHandler(ele?.id, !isFollow);
            setIsFollow((prev) => !prev);
          }}
        >
          {!isFollow ? "Follow" : "Unfollow"}
        </button>
      </div>
    </>
  );
};

export default UserCard;
