"use client";
import { useState, useEffect } from "react";

// Utility functions (you can import or define them here)
const isJsonString = (str) => {
    try {
        JSON.parse(str);
        return true;
    } catch (e) {
        return false;
    }
};

const validValue = (value) => value !== undefined && value !== null;

// Custom hook to use sessionStorage
const useSessionStorage = (key, initialValue = "") => {
    // Get data from sessionStorage or use initial value
    const storedValue =
        typeof window !== "undefined"
            ? sessionStorage.getItem(key)
            : null;

    const initial =
        storedValue !== null && validValue(storedValue)
            ? isJsonString(storedValue)
                ? JSON.parse(storedValue)
                : `${storedValue}`
            : initialValue;

    // State to hold the current value
    const [value, setValue] = useState(initial);

    // Update sessionStorage and state when the value changes
    const setStoredValue = (newValue) => {
        setValue(newValue);
        if (typeof window !== "undefined") {
            sessionStorage.setItem(key, JSON.stringify(newValue));
        }
    };

    // Listen for changes in other tabs/windows
    useEffect(() => {
        const handleStorageChange = (event) => {
            if (event.key === key) {
                const newValue =
                    event.newValue !== null && validValue(event.newValue)
                        ? JSON.parse(event.newValue)
                        : null;
                setValue(newValue);
            }
        };

        window.addEventListener("storage", handleStorageChange);

        return () => {
            window.removeEventListener("storage", handleStorageChange);
        };
    }, [key]);

    return [value, setStoredValue];
};

export default useSessionStorage;
