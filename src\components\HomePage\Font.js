// app/components/fonts.js
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Open_Sans,
    Montserrat,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Ubuntu,
    <PERSON>fair_Display,
    Quicksand,
    <PERSON>_Script,
    <PERSON>egrey<PERSON>,
    <PERSON>612,
    <PERSON><PERSON>a,
    <PERSON><PERSON><PERSON>,
    Rakkas,
    Neonderthaw,
    Sacramento,
    UnifrakturMaguntia,
} from 'next/font/google';

// ✅ Step 1: Call loaders at top-level
const roboto = Roboto({ subsets: ['latin'], weight: ['400'] });
const lato = Lato({ subsets: ['latin'], weight: ['400'] });
const openSans = Open_Sans({ subsets: ['latin'], weight: ['400'] });
const montserrat = Montserrat({ subsets: ['latin'], weight: ['400'] });
const poppins = Poppins({ subsets: ['latin'], weight: ['400'] });
const raleway = Raleway({ subsets: ['latin'], weight: ['400'] });
const ubuntu = Ubuntu({ subsets: ['latin'], weight: ['400'] });
const playfairDisplay = Playfair_Display({ subsets: ['latin'], weight: ['400'] });
const quicksand = Quicksand({ subsets: ['latin'], weight: ['400'] });
const dancingScript = Dancing_Script({ subsets: ['latin'], weight: ['400'] });
const alegreya = Alegreya({ subsets: ['latin'], weight: ['400'] });
const b612 = B612({ subsets: ['latin'], weight: ['400'] });
const varela = Varela({ subsets: ['latin'], weight: ['400'] });
const vollkorn = Vollkorn({ subsets: ['latin'], weight: ['400'] });
const rakkas = Rakkas({ subsets: ['latin'], weight: ['400'] });
const neonderthaw = Neonderthaw({ subsets: ['latin'], weight: ['400'] });
const sacramento = Sacramento({ subsets: ['latin'], weight: ['400'] });
const unifrakturMaguntia = UnifrakturMaguntia({ subsets: ['latin'], weight: ['400'] });

// ✅ Step 2: Export fontMap with pre-loaded constants
export const fontMap = {
    'Roboto': roboto,
    'Lato': lato,
    'Open Sans': openSans,
    'Montserrat': montserrat,
    'Poppins': poppins,
    'Raleway': raleway,
    'Ubuntu': ubuntu,
    'Playfair Display': playfairDisplay,
    'Quicksand': quicksand,
    'Dancing Script': dancingScript,
    'Alegreya': alegreya,
    'B612': b612,
    'Varela': varela,
    'Vollkorn': vollkorn,
    'Rakkas': rakkas,
    'Neonderthaw': neonderthaw,
    'Sacramento': sacramento,
    'UnifrakturMaguntia': unifrakturMaguntia,
};
