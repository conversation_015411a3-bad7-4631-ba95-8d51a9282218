import { getMyProfile } from "@/app/action";
import ProfileWrapper from "@/components/Profile/ProfileWrapper";

const ProfileLayout = async ({ children, tabs }) => {
    const profileData = await getMyProfile()
    // console.log(profileData?.data, ",------------ Profile")
    return (
        <>
            <ProfileWrapper tabs={tabs} userIdSlug={null} profileData={profileData} />
        </>
    );
}

export default ProfileLayout;