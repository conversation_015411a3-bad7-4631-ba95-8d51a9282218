import setCookie, { getCookie, eraseCookie } from '../Cookies';

class AuthStorage {
  getAuthToken = () => {
    return getCookie('SAID');
  };
  getProfileDetails = () => {
    const profileString = getCookie('PROFILE');
    try {
      return profileString ? JSON.parse(profileString) : null;
    } catch (error) {
      console.error('Error parsing profile details:', error);
      return null;
    }
  };
  getPrvRoute = () => {
    return getCookie('PRV-ROUTE');
  };
  setAuthDetails = (accessToken) => {
    setCookie('SAID', accessToken, 365);
    localStorage.setItem('token', accessToken);
  };

  setProfileDetails = (profile) => {
    setCookie('PROFILE', JSON.stringify(profile), 365);
    localStorage.setItem('PROFILE', JSON.stringify(profile));
  };
  setPrvRoute = (route) => {
    setCookie('PRV-ROUTE', JSON.stringify(route), 365);
    // localStorage.setItem('PRV-ROUTE', JSON.stringify(route));
  };
  setActiveToolTab = (tab) => {
    setCookie('activeTab', tab, 365);
    // localStorage.setItem('PRV-ROUTE', JSON.stringify(route));
  };
  deleteActiveToolTab = () => eraseCookie('activeTab');
  deleteAuthDetails = () => {
    eraseCookie('SAID');
    eraseCookie('UID');
    eraseCookie('PROFILE');
    localStorage.clear();
    sessionStorage.clear()
  };
}
const authStorage = new AuthStorage();

export default authStorage;
