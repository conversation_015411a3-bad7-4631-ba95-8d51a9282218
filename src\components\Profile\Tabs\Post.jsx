"use client";
import {
  editPost,
  getAllPost,
  getCommentList,
  pinPost,
  updatePostLike,
  updatePostWishList,
} from "@/app/action";
import { CustomGrid } from "@/components/Common/Custom-Display";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import { useProfile } from "@/components/context/ProfileContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import FormModal from "@/components/HomePage/FormModal";
import PostCard from "@/components/HomePage/PostCard";
import ExplorePostSkeleton from "@/components/Loader/ExplorePostSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { RESPONSE_STATUS } from "@/utils/function";
import { EditPencilIcon } from "@/utils/icons";
import { safeToast } from "@/utils/safeToast";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

const Post = ({ origin }) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [userData, setUserData] = useState(null);
  const [loadOff, setLoadOff] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { profile, error } = useProfile();
  const api = useApiRequest(profile?.id ? true : false);
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Move Handler
  const onMoveHandler = async (project, postData) => {
    let payload = {
      ProjectId: project?.id,
    };

    try {
      const response = await editPost({
        ...payload,
        id: postData?.id,
      });

      if (
        response?.status !== 200 &&
        response?.status !== RESPONSE_STATUS.SUCCESS
      ) {
        throw response;
      }
      safeToast.success("Post Move Successfully to this Project");
      setRefresh((prev) => !prev);
      resetDataList();
    } catch (error) {
      safeToast.error(error?.message);
      return;
    }
  };

  // Like Handler
  const updateLike = async (id) => {
    const res = await updatePostLike(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };
  // Wishlist Handler
  const updateWishList = async (id) => {
    const res = await updatePostWishList(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  // PinPost handler

  const pinPostHandler = async (id, setState) => {
    try {
      const response = await pinPost(id);

      if (
        response?.status !== 200 &&
        response?.status !== RESPONSE_STATUS.SUCCESS
      ) {
        throw response;
      }
      // safeToast.success(response?.message);
      resetDataList();
      setRefresh((prev) => !prev);
    } catch (error) {
      safeToast.error(error?.message);
      return;
    }
  };

  // Get Data
  const delayedSearch = async (userId) => {
    let query = {
      page: pagination.page,
      limit: pagination.limit,
      UserId: +userId,
    };

    const user = authStorage.getProfileDetails();
    setUserData(user);
    if (Object.keys(query)?.length > 0) {
      query.sortOrder = "DESC";
    }

    try {
      api.sendRequest(
        getAllPost,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [...prev, ...res?.data?.data]);
            setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setDataList(() => [...res?.data?.data]);
            setLoadOff(false);
          } else {
            setLoadOff(true);
          }
        },

        { ...query }
      );
    } catch (error) {
      console.error("Error fetching blog data:", error);
      // setScrollLoader(false);
    } finally {
      // setScrollLoader(false);
    }
    // setScrollLoader(false);
  };

  useEffect(() => {
    const fetchData = async (id) => {
      await delayedSearch(id);
    };

    if (profile?.id) {
      fetchData(profile?.id);
    }
  }, [pagination.page, profile, refresh]);
  return (
    <div>
      <FormModal
        setRefresh={setRefresh}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        isInHomePage={false}
      />
      {/* Create Button */}
      {+profile?.id === +userData?.id && (
        <button
          type="button"
          onClick={() => setIsOpen((prev) => !prev)}
          className="tw-fixed lg:tw-absolute tw-bottom-[8.5rem] tw-right-4 tw-z-50 lg:tw-z-0  lg:tw-bottom-auto lg:-tw-top-[5.8rem]  lg:tw-right-[6rem] tw-bg-primary-purple tw-border-primary-purple tw-border tw-outline-none tw-rounded-[3.125rem] tw-text-white tw-p-[1.1rem] lg:!tw-px-5 !lg:tw-py-[1.1rem] !tw-flex !tw-items-center !tw-gap-1.5 !tw-font-semibold tw-group tw-transition-transform tw-duration-300 hover:tw-scale-105"
        >
          <EditPencilIcon className="tw-h-[20px] tw-w-[20px] tw-font-semibold" />
          <span className="tw-hidden lg:tw-inline-block">Create</span>
        </button>
      )}
      <>
        <CustomGrid
          data={dataList}
          className="tw-gap-4 sm:tw-gap-8 tw-pb-16 lg:tw-pb-5"
          Component={({ data: dataProps }) => {
            return PostCard({
              data: { ...dataProps },
              userData: userData,
              setRefresh: setRefresh,
              origin: origin,
              refetchData: () => {
                resetDataList();
              },
              getLikeData: async (id) => {
                updateLike(id);
              },
              getWishlistData: async (id) => {
                updateWishList(id);
              },
              getCommentData: async (
                id,
                setCommentList,
                setIsCommentLoading
              ) => {
                try {
                  const response = await getCommentList(id);
                  if (response?.status === RESPONSE_STATUS.SUCCESS) {
                    setCommentList(response?.data?.data);
                  } else {
                    throw response;
                  }
                } catch (error) {
                  safeToast.error(error?.message);
                } finally {
                  setIsCommentLoading(false);
                }
              },
              pinPostHandler: async (id, setState) => {
                pinPostHandler(id, setState);
              },
              onMoveHandler: onMoveHandler,
            });
          }}
          xs={1}
          sm={1}
          md={1}
          lg={1}
          xl={1}
        />
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            if (
              Math.ceil(pagination.total / pagination.limit) > pagination.page
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<ExplorePostSkeleton count={2} />}
          timeout={10}
          loadOff={loadOff}
        />
      </>
      {!api.isLoading && dataList?.length === 0 && (
        <div className="tw-flex tw-h-[50dvh] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Post available!" />
        </div>
      )}
    </div>
  );
};

export default Post;
