"use client";

import { ProfileProvider } from "../context/ProfileContext";
import ProfilePage from "./ProfilePage";

const ProfileWrapper = ({
  tabs,
  profileData,
  userIdSlug = null,
  profileTabs,
}) => {
  return (
    <>
      <ProfileProvider
        userIdSlug={userIdSlug}
        initialData={profileData?.data ?? null}
      >
        <ProfilePage
          profileTabs={profileTabs}
          userIdSlug={userIdSlug}
          data={profileData}
        />

        {profileData?.data && (
          <div className="tw-px-5 md:tw-px-24 tw-relative  4xl:tw-px-48">
            {tabs}
          </div>
        )}
      </ProfileProvider>
    </>
  );
};

export default ProfileWrapper;
