"use client";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "../ui/button";
import { Field, Form, Formik, ErrorMessage } from "formik";
import * as Yup from "yup";
import CustomButton from "../Common/Custom-Button";
import Image from "next/image";
import {
  convertUTCtoLocal,
  ReplaceBackslashN,
  RESPONSE_STATUS,
  routes,
} from "@/utils/function";
import Loader from "../Loader/Loader";
import { comment } from "postcss";
import UserAvatar from "../Common/UserAvatar";
import { Trash2 } from "lucide-react";
import ShowMenuList from "../Common/ShowMenuList";
import { ThreeDotMenuIcon } from "@/utils/icons";
import { deleteComments } from "@/app/action";
import { safeToast } from "@/utils/safeToast";
import { useProgress } from "@bprogress/next";
// import { useR<PERSON>er } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";

const CommentDrawer = ({
  isOpen = false,
  setIsOpen,
  onCommentSubmit = () => {},
  isLoading = false,
  isCommentLoading = false,
  commentList = [],
  setCommentList,
  setPostState,
  postData,
  loginUserData,
}) => {
  const settings = [
    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: async (ele) => {
        // console.log(ele);
        try {
          const response = await deleteComments({
            id: ele?.id,
          });
          if (
            response?.status !== 200 &&
            response?.status !== RESPONSE_STATUS.SUCCESS
          ) {
            throw response;
          }
          setCommentList((prev) =>
            prev?.filter((item) => item?.id !== ele?.id)
          );
          setPostState((prev) => ({
            ...prev,
            commentCount: prev?.commentCount - 1,
          }));
          safeToast.success(response?.message);
        } catch (error) {
          // console.log(error);
          safeToast.error(error?.message);
        }
      },
    },
  ];
  const progress = useProgress();
  const router = useRouter();
  // console.log(commentList);
  return (
    <Sheet
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) {
          setCommentList([]); // Reset comments when closing
        }
      }}
    >
      <SheetContent className="!tw-overflow-auto">
        <SheetHeader className="tw-mt-8">
          <SheetTitle className="!tw-text-2xl !tw-text-primary-black">
            Comments
          </SheetTitle>
          <Formik
            initialValues={{ comment: "" }}
            validationSchema={Yup.object({
              comment: Yup.string()?.trim().required("Comment is required"),
            })}
            onSubmit={(values, { resetForm }) => {
              onCommentSubmit(
                {
                  comment: values?.comment?.trim()?.toString(),
                },
                resetForm
              );
              // resetForm();
              // setIsOpen(false);
            }}
          >
            {({ handleSubmit }) => (
              <Form onSubmit={handleSubmit}>
                <Field
                  as="textarea"
                  name="comment"
                  id="comment"
                  placeholder="Write a comment"
                  rows={6} // Set the number of rows here
                  className="tw-w-full tw-text-sm tw-bg-[#F1F2F3] tw-rounded-[14px] tw-py-4 tw-ps-5 tw-pe-10 tw-outline-none tw-text-primary-black"
                />
                {/* Error message display */}
                <ErrorMessage
                  name="comment"
                  component="div"
                  className="tw-text-red-500 tw-text-sm tw-mt-1"
                />
                <SheetFooter className="tw-mt-4 !tw-inline-block tw-float-right  lg:tw-flex ">
                  {/* <Button type="submit" className="tw-w-full">
                    Submit
                  </Button> */}
                  <CustomButton
                    className={"!tw-px-6"}
                    count={8}
                    loading={isLoading}
                    type="submit"
                  >
                    Post a Comment
                  </CustomButton>
                </SheetFooter>
              </Form>
            )}
          </Formik>
        </SheetHeader>
        {isCommentLoading ? (
          <div className="tw-my-6 tw-flex tw-justify-center tw-items-center">
            <Loader />
          </div>
        ) : (
          <div className="tw-pt-6 tw-h-[18rem] md:tw-h-[22rem] tw-overflow-auto">
            {commentList?.map((ele) => (
              <div
                key={ele?.id}
                className="tw-flex tw-justify-between tw-items-start tw-mb-4"
              >
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    progress.start();
                    router.push(
                      postData?.User?.id === loginUserData?.id
                        ? routes.PROFILE
                        : routes.USER.replace(":slug", ele?.User?.slug)
                    );
                  }}
                  className="tw-cursor-pointer tw-flex tw-gap-2 "
                >
                  {/* <div className="tw-relative md:tw-ws-[3.125rem]  md:tw-hs-[3.125rem] tw-w-[2.5rem] tw-h-[2.5rem] tw-bg-primary-purple tw-text-white tw-rounded-full tw-overflow-hidden tw-cursor-pointer">
                  {ele?.User?.image ? (
                    <Image
                      src={
                        ele?.User?.image ?? "https://i.pravatar.cc/100?img=5"
                      }
                      alt="User"
                      fill
                      className="tw-object-cover tw-rounded-full"
                    />
                  ) : (
                    <span className="tw-text-primary-1100 tw-font-merriweather tw-font-bold md:tw-text-3xl tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                      {ele?.User?.firstName?.charAt(0)}
                    </span>
                  )}
                </div> */}
                  <UserAvatar
                    imageUrl={ele?.User?.image}
                    userName={ele?.User?.firstName}
                  />
                  <div>
                    <div className="tw-text-base tw-max-w-[22rem]">
                      <strong>
                        {ele?.User?.firstName} {ele?.User?.lastName}
                      </strong>
                      &nbsp;
                      <span
                        dangerouslySetInnerHTML={{
                          __html: ReplaceBackslashN(ele?.comment),
                        }}
                      />
                      {/* {ele?.comment} */}
                    </div>
                    <p className="tw-text-[#787E89] tw-text-sm">
                      {convertUTCtoLocal(ele?.createdAt, "MMM DD, YYYY")}
                    </p>
                  </div>
                </div>
                {(loginUserData?.id === ele?.User?.id ||
                  postData?.User?.id === loginUserData?.id) && (
                  <ShowMenuList
                    extraClassName="tw-z-[99999]"
                    data={ele}
                    menuList={settings}
                  >
                    <button
                      type="button"
                      className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center  tw-right-7 tw-top-[1rem]"
                    >
                      <div className="">
                        <ThreeDotMenuIcon fill="#111" size={24} />
                      </div>
                    </button>
                  </ShowMenuList>
                )}
              </div>
            ))}
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};

export default CommentDrawer;
