import { getProject } from "@/app/action";
import Empty from "@/components/Common/Empty";
import SearchBar from "@/components/Common/SearchBar";
import { valContext } from "@/components/context/ValContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import { useDebouncedSearch } from "@/components/helper/hook/useDebouncedSearch";
import GlobalLoader from "@/components/Loader/GlobalLoader";
import SelectProjectSkeleton from "@/components/Loader/SelectProjectSkeleton";
import { Skeleton } from "@/components/ui/skeleton";
import authStorage from "@/utils/API/AuthStorage";
import { CloseModalIcon, LeftArrowBackIcon } from "@/utils/icons";
import { SearchIcon } from "lucide-react";
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const ModalProjects = ({
  isOpen,
  setSelectedMenu,
  selectedProjects,
  setSelectedProjects,
  isMultiple = true,
}) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [tempSelected, setTempSelected] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [search, setSearch] = useState("");
  const { loginUserData } = useContext(valContext);
  const debounceRef = useRef(null);
  const api = useApiRequest();

  // const onSearch = (value) => {
  //   setSearch(value);
  //   if (debounceRef.current) {
  //     clearTimeout(debounceRef.current);
  //   }
  //   debounceRef.current = setTimeout(() => {
  //     resetData();
  //     setSearchQuery(value);
  //   }, 500);
  // };

  // console.log(dataList, "dataList");

  const resetData = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const onSearch = useDebouncedSearch({
    setSearchQuery, // required
    resetData, // optional
    setSearch, // optional
  });

  const handleSelect = (value) => {
    setTempSelected((prev) => {
      if (prev?.find((ele) => ele?.id === value?.id)) {
        return isMultiple ? prev?.filter((item) => item?.id !== value?.id) : [];
      } else if (isMultiple) {
        return [...prev, value];
      } else {
        return [value];
      }
    });
  };

  // Fetch Projects
  const fetchProjects = useCallback(async () => {
    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
    };

    const userDetails = authStorage.getProfileDetails();
    if (Object.keys(userDetails)?.length > 0) {
      queryParams = { ...queryParams, UserId: userDetails?.id };
    }

    if (searchQuery) {
      queryParams = {
        ...queryParams,
        searchQuery,
      };
    }
    api.sendRequest(
      getProject,
      (res) => {
        const dataItems = res?.data?.data?.filter(
          (ele) =>
            ele?.UserId === loginUserData?.id ||
            ele?.ProjectMembers?.[0]?.access === "write"
        );
        if (pagination.page === 1) {
          setDataList(dataItems);
        } else {
          setDataList((prev) => [...prev, ...dataItems]);
        }
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.totalRecords,
        }));
      },

      queryParams
    );
  }, [pagination.page, searchQuery]);

  useEffect(() => {
    setTempSelected(selectedProjects ?? []);
  }, [selectedProjects]);

  useEffect(() => {
    fetchProjects();
  }, [pagination.page, searchQuery]);

  useEffect(() => {
    resetData();
  }, [isOpen]);
  return (
    <>
      <div className="tw-flex tw-justify-between tw-items-center">
        <button
          type="button"
          onClick={() => {
            setSelectedMenu(null);
          }}
          className=""
        >
          <LeftArrowBackIcon />
        </button>
        <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
          Projects
        </p>
        <button
          type="button"
          className="tw-text-primary-purple tw-font-bold"
          onClick={() => {
            setSelectedProjects(tempSelected);
            setSelectedMenu(null);
          }}
        >
          Done
        </button>
      </div>

      <div className="tw-my-2">
        {/* <div className="tw-flex tw-mt-4 tw-gap-2 tw-items-center tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8]">
          <SearchIcon color="#787E89" />
          <input
            type="text"
            id="simple-search"
            className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500 `}
            disabled={dataList?.length === 0}
            autoComplete="off"
            placeholder="Search"
            value={search}
            onChange={(e) => {
              onSearch(e.target.value);
            }}
          />
          {search && (
            <button
              onClick={() => {
                setSearch("");
                setSearchQuery("");
                resetData();
              }}
              className="tw-ml-2"
            >
              <CloseModalIcon size={16} />
            </button>
          )}
        </div> */}
        <SearchBar
          search={search}
          setSearch={setSearch}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          onSearch={onSearch}
          resetData={resetData}
        />
        {api.isLoading && pagination.page === 1 && <SelectProjectSkeleton />}
        {dataList?.length > 0 && (
          <InfiniteScroll
            height={405}
            dataLength={dataList?.length ?? 0}
            // show-scrollbar css class to show the scroll bar
            className="infinite-scrollbar tw-px-1"
            hasMore={
              Math.ceil(pagination.total / pagination.limit) > pagination.page
            }
            next={() =>
              setPagination((prev) => ({
                ...prev,
                page: prev.page + 1,
              }))
            }
            loader={<SelectProjectSkeleton count={3} />}
          >
            {dataList.map((ele) => (
              <button
                type="button"
                className="tw-my-5 tw-w-full tw-flex tw-justify-between tw-items-center"
                key={ele.id}
                onClick={() => handleSelect(ele)}
              >
                <p
                  className={`tw-text-xl tw-text-primary-black tw-font-medium ${
                    tempSelected?.find((data) => data?.id === ele.id) &&
                    "!tw-text-primary-purple"
                  }`}
                >
                  {ele.name}
                </p>
                <div>
                  <input
                    className="tw-scale-150"
                    type="checkbox"
                    // checked={tempSelected.includes(ele.id)}
                    checked={
                      !!tempSelected?.find((data) => data?.id === ele?.id)
                    }
                    readOnly
                  />
                </div>
              </button>
            ))}
          </InfiniteScroll>
        )}
        {!api.isLoading && dataList?.length === 0 && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[20rem]">
            <Empty
              iconRequired={false}
              label={"No Project"}
              subLabel={"No project yet!"}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default ModalProjects;
