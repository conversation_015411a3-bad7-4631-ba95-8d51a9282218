"use client";
import React from "react";
import Image from "next/image";
const PostDescription = ({ description, coverImage }) => {
  // Example usage:

  return (
    <div className="tw-w-full lg:tw-w-[87%]">
      {coverImage?.[0]?.isCustom && (
        <div className="tw-my-3  tw-flex tw-justify-center">
          <Image
            src={coverImage?.[0]?.link}
            alt={"Cover Image"}
            className="tw-rounded-[1.5rem] !tw-relative tw-w-[28rem] lg:!tw-w-[50rem]"
            fill
          />
        </div>
      )}
      <div id="" className="ql-editor tw-w-full tw-mb-40 lg:tw-mb-0 !tw-p-0">
        <div
          className=" !tw-relative project-description "
          dangerouslySetInnerHTML={{
            __html: description,
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(PostDescription);
