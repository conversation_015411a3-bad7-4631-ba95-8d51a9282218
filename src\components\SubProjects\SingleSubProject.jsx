"use client";
import { useEffect, useState } from "react";
import {
  apiGenerator,
  blurDataURL,
  otherProjectSetting,
  stories,
} from "@/utils/function";
import authStorage from "@/utils/API/AuthStorage";
import { CustomContainer } from "../Common/Custom-Display";
import SingleProjectSkeleton from "../Loader/SingleProjectSkeleton";
import Link from "next/link";
import {
  AddFileIcon,
  AddTeamIcon,
  DotMenuIcon,
  Edit2Icon,
  EditProfileIcon,
  LeftArrowBackIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import { ChevronRight, Lock, Trash2 } from "lucide-react";
import AboutModal from "../ProjectPage/SingleProject/AboutModal";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "next/image";
import Post from "../ProjectPage/SingleProject/Tabs/Post";
import Todos from "../ProjectPage/SingleProject/Tabs/Todos";
import Team from "../ProjectPage/SingleProject/Tabs/Team";
import Drafts from "../ProjectPage/SingleProject/Tabs/Drafts";
import Empty from "../Common/Empty";
import { useRouter } from "nextjs-toploader/app";
import PopUpModal from "../Common/PopUpModal";
import SubProjectModal from "../HomePage/Form/SubProjectModal";
import { deleteProject, getOneProject, updateProject } from "@/app/action";
import ShowMenuList from "../Common/ShowMenuList";
import useApiRequest from "../helper/hook/useApiRequest";
import PostModal from "../HomePage/Form/PostModal";
import TodoModal from "../Todo/TodoModal";
import FolderModal from "../ProjectPage/SingleProject/Tabs/FilesComponent/FolderModal";
import Files from "../ProjectPage/SingleProject/Tabs/Files";

const SingleSubProject = ({ subProjectId }) => {
  const [subProjectData, setSubProjectData] = useState(null);
  const [activeTab, setActiveTab] = useState("post");
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [userData, setUserData] = useState(null);
  const [isFileOpen, setIsFileOpen] = useState(false);
  const [isAboutModalOpen, setIsAboutModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [deleteProjectData, setDeleteProjectData] = useState(null);
  const [refresh, setRefresh] = useState(false);
  const [isAddTodo, setIsAddTodo] = useState(false);
  const [hideProject, setHideProject] = useState(null);
  const [openModal, setOpenModal] = useState({
    isOpen: false,
    type: null,
  });
  const api = useApiRequest();
  const subProjectAPI = useApiRequest(false);
  const router = useRouter();

  // console.log(subProjectData);

  // Reset DataList
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  const myProjectSetting = [
    {
      label: "Edit",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        setEditData(ele);
      },
    },
    {
      label: `${!subProjectData?.hide ? "Hide Private" : "Undo Hide"} Project`,
      className: "",
      icon: <Lock size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        // setIsHideProject(true);
        setHideProject(ele);
      },
    },
    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        setDeleteProjectData(ele);
      },
    },
  ];

  // Tabs List
  const subProjectsTabs = [
    {
      label: "Posts",
      value: "post",
    },

    {
      label: "To Do",
      value: "todos",
    },
    {
      label: "Team",
      value: "team",
    },
    {
      label: "Drafts",
      value: "drafts",
    },
    {
      label: "Files",
      value: "files",
    },
  ];

  const buttonRender = {
    post: {
      icon: <Edit2Icon size={22} />,
      label: "Create",
      onClick: () => {
        setOpenModal({
          isOpen: true,
          type: "post",
        });
      },
    },
    drafts: {
      icon: <Edit2Icon size={22} />,
      label: "Create",
      onClick: () => {
        setOpenModal({
          isOpen: true,
          type: "post",
        });
      },
    },
    team: {
      icon: <AddTeamIcon size={18} className="" />,
      label: "Add",
      onClick: () => {},
    },
    todos: {
      icon: <DotMenuIcon size={20} className="" />,
      label: "Create",
      onClick: () => {
        setIsAddTodo(true);
      },
    },
    files: {
      icon: <AddFileIcon size={20} className="" />,
      label: "Create",
      onClick: () => {
        setIsFileOpen(true);
      },
    },
  };

  useEffect(() => {
    api.sendRequest(
      getOneProject,
      (res) => {
        const currentUserData = authStorage.getProfileDetails();
        setUserData(currentUserData);
        if (res?.data) {
          setSubProjectData({
            ...res?.data,
            createdBy:
              +currentUserData?.id === +res?.data?.UserId ? "me" : "User",
          });
        }
      },
      {
        id: subProjectId,
      }
    );
  }, [refresh]);
  return (
    <>
      {/* Folder Modal */}
      {/* <FolderModal
        isOpen={isFileOpen}
        setIsOpen={setIsFileOpen}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
        defaultData={subProjectData}
      /> */}
      {/* Post Modal */}
      {/* <PostModal
        reFetchData={resetDataList}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        formData={{
          ...subProjectData?.ParentProject,
          id: subProjectData?.id,
        }}
      /> */}
      {/* Sub-Project hide/unhide Confirm Popup */}
      <PopUpModal
        isLoading={subProjectAPI.isLoading}
        isOpen={hideProject}
        setIsOpen={setHideProject}
        icon={null}
        mainMessage={`${!subProjectData?.hide ? "Hide" : "Undo Hide"} Project`}
        subMessage={`Are you sure you want to ${
          !subProjectData?.hide ? "hide" : "Unhide"
        } your Project?`}
        onConfirm={() => {
          const payload = {
            id: hideProject?.id,
            hide: !hideProject?.hide,
          };
          subProjectAPI.sendRequest(
            updateProject,
            (res) => {
              // console.log(res);
              setRefresh((prev) => !prev);
              setHideProject(null);
            },
            payload
          );
        }}
      />

      {/* Todo Modal */}
      {/* <TodoModal
        isOpen={isAddTodo}
        setIsOpen={setIsAddTodo}
        setRefresh={setRefresh}
        reFetchData={resetDataList}
        defaultData={{
          subProjectId: subProjectData?.id,
        }}
      /> */}
      {/* Edit Sub-project Modal */}
      <SubProjectModal
        isOnlyEditSubProject
        editData={editData}
        setEditData={setEditData}
        editProjectData={editData}
        setRefresh={setRefresh}
        modalTitle={"Edit sub-project"}
        modalSubmitButton={"Update"}
      />
      {/* Delete Popup */}
      <PopUpModal
        isLoading={subProjectAPI.isLoading}
        isOpen={deleteProjectData}
        setIsOpen={setDeleteProjectData}
        mainMessage="Delete Sub-Project"
        subMessage="Are you sure you want to Delete Sub-Project Permanently?"
        onConfirm={() => {
          subProjectAPI.sendRequest(
            deleteProject,
            () => {
              // router.push(`/projects/${subProjectData?.ParentId}`);
              router.back();
            },
            {
              id: deleteProjectData?.id,
            },
            "Delete Sub-Project Successfully"
          );
        }}
      />
      {api.isLoading ? (
        <SingleProjectSkeleton />
      ) : subProjectData ? (
        <>
          <CustomContainer className="tw-py-4 tw-h-full">
            <AboutModal
              isOpen={isAboutModalOpen}
              setIsOpen={setIsAboutModalOpen}
              projectData={subProjectData}
            />
            {/* Back button */}
            <button
              type="button"
              onClick={() => {
                router.back();
              }}
              className="tw-mb-4"
            >
              <LeftArrowBackIcon />
            </button>
            {/* Project Image */}
            {
              <div className="tw-relative tw-w-full tw-h-[21rem] ">
                <Image
                  src={
                    subProjectData?.image ??
                    "https://s3-alpha-sig.figma.com/img/ae01/6fc3/0c7f4914bd4bb5256ed17debd1ce787e?Expires=1744588800&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=I~jgqIDcwwdcZyU8YWPu7UKt8gly4XOjQEVTyxHRAP1mBUvf8LdVEEG3NIgOmyGaYeCLGXAjO3dDD~2ETdryTAfhmbnWFnUfBpTEA0aqubPp~5PppUgxAscylh77aHu956geVLBtPQHR~nLyQAJ5YPlqFqlSSsecOpaVKM7e2OvnEtZIzVdHGJEeBeZ8UjDQV2Fh5CSnFqwxc7JngsSSW3m4kKjUbWUND3Oh4sgVtVg8tZf-Ye9tAD32hEAEBnS63vSvBksy66S9r7wyj0RaD9svS4n9Cv3UdoN2xG5Ca91HwrAh2A5ZulTvz4bLij4FpmmzvVs0lOUrq0YOg7Qf-Q__"
                  }
                  alt={"project logo"}
                  fill
                  className="tw-rounded-3xl tw-object-cover"
                  placeholder="blur"
                  priority
                  blurDataURL={blurDataURL(800, 400)}
                />

                <div className="tw-absolute tw-inset-0 tw-rounded-3xl tw-bg-projectCoverImgBg" />
                <ShowMenuList
                  data={subProjectData}
                  menuList={
                    subProjectData?.createdBy !== "me"
                      ? otherProjectSetting
                      : myProjectSetting?.filter((data) =>
                          subProjectData?.isPrivate
                            ? data
                            : data?.label === "Edit" || data?.label === "Delete"
                        )
                  }
                >
                  <div className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-7">
                    <ThreeDotMenuIcon fill="#fff" size={24} />
                  </div>
                </ShowMenuList>
              </div>
            }
            {/* Title */}
            <div className="tw-flex tw-justify-between tw-items-center tw-mt-4">
              <button
                onClick={() => setIsAboutModalOpen((prev) => !prev)}
                className="tw-flex tw-gap-2 tw-items-center"
              >
                <h1 className="tw-my-5 tw-text-4xl tw-font-bold tw-text-primary-black ">
                  <p>{subProjectData?.name}</p>
                </h1>
                <ChevronRight size={30} />
              </button>
            </div>
            {/* User Info */}
            <div className="tw-italic -tw-mt-3 tw-flex tw-gap-2 tw-items-center tw-text-[#787E89]">
              <p>Created by</p>
              {subProjectData?.createdBy === "me" ? (
                subProjectData?.createdBy
              ) : (
                <div className="tw-flex  tw-gap-2 tw-items-center">
                  <div
                    className={`tw-relative tw-rounded-full tw-w-[2rem] tw-h-[2rem] ${
                      !subProjectData?.User?.image &&
                      "tw-bg-primary-purple tw-text-white "
                    }`}
                  >
                    {subProjectData?.User?.image ? (
                      <Image
                        src={subProjectData?.User?.image}
                        alt="user"
                        fill
                        className="!tw-rounded-full"
                      />
                    ) : (
                      <span className="tw-text-primary-1100  tw-font-merriweather tw-font-bold md:tw-text-base tw-uppercase tw-absolute tw-top-[50%] tw-left-[50%] tw--translate-x-[50%] tw--translate-y-[50%]">
                        {subProjectData?.User?.firstName?.charAt(0)}
                      </span>
                    )}
                  </div>
                  <div>
                    <p className="tw-font-medium !tw-text-sm tw-not-italic !tw-text-primary-black">
                      {`${subProjectData?.User?.firstName ?? ""} ${
                        subProjectData?.User?.lastName ?? ""
                      }`}
                    </p>
                  </div>
                </div>
              )}
            </div>
            <div className="tw-w-full tw-overflow-hidden tw-block tw-mt-5 tw-mb-8">
              <p className="tw-text-lg tw-font-medium tw-text-primary-black tw-italic tw-mb-2">
                Highlights
              </p>
              <Carousel
                opts={{
                  align: "start",
                  draggable: true, // Enable dragging
                  speed: 8, // Lower value = smoother
                  loop: false, // Enable infinite loop if needed
                  startIndex: 0, // Ensure it starts at the first slide
                  containScroll: "trimSnaps", // Keeps items inside the viewport
                  skipSnaps: true,
                }}
                className=""
              >
                <CarouselContent>
                  {stories?.map((story) => (
                    <CarouselItem key={`${story.key}-${story.id}`}>
                      <div
                        className="tw-relative tw-w-32 tw-cursor-pointer tw-h-40 tw-flex-shrink-0 tw-rounded-2xl tw-overflow-hidden"
                        onClick={() => {}}
                      >
                        <Image
                          src={story.img}
                          alt="story"
                          fill
                          className="tw-rounded-3xl tw-object-cover"
                        />
                      </div>
                      <p className="tw-pt-2 tw-text-primary-black tw-font-medium">
                        {story?.label}
                      </p>
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
            </div>
            {/* Tabs */}
            {subProjectData?.createdBy === "me" && (
              <div className="tw-flex tw-justify-between tw-items-center tw-my-10">
                <div className="tw-flex  tw-items-center tw-gap-4 ">
                  {subProjectsTabs?.map((ele) => (
                    <button
                      type="button"
                      onClick={() => {
                        setActiveTab(ele?.value);
                        resetDataList();
                      }}
                      key={ele?.label}
                      className={`tw-py-3 tw-cursor-pointer tw-rounded-full ${
                        ele?.value === activeTab
                          ? "tw-font-bold tw-bg-[#F6EFFE] tw-text-incenti-purple"
                          : "tw-bg-[#F5F7F8] tw-text-[#2D394A] tw-font-medium"
                      } tw-px-5`}
                    >
                      {ele?.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
            {/* <div>{activeTabRender[activeTab]}</div> */}
          </CustomContainer>
        </>
      ) : (
        <div className="tw-mt-10">
          <Empty />
        </div>
      )}
    </>
  );
};

export default SingleSubProject;
