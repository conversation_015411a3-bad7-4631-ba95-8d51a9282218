import GlobalLoader from "./GlobalLoader";

const FullScreenLoader = ({ size = 10, extraClassName = "" }) => {
  return (
    <div
      className={`tw-w-full tw-flex tw-justify-center tw-items-center -tw-left-0 tw-top-0 tw-opacity-50 tw-z-[9999] tw-h-screen tw-bg-black tw-absolute ${extraClassName}`}
    >
      <GlobalLoader color="#6D11D2" size={size} />
    </div>
  );
};

export default FullScreenLoader;
