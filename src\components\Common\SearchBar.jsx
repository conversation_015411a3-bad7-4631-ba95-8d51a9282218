import { CloseModalIcon } from "@/utils/icons";
import { SearchIcon } from "lucide-react";

const SearchBar = ({
  search = "",
  searchQuery = "",
  setSearch = () => {},
  onSearch = () => {},
  resetData = () => {},
  setSearchQuery = () => {},
  className = "",
  parentClassName = "",
  isExtraStateUsed = true,
}) => {
  return (
    <>
      <div
        className={`tw-flex tw-mt-4 tw-gap-2  tw-rounded-full tw-py-2 tw-px-4  tw-bg-[#F5F7F8] ${parentClassName}`}
      >
        <SearchIcon color="#787E89" />
        <input
          type="text"
          id="simple-search"
          className={`tw-bg-transparent tw-py-1 tw-transition-all tw-duration-500 tw-focus:outline-none tw-outline-none tw-focus:ring-0 tw-ring-0 tw-block tw-w-full tw-pe-8  tw-text-gray-500 ${className} `}
          autoComplete="off"
          placeholder="Search"
          value={isExtraStateUsed ? search : searchQuery}
          onChange={(e) => {
            onSearch(e.target.value);
          }}
        />
        {(isExtraStateUsed ? search : searchQuery) && (
          <button
            onClick={() => {
              setSearch("");
              setSearchQuery((prev) => {
                if (prev?.trim() !== "") {
                  resetData();
                }
                return "";
              });
              // setSearchQuery("");
              // resetData();
            }}
            className="tw-ml-2"
          >
            <CloseModalIcon size={16} />
          </button>
        )}
      </div>
    </>
  );
};

export default SearchBar;
