"use client";

import React, { useRef, useEffect, useState } from "react";
import { motion, useMotionValue, animate } from "framer-motion";

const ALIGNMENTS = {
  TOP_LEFT: "topLeft",
  TOP_CENTER: "topCenter",
  TOP_RIGHT: "topRight",
  CENTER_LEFT: "centerLeft",
  CENTER: "center",
  CENTER_RIGHT: "centerRight",
  BOTTOM_LEFT: "bottomLeft",
  BOTTOM_CENTER: "bottomCenter",
  BOTTOM_RIGHT: "bottomRight",
};

const AnimatedAlignPositioned = ({
  children,
  alignment = ALIGNMENTS.CENTER,
  dx = 0,
  dy = 0,
  moveByChildWidth = 0,
  moveByChildHeight = 0,
  moveByContainerWidth = 0,
  moveByContainerHeight = 0,
  moveVerticallyByChildWidth = 0,
  moveHorizontallyByChildHeight = 0,
  moveVerticallyByContainerWidth = 0,
  moveHorizontallyByContainerHeight = 0,
  childWidth,
  childHeight,
  childWidthRatio,
  childHeightRatio,
  minChildWidth,
  minChildHeight,
  maxChildWidth,
  maxChildHeight,
  minChildWidthRatio,
  minChildHeightRatio,
  maxChildWidthRatio,
  maxChildHeightRatio,
  rotateDegrees = 0,
  wins = "min",
  touch = "inside",
  duration = 0.3,
  curve = "easeInOut",
  className = "",
  style = {},
}) => {
  const containerRef = useRef(null);
  const childRef = useRef(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [childSize, setChildSize] = useState({ width: 0, height: 0 });

  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotate = useMotionValue(rotateDegrees);

  const getAlignmentOffset = (containerSize, childSize) => {
    const map = {
      topLeft: { x: 0, y: 0 },
      topCenter: { x: (containerSize.width - childSize.width) / 2, y: 0 },
      topRight: { x: containerSize.width - childSize.width, y: 0 },
      centerLeft: { x: 0, y: (containerSize.height - childSize.height) / 2 },
      center: {
        x: (containerSize.width - childSize.width) / 2,
        y: (containerSize.height - childSize.height) / 2,
      },
      centerRight: {
        x: containerSize.width - childSize.width,
        y: (containerSize.height - childSize.height) / 2,
      },
      bottomLeft: { x: 0, y: containerSize.height - childSize.height },
      bottomCenter: {
        x: (containerSize.width - childSize.width) / 2,
        y: containerSize.height - childSize.height,
      },
      bottomRight: {
        x: containerSize.width - childSize.width,
        y: containerSize.height - childSize.height,
      },
    };
    return map[alignment] || map.center;
  };

  const calculatePosition = () => {
    if (
      !containerSize.width ||
      !containerSize.height ||
      !childSize.width ||
      !childSize.height
    ) {
      return { x: 0, y: 0 };
    }

    let finalX = 0;
    let finalY = 0;
    const offset = getAlignmentOffset(containerSize, childSize);

    if (touch === "inside") {
      finalX = offset.x;
      finalY = offset.y;
    } else if (touch === "outside") {
      finalX =
        offset.x +
        (offset.x > containerSize.width / 2
          ? childSize.width
          : -childSize.width);
      finalY =
        offset.y +
        (offset.y > containerSize.height / 2
          ? childSize.height
          : -childSize.height);
    } else if (touch === "middle") {
      finalX = offset.x - childSize.width / 2;
      finalY = offset.y - childSize.height / 2;
    }

    const dxVal = Math.abs(dx) <= 1 ? dx * containerSize.width : dx;
    const dyVal = Math.abs(dy) <= 1 ? dy * containerSize.height : dy;

    finalX += dxVal;
    finalY += dyVal;

    finalX +=
      childSize.width * moveByChildWidth +
      childSize.height * moveHorizontallyByChildHeight;
    finalY +=
      childSize.height * moveByChildHeight +
      childSize.width * moveVerticallyByChildWidth;

    finalX +=
      containerSize.width * moveByContainerWidth +
      containerSize.height * moveHorizontallyByContainerHeight;
    finalY +=
      containerSize.height * moveByContainerHeight +
      containerSize.width * moveVerticallyByContainerWidth;

    return { x: finalX, y: finalY };
  };

  const calculateChildSize = () => {
    if (!containerSize.width || !containerSize.height) return {};
    const styles = {};

    let width = childWidth;
    if (childWidthRatio)
      width = (width || 0) + containerSize.width * childWidthRatio;

    let height = childHeight;
    if (childHeightRatio)
      height = (height || 0) + containerSize.height * childHeightRatio;

    if (width !== undefined) {
      let minWidth = minChildWidth;
      if (minChildWidthRatio)
        minWidth = Math.max(
          minWidth || 0,
          containerSize.width * minChildWidthRatio
        );

      let maxWidth = maxChildWidth;
      if (maxChildWidthRatio)
        maxWidth = Math.min(
          maxWidth || Infinity,
          containerSize.width * maxChildWidthRatio
        );

      if (minWidth !== undefined) width = Math.max(width, minWidth);
      if (maxWidth !== undefined) width = Math.min(width, maxWidth);

      styles.width = width;
    }

    if (height !== undefined) {
      let minHeight = minChildHeight;
      if (minChildHeightRatio)
        minHeight = Math.max(
          minHeight || 0,
          containerSize.height * minChildHeightRatio
        );

      let maxHeight = maxChildHeight;
      if (maxChildHeightRatio)
        maxHeight = Math.min(
          maxHeight || Infinity,
          containerSize.height * maxChildHeightRatio
        );

      if (minHeight !== undefined) height = Math.max(height, minHeight);
      if (maxHeight !== undefined) height = Math.min(height, maxHeight);

      styles.height = height;
    }

    return styles;
  };

  useEffect(() => {
    const updateSizes = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerSize({ width: rect.width, height: rect.height });
      }
      if (childRef.current) {
        const rect = childRef.current.getBoundingClientRect();
        setChildSize({ width: rect.width, height: rect.height });
      }
    };

    updateSizes();

    const resizeObserver = new ResizeObserver(updateSizes);
    if (containerRef.current) resizeObserver.observe(containerRef.current);
    if (childRef.current) resizeObserver.observe(childRef.current);

    return () => resizeObserver.disconnect();
  }, [children]);

  useEffect(() => {
    const pos = calculatePosition();
    animate(x, pos.x, { duration, ease: curve });
    animate(y, pos.y, { duration, ease: curve });
    animate(rotate, rotateDegrees, { duration, ease: curve });
  }, [
    dx,
    dy,
    alignment,
    containerSize,
    childSize,
    moveByChildWidth,
    moveByChildHeight,
    moveByContainerWidth,
    moveByContainerHeight,
    moveVerticallyByChildWidth,
    moveHorizontallyByChildHeight,
    moveVerticallyByContainerWidth,
    moveHorizontallyByContainerHeight,
    rotateDegrees,
    touch,
    duration,
    curve,
  ]);

  const childStyles = calculateChildSize();

  return (
    <div
      ref={containerRef}
      className={`tw-relative tw-w-full tw-h-full tw-overflow-hidden ${className}`}
      style={style}
    >
      <motion.div
        ref={childRef}
        style={{
          position: "absolute",
          x,
          y,
          rotate,
          zIndex: 9999999,
          ...childStyles,
        }}
      >
        {children}
      </motion.div>
    </div>
  );
};

export default AnimatedAlignPositioned;
