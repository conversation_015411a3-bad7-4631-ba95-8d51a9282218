"use client";
import Link from "next/link";
import { useContext, useEffect, useRef, useState } from "react";
import logo from "../../../public/images/logo/logo-primary.svg";
import * as Yup from "yup";
import Image from "next/image";
import {
  BriefCaseIcon,
  DotMenuIcon,
  FolderIcon,
  GenieChatIcon,
  HomeIcon,
  SentChatIcon,
  UserGroupIcon,
} from "@/utils/icons";
import { usePathname } from "next/navigation";
import dayjs from "dayjs";
import { Copy, X } from "lucide-react";
import { sendMessageToBot } from "@/app/action";
import useApiRequest from "../helper/hook/useApiRequest";
import { io } from "socket.io-client";
import { valContext } from "../context/ValContext";
import { handleCopy, role, routes } from "@/utils/function";
import GenieBot from "./GenieBot";
import { Modal } from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import { set } from "nprogress";
// import { CollapsedIcon, ContactIcon, DotsIcon, EditIcon, HistoryIcon, ImageIcon } from "@/utils/Icons";

let socket = null;
let userChatCount = 0;

function MessageLoader() {
  return (
    <div className="tw-bg-[#F7F7F2] tw-inline-block tw-rounded-tl-2xl tw-rounded-tr-2xl tw-rounded-br-2xl tw-p-2.5">
      {/* <span
        style={{
          width: "6px",
          height: "6px",
        }}
        className="message-loader"
      ></span> */}

      <div className="chat-loader"></div>
    </div>
  );
}

function ListItem({
  text,
  isActive,
  icon,
  label,
  path = "/",
  onClick,
  className,
}) {
  return (
    <Link href={path}>
      <li
        className={`tw-cursor-pointer tw-w-full lg:tw-px-10 tw-flex tw-items-center group tw-py-2 lg:tw-py-4 ${
          isActive ? "lg:tw-bg-incenti-purple" : "lg:hover:tw-bg-[#6d606012]"
        } `}
        onClick={() => {
          onClick();
        }}
      >
        {/* {isActive && <span className="tw-w-[0.3125rem] tw-h-[1.5rem] tw-bg-orange-600 tw-rounded-br-[0.625rem] tw-rounded-tr-[0.625rem] tw-me-[0.625rem]"></span>} */}
        <div
          className={`tw-flex tw-flex-col lg:tw-flex-row tw-items-center tw-text-sm lg:tw-gap-2  tw-px-5 tw-w-full ${
            isActive
              ? "tw-text-primary-purple lg:tw-text-white"
              : "tw-text-black"
          } ${className}`}
        >
          <div>{icon}</div>
          <p className="tw-relative tw-top-[0.5px] ">{label}</p>
        </div>
      </li>
    </Link>
  );
}

// const routes = {
//   HOME: "/",
//   HELP_ME_FIND: "/help-me-find",
//   PROJECTS: "projects",
//   SUB_PROJECT: "sub-project",
//   COMMUNITIES: "communities",
//   TO_DOS: "/to-dos",
// };

const SidebarPage = ({
  onClose,
  isDesktopSidebarOpen,
  setIsDesktopSidebarOpen,
  setSearchValue,
  setGlobalSearch,
}) => {
  const [isBotOpen, setIsBotOpen] = useState(false);
  const [firstTimeLoading, setFirstTimeLoading] = useState(true);
  const { isMobile } = useContext(valContext);
  const [chatList, setChatList] = useState([
    {
      role: role.assistant,
      content: (
        <p className="tw-font-semibold">
          Hi, I'm Genie. How can I help you build your Project journey?
        </p>
      ),
      timestamp: dayjs().toISOString(),
    },
  ]);
  const [message, setMessage] = useState(null);
  const [isDataFetching, setIsDataFetching] = useState(false);
  const [isMobileBotOpen, setIsMobileBotOpen] = useState(false);
  const chatContainerRef = useRef(null); // Ref for the chat container
  const modalRef = useRef(null);
  const pathname = usePathname();
  let chunkTimeout = useRef(null);

  // console.log(isMobile);

  const [isMessageFetching, setIsMessageFetching] = useState(false);
  const activeColor = "#fff";
  const defaultColor = "#2D394A";
  const mobileActiveColor = "#6D11D2";
  const mobileDefaultColor = "#00000000";
  const activeClass = "tw-stroke-[#6d11d2] lg:tw-stroke-[#fff]";
  const defaultClass = "tw-stroke-[#2D394A]";
  const menuList = [
    {
      id: 1,
      label: "Explore",
      // icon: <HomeIcon stroke={"#6D11D2"} fill="#6D11D2" />,
      icon: (
        <HomeIcon
          // stroke={
          //   routes.HOME === pathname
          //     ? isMobile
          //       ? mobileActiveColor
          //       : activeColor
          //     : defaultColor
          // }
          className={`${
            routes.HOME === pathname ? activeClass : defaultClass
          } `}
          fill={
            isMobile && routes.HOME === pathname
              ? mobileActiveColor
              : mobileDefaultColor
          }
        />
      ),
      isActive: pathname === routes.HOME,
      path: routes.HOME,
    },
    // {
    //     id: 2,
    //     label: "HelpMeFind",
    //     icon: <BriefCaseIcon stroke={pathname?.toString() === routes.HELP_ME_FIND ? activeColor : defaultColor} />,
    //     isActive: false,
    //     path: routes.HELP_ME_FIND
    // },
    {
      id: 3,
      label: "Projects",
      icon: (
        <FolderIcon
          size={18}
          className={`${
            routes.PROJECTS === pathname?.split("/")?.[1] ||
            routes.SUB_PROJECT === pathname?.split("/")?.[1]
              ? activeClass
              : defaultClass
          } `}
          fill={
            isMobile &&
            (routes.PROJECTS === pathname?.split("/")?.[1] ||
              routes.SUB_PROJECT === pathname?.split("/")?.[1])
              ? mobileActiveColor
              : mobileDefaultColor
          }
        />
      ),
      isActive:
        routes.PROJECTS === pathname?.split("/")?.[1] ||
        routes.SUB_PROJECT === pathname?.split("/")?.[1],
      path: `/${routes.PROJECTS}`,
    },
    {
      id: 4,
      label: "Communities",
      icon: (
        <UserGroupIcon
          className={`${
            routes.COMMUNITIES === pathname?.split("/")?.[1]
              ? activeClass
              : defaultClass
          } `}
          fill={
            isMobile && routes.COMMUNITIES === pathname?.split("/")?.[1]
              ? mobileActiveColor
              : mobileDefaultColor
          }
        />
      ),
      isActive: routes.COMMUNITIES === pathname?.split("/")?.[1],
      path: `/${routes.COMMUNITIES}`,
    },
    {
      id: 5,
      label: "To Do",
      icon: (
        <DotMenuIcon
          className={`${
            pathname?.toString()?.includes(routes.TO_DOS)
              ? activeClass
              : defaultClass
          } `}
          fill={
            isMobile && pathname?.toString()?.includes(routes.TO_DOS)
              ? mobileActiveColor
              : mobileDefaultColor
          }
        />
      ),
      isActive: pathname?.toString()?.includes(routes.TO_DOS),
      path: routes.TO_DOS,
    },
    {
      id: 6,
      label: "Genie",
      icon: (
        <GenieChatIcon
          bgFill={`${
            pathname?.toString()?.includes(routes.GENIE) ? "#fff" : "#00000000"
          } `}
        />
      ),
      isActive: pathname?.toString()?.includes(routes.GENIE),
      path: routes.GENIE,
      className: "tw-relative -tw-left-1.5",
    },
  ];

  const genieBotVisibleScreen = [
    routes.CREATED_PROJECTS,
    routes.FOLLOWED_PROJECTS,
    routes.EXPLORE_PROJECTS,
    routes.CREATED_COMMUNITIES,
    routes.JOINED_COMMUNITIES,
    routes.EXPLORE_COMMUNITIES,
    routes.TO_DOS,
    routes.PROFILE,
    `${routes.PROFILE}/${routes.PROJECTS}`,
  ];

  const textareaRef = useRef(null);

  const chatHandler = () => {
    let messageData = message?.replace(/\n/g, "");
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto"; // reset height immediately after send
    }
    // console.log("message data");
    if (!messageData || messageData === "") return;
    setIsMessageFetching(true);
    setIsDataFetching(true);
    ++userChatCount;
    let messagesList = [
      ...chatList,
      {
        role: role.user,
        content: messageData,
        timestamp: dayjs().toISOString(),
      },
    ];
    if (userChatCount % 5 === 0) {
      let subscribeMessage = (
        <div className="tw-max-w-[20.5rem]">
          <p>Help us fund our Startup Journey! 🚀</p>
          <p className="tw-my-2">
            Subscribe and be the first to unlock the premium features over our
            next update for just&nbsp;
            <span className="tw-font-semibold">$19.99/month</span>.
          </p>
          <Link
            href={"https://buy.stripe.com/3cscNCeCc2Ot04o288"}
            target="_blank"
            className="tw-text-primary-black tw-border tw-border-primary-black tw-rounded-full tw-text-xs tw-mt-2 tw-px-2 tw-py-1"
          >
            Subscribe Now
          </Link>
        </div>
      );
      messagesList = [
        ...messagesList,
        {
          role: role.assistant,
          content: subscribeMessage,
          timestamp: dayjs().toISOString(),
        },
      ];
    }

    setChatList(messagesList);
    // console.log("here");
    if (userChatCount % 5 !== 0) {
      socket.emit("sendMessage", {
        conversations: messagesList?.map((ele) => ({
          role: ele.role,
          content: ele?.content?.toString(),
        })),
      });
    } else {
      setIsMessageFetching(false);
      setIsDataFetching(false);
    }

    setMessage(null);
  };

  if (isBotOpen || isMobileBotOpen) {
    setTimeout(() => {
      setFirstTimeLoading(false);
    }, 1000);
  }

  // Fixed the outside scroll
  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer || !isBotOpen) return;

    const stopScrollPropagation = (e) => {
      const { scrollTop, scrollHeight, clientHeight } = chatContainer;

      const delta = e.deltaY;
      const up = delta < 0;
      const down = delta > 0;

      const isAtTop = scrollTop === 0;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;

      if ((up && isAtTop) || (down && isAtBottom)) {
        // If trying to scroll past the boundary
        e.preventDefault();
        e.stopPropagation();
      }
      // else allow normal scroll inside the div
    };

    chatContainer.addEventListener("wheel", stopScrollPropagation, {
      passive: false,
    });

    return () => {
      chatContainer.removeEventListener("wheel", stopScrollPropagation);
    };
  }, [isBotOpen]);

  // Scrolling
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight, // Scroll to bottom
        behavior: "smooth",
      });
    }
  }, [chatList]);

  // Close the Modal
  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsBotOpen(false);
      }
    }
    if (isBotOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isBotOpen]);

  // Socket Connection
  useEffect(() => {
    if (!socket) {
      socket = io(process.env.NEXT_PUBLIC_SOCKET_URL, {
        // path: "/api/socket",
        autoConnect: false,
      });

      socket.connect();

      socket.on("connect", () => {
        // console.log("✅ Socket connected:");
      });

      socket.on("chatChunk", (data) => {
        let newText = "";
        if (Object.keys(data).length > 0) {
          setIsMessageFetching(false);
          newText += data?.content;
        }
        if (chunkTimeout.current) clearTimeout(chunkTimeout.current);

        setChatList((prev) => {
          const lastIndex = prev.length - 1;
          const lastMessage = prev[lastIndex];

          if (lastMessage.role === role.user) {
            // Add new assistant message
            return [
              ...prev,
              {
                role: role.assistant,
                content: newText,
              },
            ];
          } else {
            // Append to last assistant message
            const updated = [...prev];
            updated[lastIndex] = {
              ...lastMessage,
              content: lastMessage.content + newText,
            };
            return updated;
          }
        });
        if (chunkTimeout.current) clearTimeout(chunkTimeout.current);

        // Start new timeout: if no chunks arrive in 500ms, loading is done
        chunkTimeout.current = setTimeout(() => {
          setIsDataFetching(false);
        }, 500);
      });

      socket.on("disconnect", () => {
        // console.log("❌ Socket disconnected");
      });

      socket.on("connect_error", (err) => {
        // console.error("⚠️ Socket connect error:", err.message);
      });
    }

    return () => {
      if (socket && socket.connected) {
        socket.disconnect();
        socket.off("chatChunk");
      }
    };
  }, []);
  return (
    <div className="tw-relative">
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[100%] !tw-w-full lg:!tw-w-[20rem] !tw-rounded-[1.25rem] !tw-z-[99999] !tw-m-0  !tw-h-[80vh] lg:!tw-h-[35rem] lg:!tw-absolute lg:!tw-left-[2.5rem] lg:!tw-bottom-[9%] ",
          overlay: "!tw-bg-[#000000CC] lg:!tw-bg-[#00000000]",
        }}
        open={isMobileBotOpen}
        onClose={() => {
          setIsMobileBotOpen(false);
          setMessage("");
        }}
        closeIcon={<></>}
        center
      >
        <GenieBot
          setIsBotOpen={setIsMobileBotOpen}
          message={message}
          setMessage={setMessage}
          firstTimeLoading={firstTimeLoading}
          chatList={chatList}
          isMessageFetching={isMessageFetching}
          chatHandler={chatHandler}
          isDataFetching={isDataFetching}
          chatContainerRef={chatContainerRef}
          role={role}
          textareaRef={textareaRef}
        />
      </Modal>
      {/* Mobile */}
      <div className="">
        {(genieBotVisibleScreen?.includes(pathname) ||
          pathname === routes.HOME) && (
          <button
            onClick={() => {
              setIsMobileBotOpen(true);
            }}
            className="tw-bg-[#E8AB2E] tw-rounded-full lg:tw-hidden tw-p-[.8rem] tw-fixed tw-bottom-16 tw-right-4  tw-justify-center tw-items-center tw-z-50"
            type="button"
          >
            <GenieChatIcon bgFill="#FFFFFFE5" size={30} />
          </button>
        )}
        <div className="lg:tw-hidden tw-fixed tw-bottom-0 tw-w-full tw-z-[99] tw-shadow-2xl">
          <ul className="tw-w-full tw-flex  tw-items-center tw-justify-evenly  tw-bg-white tw-z-30 tw-shadow-2xl">
            {menuList
              ?.filter((ele) => ele?.label !== "Genie")
              ?.map((el) => (
                <ListItem
                  key={el.id}
                  path={el.path}
                  label={el.label}
                  icon={el.icon}
                  isActive={el.isActive}
                  onClick={() => {
                    setTimeout(() => {
                      setSearchValue("");
                      setGlobalSearch("");
                    }, 1000);
                  }}
                />
              ))}
          </ul>
        </div>
      </div>
      {/* Desktop View */}
      <div
        className={`tw-bg-primary-1100 tw-hidden lg:tw-flex tw-flex-col tw-justify-between lg:tw-sticky tw-top-0 tw-text-white tw-h-screen tw-transition-all tw-duration-300 tw-w-[16.75rem]`}
      >
        <div>
          <div className="tw-py-6 tw-px-4 tw-flex tw-items-center tw-justify-center">
            <div className="tw-relative tw-w-[7.5rem] tw-h-[2.8rem]">
              <Image
                src={logo} // Replace with actual illustration path
                alt="logo"
                fill
                className=""
              />
            </div>
          </div>

          {/* List Sections */}
          <div className="tw-space-y-6 tw-my-5 tw-flex-1 tw-overflow-y-auto custom-scrollbar ">
            {/* Section: Today */}
            <div>
              <ul className="tw-space-y-[0.625rem]  tw-mb-0">
                {menuList?.map((el) => (
                  <ListItem
                    key={el.id}
                    path={el.path}
                    label={el.label}
                    icon={el.icon}
                    isActive={el.isActive}
                    className={el?.className ?? ""}
                    onClick={() => {
                      setTimeout(() => {
                        setSearchValue("");
                        setGlobalSearch("");
                      }, 1000);
                    }}
                  />
                ))}
              </ul>
            </div>
          </div>
        </div>
        {/* {isBotOpen && (
          <div
            ref={modalRef}
            className="tw-bg-white tw-absolute tw-bottom-16 tw-left-[10%] tw-z-[999] tw-h-[35rem] tw-w-[20rem] tw-shadow-chatbot tw-rounded-3xl tw-px-4 tw-py-6 !tw-text-primary-black"
          >
            <GenieBot
              setIsBotOpen={setIsBotOpen}
              message={message}
              setMessage={setMessage}
              firstTimeLoading={firstTimeLoading}
              chatList={chatList}
              isMessageFetching={isMessageFetching}
              chatHandler={chatHandler}
              isDataFetching={isDataFetching}
              chatContainerRef={chatContainerRef}
              role={role}
            />
          </div>
        )} */}
        {/* <div className="tw-absolute tw-bottom-20 tw-left-[28%]">
          <button
            onClick={() => {
              // setIsBotOpen((prev) => !prev);
              setIsMobileBotOpen(true);
            }}
            className="tw-flex tw-rounded-full tw-px-5 tw-py-3 tw-bg-[#E8AB2E] tw-gap-3 tw-items-center  "
          >
            <div>
              <GenieChatIcon bgFill="#fff" />
            </div>
            <p>Genie</p>
          </button>
        </div> */}
        <div className="tw-text-[#787E89] tw-font-light tw-text-center tw-relative -tw-top-5">
          &copy;{dayjs().format("YYYY")}&nbsp;Incenti
        </div>
      </div>
    </div>
  );
};

export default SidebarPage;
