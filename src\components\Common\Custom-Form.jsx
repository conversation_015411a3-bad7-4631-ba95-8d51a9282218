"use client";
import { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import CustomButton from "./Custom-Button";
import { Eye, EyeOff } from "lucide-react";
import CustomPasswordVal from "./Custom-Password-Val";
import { EditPencileIcon, UserProfileIcon } from "@/utils/icons";
import Image from "next/image";
import toast from "react-hot-toast";
import { safeToast } from "@/utils/safeToast";

const GlobalForm = ({
  fields,
  initialValues,
  validationSchema,
  onSubmit,
  submitButtonText = "Submit",
  submitType = 1,
  extraButton = null,
  loading = false,
  setLoading = () => {},
  error = null,
  setError = () => {},
}) => {
  const [isShowPass, setIsShowPass] = useState({});
  const [profileImage, setProfileImage] = useState(null);

  const formik = useFormik({
    initialValues,
    validationSchema: Yup.object(validationSchema),
    onSubmit,
  });

  // console.log(formik.errors);
  const handleImageUpload = (e, fieldName) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }
    // const fileSize = e.target.files[0].size / (1024 * 1024);
    // if (fileSize > 2) {
    //   toast.error("The file size exceeds 2 MB. Please upload a smaller file.");
    //   return;
    // }
    const file = e.currentTarget.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result);
        formik.setFieldValue(fieldName, file);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <form onSubmit={formik.handleSubmit} className="tw-space-y-4">
      {fields.map(
        ({
          name,
          label,
          placeholder,
          type,
          options,
          maxLengthTextArea = null,
          required = false,
        }) => (
          <div key={name} className="tw-flex tw-flex-col">
            {type === "password" ? (
              <div className="tw-relative tw-w-[22rem] lg:tw-w-[28rem]">
                <input
                  type={isShowPass[name] ? "text" : "password"}
                  name={name}
                  id={name}
                  // placeholder={placeholder || label}
                  autoComplete="off"
                  onChange={(e) => {
                    setError(null);
                    formik.handleChange(e);
                  }}
                  onBlur={(e) => {
                    setError(null);
                    formik.handleBlur(e);
                  }}
                  value={formik.values[name]}
                  className={`tw-border ${
                    error ? "tw-border-red-500" : "tw-border-transparent"
                  } tw-peer tw-bg-[#F1F2F3] tw-rounded-[14px] tw-pt-6 tw-pb-4 tw-ps-5 tw-pe-10 tw-outline-none tw-w-[100%] tw-text-primary-black tw-font-semibold`}
                />
                <button
                  type="button"
                  className="tw-absolute tw-transition tw-right-3 tw-top-[1.4rem] tw-text-gray-600"
                  onClick={() =>
                    setIsShowPass((prev) => ({
                      ...prev,
                      [name]: !prev[name],
                    }))
                  }
                >
                  {!isShowPass[name] ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
                <label
                  htmlFor={name}
                  className={`tw-absolute tw-left-5 tw-top-5 tw-text-lg tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none 
                  peer-placeholder-shown:tw-top-6 peer-placeholder-shown:tw-text-lg 
                  ${
                    formik.values[name]
                      ? "!tw-top-2 tw-text-xs"
                      : "peer-focus:tw-top-2 peer-focus:tw-text-xs"
                  }   `}
                >
                  {label}
                  {required && "*"}
                </label>
              </div>
            ) : type === "passwordWithVal" ? (
              <CustomPasswordVal
                name={name}
                placeholder={placeholder}
                label={label}
                Field="input"
                setFieldValue={formik.setFieldValue}
                formik={formik}
                required={required}
              />
            ) : type === "select" ? (
              <select
                name={name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values[name]}
                className="tw-border tw-rounded tw-p-2 tw-w-full"
              >
                <option value="">Select an option</option>
                {options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            ) : type === "radio" ? (
              options.map((option) => (
                <label
                  key={option.value}
                  className="tw-flex tw-items-center tw-space-x-2"
                >
                  <input
                    type="radio"
                    name={name}
                    value={option.value}
                    checked={formik.values[name] === option.value}
                    onChange={formik.handleChange}
                  />
                  <span>{option.label}</span>
                </label>
              ))
            ) : type === "checkbox" ? (
              options.map((option) => (
                <label
                  key={option.value}
                  className="tw-flex tw-items-center tw-space-x-2"
                >
                  <input
                    type="checkbox"
                    name={name}
                    value={option.value}
                    checked={formik.values[name]?.includes(option.value)}
                    onChange={formik.handleChange}
                  />
                  <span>{option.label}</span>
                </label>
              ))
            ) : type === "file" ? (
              <input
                type="file"
                name={name}
                onChange={(e) => {
                  console.log(e);
                  if (
                    !e.currentTarget.files[0] ||
                    !e.currentTarget.files[0].type.startsWith("image/")
                  ) {
                    safeToast.error(
                      "Unsupported file format. Please select an image file."
                    );
                    return;
                  }
                  // const fileSize =
                  //   e.currentTarget.files[0].size / (1024 * 1024);
                  // if (fileSize > 2) {
                  //   toast.error(
                  //     "The file size exceeds 2 MB. Please upload a smaller file."
                  //   );
                  //   return;
                  // }

                  formik.setFieldValue(name, e.currentTarget.files[0]);
                }}
                className="tw-border tw-rounded tw-p-2 tw-w-full"
              />
            ) : type === "textarea" ? (
              <>
                <div className="tw-relative">
                  <textarea
                    name={name}
                    id={name}
                    // placeholder={placeholder || label}
                    maxLength={maxLengthTextArea ?? 250}
                    rows={6}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values[name]}
                    className="tw-peer tw-bg-[#F1F2F3] tw-resize-none tw-rounded-[14px] tw-pt-6 tw-pb-4 tw-ps-5 tw-pe-10 tw-outline-none tw-w-[22rem] lg:tw-w-[28rem] tw-text-primary-black tw-font-semibold"
                  />
                  <label
                    htmlFor={name}
                    className={`tw-absolute tw-left-5 tw-top-5 tw-text-lg tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none 
                  peer-placeholder-shown:tw-top-6 peer-placeholder-shown:tw-text-lg 
                  ${
                    formik.values[name]
                      ? "!tw-top-2 tw-text-xs"
                      : "peer-focus:tw-top-2 peer-focus:tw-text-xs"
                  }   `}
                  >
                    {label}
                  </label>
                  <div className="tw-flex tw-justify-end tw-mb-4">
                    <p className="tw-text-sm tw-text-[#787E89] ">
                      {formik.values[name]?.length ?? 0}/250
                    </p>
                  </div>
                </div>
              </>
            ) : type === "profileImage" ? (
              <div className="tw-flex tw-flex-col tw-items-center">
                {profileImage ? (
                  <div className="tw-relative tw-rounded-full tw-h-36 tw-w-36">
                    <Image
                      width={144}
                      height={144}
                      src={profileImage}
                      alt="Profile"
                      className="tw-rounded-full tw-h-36 tw-w-36 tw-object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const fileInput = document.getElementById(name);
                        if (fileInput) fileInput.click();
                      }}
                      className="tw-absolute tw-bottom-2 tw-right-2 tw-bg-incenti-purple tw-text-white tw-rounded-full tw-w-8 tw-h-8 tw-flex tw-items-center tw-justify-center tw-shadow-md"
                    >
                      <EditPencileIcon width={16} height={16} />
                    </button>
                  </div>
                ) : (
                  <label
                    htmlFor={name}
                    className="tw-flex tw-justify-center tw-items-center tw-rounded-full tw-h-36 tw-w-36 tw-cursor-pointer tw-bg-[#F6EFFE] tw-p-4 tw-text-center tw-border-[1.17px] tw-border-dashed tw-border-[#6D11D2]"
                  >
                    <div className="tw-flex tw-items-center tw-flex-col tw-gap-1">
                      <UserProfileIcon />
                      <span className="tw-text-gray-600 tw-text-sm">
                        Upload
                      </span>
                    </div>
                  </label>
                )}
                <input
                  type="file"
                  id={name}
                  name={name}
                  onChange={(event) => handleImageUpload(event, name)}
                  className="tw-hidden"
                  accept="image/*"
                />
              </div>
            ) : (
              <div className="tw-relative ">
                <input
                  type={type}
                  name={name}
                  id={name}
                  // placeholder={placeholder || label}
                  autoComplete="off"
                  onChange={(e) => {
                    setError(null);
                    formik.handleChange(e);
                  }}
                  onBlur={(e) => {
                    setError(null);
                    formik.handleBlur(e);
                  }}
                  value={formik.values[name]}
                  className={`tw-border ${
                    error ||
                    (type !== "passwordWithVal" &&
                      formik.touched[name] &&
                      formik.errors[name])
                      ? "tw-border-red-500"
                      : "tw-border-transparent"
                  } tw-peer tw-bg-[#F1F2F3] tw-rounded-[14px] tw-pt-6 tw-pb-4 tw-ps-5 tw-pe-10 tw-outline-none tw-w-[22rem] lg:tw-w-[28rem] tw-text-primary-black tw-font-semibold`}
                />
                <label
                  htmlFor={name}
                  className={`tw-absolute tw-left-5 tw-top-5 tw-text-lg tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none 
                  peer-placeholder-shown:tw-top-6 peer-placeholder-shown:tw-text-lg 
                  ${
                    formik.values[name]
                      ? "!tw-top-2 tw-text-xs"
                      : "peer-focus:tw-top-2 peer-focus:tw-text-xs"
                  }   `}
                >
                  {label}
                  {required && "*"}
                </label>
              </div>
            )}

            {/* Error Message */}
            {type !== "passwordWithVal" &&
              formik.touched[name] &&
              formik.errors[name] && (
                <div className="tw-text-red-500 tw-text-sm tw-space-x-2 tw-mt-1.5 tw-mx-4">
                  {formik.errors[name]}
                </div>
              )}
          </div>
        )
      )}

      {extraButton && extraButton}

      <div className="tw-w-full tw-flex tw-justify-center tw-items-center tw-my-0">
        <CustomButton loading={loading} type="submit" count={submitType}>
          {submitButtonText}
        </CustomButton>
      </div>
    </form>
  );
};

export default GlobalForm;
