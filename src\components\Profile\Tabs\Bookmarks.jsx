"use client";
import {
  bookMark<PERSON>ist,
  followUser,
  getCommentList,
  pinPost,
  unFollowUser,
  updatePostLike,
  updatePostWishList,
} from "@/app/action";
import { CustomGrid } from "@/components/Common/Custom-Display";
import Empty from "@/components/Common/Empty";
import InfiniteScroll from "@/components/Common/InfiniteScroll";
import { useProfile } from "@/components/context/ProfileContext";
import useApiRequest from "@/components/helper/hook/useApiRequest";
import PostCard from "@/components/HomePage/PostCard";
import ExplorePostSkeleton from "@/components/Loader/ExplorePostSkeleton";
import authStorage from "@/utils/API/AuthStorage";
import { RESPONSE_STATUS } from "@/utils/function";
import { safeToast } from "@/utils/safeToast";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

const Bookmarks = ({ origin }) => {
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [userData, setUserData] = useState(null);
  const [loadOff, setLoadOff] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const api = useApiRequest();

  const { profile } = useProfile();
  // Update Follow Handler
  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      console.log(error);
      safeToast.error(error?.message);
    }
  };

  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Update Like
  const updateLike = async (id) => {
    const res = await updatePostLike(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  // Update Bookmark
  const updateWishList = async (id) => {
    const res = await updatePostWishList(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  // Get Data
  const delayedSearch = async () => {
    let query = {
      page: pagination.page,
      limit: pagination.limit,
    };

    const user = authStorage.getProfileDetails();
    setUserData(user);
    if (Object.keys(query)?.length > 0) {
      query.sortOrder = "DESC";
    }

    try {
      api.sendRequest(
        bookMarkList,
        (res) => {
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
          const items = res?.data?.data?.map((ele) => ({
            mainId: ele?.id,
            ...ele?.Post,
          }));
          if (res?.data?.data?.length && pagination?.page > 1) {
            setDataList((prev) => [...prev, ...items]);
            setLoadOff(false);
          } else if (res?.data?.data?.length && pagination.page === 1) {
            setDataList(() => [...items]);
            setLoadOff(false);
          } else {
            setLoadOff(true);
          }
        },

        { ...query }
      );
    } catch (error) {
      console.error("Error fetching blog data:", error);
      // setScrollLoader(false);
    } finally {
      // setScrollLoader(false);
    }
    // setScrollLoader(false);
  };

  useEffect(() => {
    const fetchData = async () => {
      await delayedSearch();
    };

    if (profile?.id) {
      fetchData();
    }
  }, [pagination.page, profile, refresh]);
  return (
    <div>
      <>
        <CustomGrid
          data={dataList}
          className="tw-gap-4 sm:tw-gap-8 tw-pb-5"
          Component={({ data: dataProps }) => {
            return PostCard({
              data: { ...dataProps },
              userData: userData,
              refetchData: resetDataList,
              isUserLinkActive: true,
              setRefresh: setRefresh,
              origin: origin,
              getLikeData: async (id) => {
                updateLike(id);
              },
              getWishlistData: async (id) => {
                updateWishList(id);
              },
              getCommentData: async (
                id,
                setCommentList,
                setIsCommentLoading
              ) => {
                try {
                  const response = await getCommentList(id);
                  if (response?.status === RESPONSE_STATUS.SUCCESS) {
                    setCommentList(response?.data?.data);
                  } else {
                    throw response;
                  }
                } catch (error) {
                  console.log(error, "error");
                  safeToast.error(error?.message);
                } finally {
                  setIsCommentLoading(false);
                }
              },
              followAndUnfollowHandler: (userId, isFollow) => {
                followAndUnfollowHandler(userId, isFollow);
              },
              pinPostHandler: async (id, setState) => {
                try {
                  const response = await pinPost(id);

                  if (
                    response?.status !== 200 &&
                    response?.status !== RESPONSE_STATUS.SUCCESS
                  ) {
                    throw response;
                  }
                  // safeToast.success(response?.message);
                  resetDataList();
                  setRefresh((prev) => !prev);
                } catch (error) {
                  safeToast.error(error?.message);

                  return;
                }
              },
            });
          }}
          xs={1}
          sm={1}
          md={1}
          lg={1}
          xl={1}
        />
        <InfiniteScroll
          threshold={90}
          loadMoreFunction={() => {
            if (
              Math.ceil(pagination.total / pagination.limit) > pagination.page
            ) {
              setPagination((prev) => ({
                ...prev,
                page: prev?.page + 1,
              }));
            }
          }}
          isLoading={api.isLoading}
          loadingComponent={<ExplorePostSkeleton />}
          timeout={10}
          loadOff={loadOff}
        />
      </>
      {!api.isLoading && dataList?.length === 0 && (
        <div className="tw-flex tw-h-[25rem] tw-justify-center tw-items-center ">
          <Empty iconRequired={false} label="No Bookmark available!" />
        </div>
      )}
    </div>
  );
};

export default Bookmarks;
