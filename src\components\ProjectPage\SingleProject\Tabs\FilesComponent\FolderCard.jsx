import ShowMenuList from "@/components/Common/ShowMenuList";
import { EditProfileIcon, ThreeDotMenuIcon } from "@/utils/icons";
import { FolderClosed, Trash2 } from "lucide-react";

const FolderCard = ({
  ele,
  editHandler = () => {},
  deleteHandler = () => {},
  showFileCount = true,
  otherLabel = null,
  showAllSettings = true,
  isOnlyEditSetting = false,
}) => {
  const folderSetting = [
    {
      label: "Rename",
      className: "",
      icon: <EditProfileIcon size={17} stroke="#2D394A" />,
      onClick: async (ele) => {
        editHandler(ele);
      },
    },

    {
      label: "Delete",
      className: "tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        deleteHandler(ele);
      },
    },
  ];
  return (
    <>
      <div className="tw-relative tw-bg-[#F6F6F6] tw-px-4 tw-py-12 tw-rounded-3xl tw-flex tw-justify-center tw-items-center">
        <FolderClosed strokeWidth={1.25} size={80} />
        {(isOnlyEditSetting || showAllSettings) && (
          <ShowMenuList
            data={ele}
            menuList={
              showAllSettings
                ? folderSetting
                : isOnlyEditSetting
                ? folderSetting?.filter((ele) => ele.label === "Rename")
                : []
            }
          >
            <button
              type="button"
              className="tw-cursor-pointer tw-flex tw-z-40 tw-gap-3 tw-items-center tw-absolute tw-right-7 tw-top-[1rem]"
            >
              <div className="">
                <ThreeDotMenuIcon fill="#111" size={24} />
              </div>
            </button>
          </ShowMenuList>
        )}
      </div>
      <div className="tw-mt-2">
        <p className="tw-text-primary-black tw-text-xl tw-font-bold tw-break-all tw-line-clamp-2">
          {ele?.name}
        </p>
        {showFileCount && (
          <p className="tw-text-primary-black">{ele?.filesCount} Files</p>
        )}
        {otherLabel && (
          <p className="tw-text-primary-black tw-text-sm">{otherLabel} </p>
        )}
      </div>
    </>
  );
};

export default FolderCard;
